# 烟叶综合杂色检测详细步骤计划

## 📋 项目概述

### 🎯 目标
将6个成熟的烟叶检测模块集成到一个综合检测程序中，实现GPU流水线并行处理，提供统一的检测接口和可视化功能。

### 📦 涉及模块
1. **焦点浮青等检测** - `seg_det_jiaodian/deploy_seg_det_condition_onnx_gpu_final.py`
2. **横纹检测** - `seg_det_hengwen/deploy_seg_det_hengwen_onnx_gpu_final.py`
3. **烤红检测** - `seg_det_kaohong/deploy_seg_det_kaohong_onnx_gpu_final.py`
4. **挂灰检测** - `seg_det_kaohong/deploy_seg_det_guahui_onnx_gpu_final.py`
5. **皱缩检测** - `seg_det_zhousuo/deploy_seg_det_zhousuo_onnx_gpu_final.py`
6. **潮红检测** - `seg_det_chaohong/deploy_seg_det_chaohong_onnx_gpu_final.py`

### 🎯 输出目标
- **主程序**: `comprehensive_det_onnx_gpu.py`
- **可视化程序**: `vis_comprehensive_det_onnx_gpu.py`
- **测试数据**: `/home/<USER>/xm/code/coderafactor/test_images/` 前5张图像
- **输出目录**: `/home/<USER>/xm/code/coderafactor/test_data/test_output/`

---

## 🔧 详细实施步骤

### 第一阶段：架构设计与分析

#### 步骤1.1：分析现有模块架构
- [ ] **分析各模块的流水线架构**
  - 查看每个模块的PipelineProcessor类结构
  - 分析GPU内存使用模式
  - 了解队列系统和线程配置
  - 记录模型文件路径和输入输出格式

- [ ] **评估GPU资源需求**
  - 分析每个模型的显存占用
  - 计算24G显存的合理分配方案
  - 设计模块间的资源隔离策略
  - 确定并行执行的可行性

#### 步骤1.2：设计综合架构
- [ ] **设计统一的数据流**
  - 定义综合PipelineData结构
  - 设计模块间的数据传递机制
  - 规划结果合并策略
  - 设计错误处理和恢复机制

- [ ] **设计GPU资源管理**
  - 创建统一的GPU内存池管理器
  - 设计模块间的GPU资源调度
  - 规划模型加载和卸载策略
  - 设计显存监控和优化机制

### 第二阶段：核心组件开发

#### 步骤2.1：创建综合检测管理器
- [ ] **ComprehensiveDetectionManager类**
  ```python
  class ComprehensiveDetectionManager:
      def __init__(self):
          # 初始化6个检测模块
          # 配置GPU资源分配
          # 设置统一的处理参数
  ```
  - 管理6个检测模块的生命周期
  - 协调GPU资源分配
  - 提供统一的检测接口

#### 步骤2.2：创建统一的流水线处理器
- [ ] **UnifiedPipelineProcessor类**
  ```python
  class UnifiedPipelineProcessor:
      def __init__(self, detection_manager):
          # 初始化统一的队列系统
          # 配置工作线程
          # 设置结果合并逻辑
  ```
  - 协调6个模块的并行执行
  - 管理统一的队列系统
  - 实现结果合并和冲突解决

#### 步骤2.3：创建结果合并器
- [ ] **ResultMerger类**
  ```python
  class ResultMerger:
      def merge_detection_results(self, results_dict):
          # 合并6个模块的检测结果
          # 处理重叠区域和冲突
          # 生成统一的JSON格式
  ```
  - 合并不同模块的检测结果
  - 处理标签冲突和重叠
  - 生成标准化的输出格式

### 第三阶段：主程序开发

#### 步骤3.1：开发comprehensive_det_onnx_gpu.py
- [ ] **主程序结构**
  ```python
  def main():
      # 1. 初始化综合检测管理器
      # 2. 加载6个检测模块
      # 3. 配置GPU资源分配
      # 4. 处理测试图像
      # 5. 合并和保存结果
  ```

- [ ] **关键功能实现**
  - 模块初始化和资源分配
  - 图像批量处理
  - 结果合并和保存
  - 性能监控和统计
  - 错误处理和日志记录

#### 步骤3.2：GPU资源优化配置
- [ ] **显存分配策略**
  - 焦点浮青检测：4GB (复杂模型)
  - 横纹检测：3GB (分割模型)
  - 烤红检测：3GB (YOLO模型)
  - 挂灰检测：3GB (YOLO模型)
  - 皱缩检测：4GB (分割模型)
  - 潮红检测：3GB (YOLO模型)
  - 系统缓冲：4GB

- [ ] **并行执行策略**
  - 轻量级模块并行执行
  - 重量级模块串行执行
  - 动态资源调度
  - 内存池共享优化

### 第四阶段：可视化程序开发

#### 步骤4.1：开发vis_comprehensive_det_onnx_gpu.py
- [ ] **可视化功能**
  ```python
  class ComprehensiveVisualizer:
      def visualize_detection_results(self, image_path, json_path):
          # 1. 加载图像和检测结果
          # 2. 绘制不同类型的检测框/轮廓
          # 3. 使用不同颜色区分模块
          # 4. 添加标签和置信度信息
          # 5. 保存可视化结果
  ```

- [ ] **可视化设计**
  - 不同模块使用不同颜色
  - 清晰的标签和图例
  - 支持多种输出格式
  - 批量可视化处理

#### 步骤4.2：可视化配置
- [ ] **颜色方案**
  - 焦点浮青：蓝色系
  - 横纹：绿色系
  - 烤红：红色系
  - 挂灰：灰色系
  - 皱缩：紫色系
  - 潮红：橙色系

### 第五阶段：测试与优化

#### 步骤5.1：功能测试
- [ ] **单模块测试**
  - 验证每个模块独立运行
  - 检查GPU资源使用
  - 确认输出格式正确

- [ ] **集成测试**
  - 测试6个模块并行执行
  - 验证结果合并正确性
  - 检查内存泄漏和资源冲突

#### 步骤5.2：性能优化
- [ ] **GPU资源优化**
  - 监控显存使用情况
  - 优化模型加载策略
  - 调整并行执行参数

- [ ] **处理速度优化**
  - 优化队列大小和线程数
  - 实现智能负载均衡
  - 减少不必要的数据拷贝

#### 步骤5.3：最终验证测试
- [ ] **运行完整测试**
  ```bash
  source ~/.bashrc && conda activate vllm && python comprehensive_det_onnx_gpu.py
  ```
  - 处理前5张测试图像
  - 验证所有模块正常工作
  - 检查输出结果完整性
  - 确认可视化效果

---

## 📊 预期输出结果

### 🎯 检测结果
- **合并JSON文件**: 包含6个模块的所有检测结果
- **统计信息**: 每个模块的检测数量和处理时间
- **性能报告**: GPU使用率、处理速度、并行效率

### 🖼️ 可视化结果
- **标注图像**: 显示所有检测结果的可视化图像
- **分类统计**: 各类缺陷的数量和分布
- **质量报告**: 检测质量和置信度分析

---

## ⚠️ 风险评估与应对

### 🚨 潜在风险
1. **GPU显存不足**: 24G可能无法同时加载所有模型
2. **模块冲突**: 不同模块可能存在资源竞争
3. **性能瓶颈**: 并行执行可能不如预期
4. **结果冲突**: 不同模块检测结果可能重叠

### 🛡️ 应对策略
1. **动态资源管理**: 实现模型的动态加载和卸载
2. **资源隔离**: 使用独立的GPU上下文和内存池
3. **智能调度**: 根据模型复杂度安排执行顺序
4. **冲突解决**: 实现智能的结果合并和去重算法

---

## 📈 成功标准

### ✅ 功能标准
- [ ] 6个模块全部成功集成
- [ ] GPU资源合理分配，无冲突
- [ ] 检测结果准确，与单独运行一致
- [ ] 可视化效果清晰，信息完整

### ✅ 性能标准
- [ ] 处理速度不低于单模块平均速度的70%
- [ ] GPU显存使用率控制在90%以内
- [ ] 系统稳定运行，无内存泄漏
- [ ] 并行效率达到预期目标

### ✅ 质量标准
- [ ] 代码结构清晰，遵循设计原则
- [ ] 错误处理完善，日志信息详细
- [ ] 文档完整，易于维护和扩展
- [ ] 测试覆盖全面，结果可重现

---

## 🎯 项目里程碑

1. **Week 1**: 完成架构设计和分析 ✅
2. **Week 2**: 完成核心组件开发 🔄
3. **Week 3**: 完成主程序和可视化开发 ⏳
4. **Week 4**: 完成测试和优化 ⏳
5. **Week 5**: 最终验证和交付 ⏳

---

*本计划将确保烟叶综合杂色检测系统的成功开发和部署，实现高效、准确、稳定的多模块并行检测能力。*
