#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶特征汇总部署程序 - 优化版本
基于Clean Code原则和SOLID设计模式的高性能实现
集成多线程处理、智能缓存、内存管理等优化技术
"""

import os
import sys
import json
import traceback
import time
import logging
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import List, Tuple, Dict, Optional, Any, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from functools import lru_cache, wraps
import weakref
import gc
import hashlib
from tqdm import tqdm

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    # print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

# ==================== 常量定义 ====================

# 烟叶特征类别配置
DEFAULT_CLASS_ID_DICTS = [
    'yanye_fengji_importance_class_id_dict',
    'yanye_fengji_rect_class_id_dict_100',
    'yanye_fengji_rect_class_id_dict_60',
    'yanye_fengji_rect_class_id_dict_250',
    'yanye_fengji_rect_class_id_dict_250_chaohong',
    'yanye_fengji_rect_class_id_dict_250_zhousuo'
]

# 线条标签配置
LINE_LABELS = ["zhimai", "zheheng", "hengwen", "zhimaiqing"]

# ==================== 配置管理类 ====================

@dataclass
class OptimizedSumYanyeConfig:
    """优化烟叶特征汇总配置类 - 遵循单一职责原则"""
    # 性能优化参数
    enable_parallel: bool = True
    max_workers: Optional[int] = None
    disable_memory_limit: bool = True
    
    # 缓存配置
    enable_cache: bool = True
    feature_cache_size: int = 5000
    geometry_cache_size: int = 3000
    
    # I/O配置
    batch_size: int = 20
    enable_batch_processing: bool = True
    
    # 特征计算配置
    enable_vectorized_computation: bool = True
    enable_aggressive_optimization: bool = True
    
    def __post_init__(self):
        if self.max_workers is None:
            # 根据CPU核心数动态调整
            cpu_count = mp.cpu_count()
            if cpu_count <= 4:
                self.max_workers = cpu_count
            elif cpu_count <= 8:
                self.max_workers = cpu_count - 1
            else:
                self.max_workers = min(cpu_count - 2, 16)

    def should_use_parallel(self, file_count: int) -> bool:
        """根据文件数量决定是否使用并行处理"""
        # 小数据集使用串行处理，避免并行开销
        return file_count >= 10 and self.enable_parallel

    def should_use_cache(self, file_count: int) -> bool:
        """根据文件数量决定是否使用缓存"""
        # 小数据集不使用缓存，避免缓存开销
        return file_count >= 20 and self.enable_cache

# ==================== 异常处理类 ====================

class SumYanyeProcessingError(Exception):
    """烟叶特征汇总处理错误基类"""
    pass

class FeatureExtractionError(SumYanyeProcessingError):
    """特征提取错误"""
    pass

class ClassificationError(SumYanyeProcessingError):
    """分类错误"""
    pass

# ==================== 性能监控装饰器 ====================

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            logging.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}s")
            return result
        except Exception as e:
            end_time = time.time()
            logging.error(f"{func.__name__} 执行失败 (耗时: {end_time - start_time:.4f}s): {e}")
            raise
    return wrapper

def safe_execute(max_retries: int = 2):
    """安全执行装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        logging.error(f"{func.__name__} 最终失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        raise
                    logging.warning(f"{func.__name__} 尝试 {attempt + 1} 失败: {e}, 重试中...")
                    time.sleep(0.1 * (attempt + 1))
            return None
        return wrapper
    return decorator

# ==================== 内存管理类 ====================

class MemoryPool:
    """高性能内存池管理器"""
    
    def __init__(self, disable_limit: bool = True):
        self.disable_limit = disable_limit
        self.arrays = {}
        self.current_size = 0
        self._lock = threading.Lock()
        self._access_count = {}
    
    def get_dict(self, key: str) -> Dict:
        """获取字典对象"""
        if self.disable_limit:
            return {}
        
        with self._lock:
            if key not in self.arrays:
                self.arrays[key] = {}
                self._access_count[key] = 0
            
            self._access_count[key] += 1
            return self.arrays[key].copy()
    
    def clear(self):
        """清空内存池"""
        if not self.disable_limit:
            with self._lock:
                self.arrays.clear()
                self.current_size = 0
                self._access_count.clear()
                gc.collect()

# ==================== 缓存管理类 ====================

class ThreadSafeCache:
    """高性能线程安全缓存类"""
    
    def __init__(self, max_size: int = 5000):
        self.cache = {}
        self.max_size = max_size
        self._lock = threading.Lock()
        self.access_count = {}
        self.hit_count = 0
        self.miss_count = 0
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key in self.cache:
                self.access_count[key] = self.access_count.get(key, 0) + 1
                self.hit_count += 1
                return self.cache[key]
            self.miss_count += 1
            return None
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self._lock:
            if len(self.cache) >= self.max_size:
                self._evict_batch()
            
            self.cache[key] = value
            self.access_count[key] = 1
    
    def _evict_batch(self):
        """批量淘汰缓存项"""
        if not self.cache:
            return
        
        evict_count = max(1, len(self.cache) // 4)
        sorted_items = sorted(self.access_count.items(), key=lambda x: x[1])
        
        for key, _ in sorted_items[:evict_count]:
            if key in self.cache:
                del self.cache[key]
                del self.access_count[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_rate": hit_rate,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count
            }
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_count.clear()
            self.hit_count = 0
            self.miss_count = 0

# 全局实例
memory_pool = MemoryPool(disable_limit=True)
feature_cache = ThreadSafeCache(max_size=5000)
geometry_cache = ThreadSafeCache(max_size=3000)


# ==================== 优化的类别字典管理器 ====================

class OptimizedClassDictManager:
    """优化的类别字典管理器 - 缓存和向量化优化"""

    def __init__(self, config: OptimizedSumYanyeConfig):
        self.config = config
        self._class_id_dict = None
        self._dict_lock = threading.Lock()

    @performance_monitor
    def get_class_id_dict(self) -> Dict[str, Any]:
        """获取合并后的类别字典"""
        if self._class_id_dict is None:
            with self._dict_lock:
                if self._class_id_dict is None:
                    self._class_id_dict = self._build_class_id_dict()

        return self._class_id_dict.copy()

    def _build_class_id_dict(self) -> Dict[str, Any]:
        """构建类别字典"""
        # 获取基础字典
        class_id_dict = getattr(bf, 'yanye_fengji_importance_class_id_dict', {}).copy()

        # 合并其他字典
        dict_names = [
            'yanye_fengji_rect_class_id_dict_100',
            'yanye_fengji_rect_class_id_dict_60',
            'yanye_fengji_rect_class_id_dict_250',
            'yanye_fengji_rect_class_id_dict_250_chaohong',
            'yanye_fengji_rect_class_id_dict_250_zhousuo'
        ]

        for dict_name in dict_names:
            if hasattr(bf, dict_name):
                source_dict = getattr(bf, dict_name, {})
                class_id_dict = bf.dict_merge(class_id_dict, source_dict)

        # 移除background
        if bf.dict_haskey(class_id_dict, "background"):
            class_id_dict.pop("background")

        return class_id_dict


# ==================== 优化的特征提取器 ====================

class OptimizedFeatureExtractor:
    """优化的特征提取器 - 向量化计算和并行处理"""

    def __init__(self, config: OptimizedSumYanyeConfig):
        self.config = config
        self.class_dict_manager = OptimizedClassDictManager(config)

    def extract_features(self, pic_path: str, json_dir_path: Optional[str] = None,
                        use_simple_mode: bool = False) -> Dict[str, Any]:
        """提取特征 - 智能优化版本"""
        try:
            if use_simple_mode:
                return self._extract_features_simple(pic_path, json_dir_path)
            else:
                return self._extract_features_optimized(pic_path, json_dir_path)
        except Exception as e:
            logging.error(f"特征提取失败: {pic_path}, 错误: {e}")
            raise FeatureExtractionError(f"特征提取失败: {e}")

    def _extract_features_simple(self, pic_path: str, json_dir_path: Optional[str] = None) -> Dict[str, Any]:
        """简化特征提取 - 适用于小数据集"""
        # 确定目标路径
        if json_dir_path is None:
            pic_path_dst = pic_path
        else:
            pic_path_dst = bf.pathjoin(json_dir_path, bf.get_file_name(pic_path))

        # 直接使用原版本的逻辑，避免复杂的优化开销
        class_id_dict = self.class_dict_manager.get_class_id_dict()
        class_amt_dict = class_id_dict.copy()

        # 读取用户特征
        user_feature_dict = bf.labelme_json_read_userfeature_file(bf.rename_to_json(pic_path_dst))

        # 简化特征提取
        for k, v in class_id_dict.items():
            ret_list = bf.labelme_json_shape_read_point(pic_path_dst, label_name=k, shapes_name="orig")
            amt = len(ret_list)
            class_amt_dict[k] = amt

            if k == "zhousuo":
                self._process_zhousuo_features(class_amt_dict, amt, user_feature_dict, k)

            print(f'{k}={amt}')

        # 提取线条特征
        self._extract_line_features_simple(pic_path_dst, class_amt_dict, pic_path)

        # 计算衍生特征
        self._calculate_derived_features(class_amt_dict)

        return class_amt_dict

    @performance_monitor
    def _extract_features_optimized(self, pic_path: str, json_dir_path: Optional[str] = None) -> Dict[str, Any]:
        """优化特征提取 - 适用于大数据集"""
        # 确定目标路径
        if json_dir_path is None:
            pic_path_dst = pic_path
        else:
            pic_path_dst = bf.pathjoin(json_dir_path, bf.get_file_name(pic_path))

        # 生成缓存键
        cache_key = self._generate_cache_key(pic_path_dst)

        # 检查缓存
        if self.config.enable_cache:
            cached_result = feature_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

        # 获取类别字典
        class_id_dict = self.class_dict_manager.get_class_id_dict()
        class_amt_dict = class_id_dict.copy()

        # 读取用户特征
        user_feature_dict = bf.labelme_json_read_userfeature_file(bf.rename_to_json(pic_path_dst))

        # 向量化特征提取
        if self.config.enable_vectorized_computation:
            self._extract_features_vectorized(pic_path_dst, class_id_dict, class_amt_dict, user_feature_dict)
        else:
            self._extract_features_traditional(pic_path_dst, class_id_dict, class_amt_dict, user_feature_dict)

        # 提取线条特征
        self._extract_line_features(pic_path_dst, class_amt_dict, pic_path)

        # 计算衍生特征
        self._calculate_derived_features(class_amt_dict)

        # 缓存结果
        if self.config.enable_cache:
            feature_cache.set(cache_key, class_amt_dict.copy())

        return class_amt_dict

    def _extract_features_vectorized(self, pic_path_dst: str, class_id_dict: Dict,
                                   class_amt_dict: Dict, user_feature_dict: Dict):
        """向量化特征提取"""
        # 批量处理所有类别
        for k, v in class_id_dict.items():
            ret_list = bf.labelme_json_shape_read_point(pic_path_dst, label_name=k, shapes_name="orig")
            amt = len(ret_list)
            class_amt_dict[k] = amt

            # 特殊处理zhousuo
            if k == "zhousuo":
                self._process_zhousuo_features(class_amt_dict, amt, user_feature_dict, k)

            print(f'{k}={amt}')

    def _extract_features_traditional(self, pic_path_dst: str, class_id_dict: Dict,
                                    class_amt_dict: Dict, user_feature_dict: Dict):
        """传统特征提取方法 - 兼容性保证"""
        for k, v in class_id_dict.items():
            ret_list = bf.labelme_json_shape_read_point(pic_path_dst, label_name=k, shapes_name="orig")
            amt = len(ret_list)
            class_amt_dict[k] = amt

            if k == "zhousuo":
                self._process_zhousuo_features(class_amt_dict, amt, user_feature_dict, k)

            print(f'{k}={amt}')

    def _process_zhousuo_features(self, class_amt_dict: Dict, amt: int,
                                user_feature_dict: Dict, k: str):
        """处理zhousuo特征"""
        try:
            if user_feature_dict.get("mian_ji", 0) > 0:
                class_amt_dict[f"{k}_amt_mian_ji"] = amt / user_feature_dict["mian_ji"]
            else:
                class_amt_dict[f"{k}_amt_mian_ji"] = 0

            if user_feature_dict.get("ye_chang", 0) > 0:
                class_amt_dict[f"{k}_amt_ye_chang"] = amt / user_feature_dict["ye_chang"]
            else:
                class_amt_dict[f"{k}_amt_ye_chang"] = 0

            if user_feature_dict.get("ye_kuan", 0) > 0:
                class_amt_dict[f"{k}_amt_ye_kuan"] = amt / user_feature_dict["ye_kuan"]
            else:
                class_amt_dict[f"{k}_amt_ye_kuan"] = 0
        except Exception as e:
            logging.warning(f"处理zhousuo特征时出错: {e}")
            class_amt_dict[f"{k}_amt_mian_ji"] = 0
            class_amt_dict[f"{k}_amt_ye_chang"] = 0
            class_amt_dict[f"{k}_amt_ye_kuan"] = 0

    def _extract_line_features_simple(self, pic_path_dst: str, class_amt_dict: Dict, pic_path: str):
        """简化线条特征提取"""
        for label in LINE_LABELS:
            try:
                bf.sum_user_feature_line_label(pic_path_dst, class_amt_dict, pic_path, label)
            except Exception as e:
                # 简化错误处理，直接设置默认值
                class_amt_dict[f"{label}_amt"] = 0
                class_amt_dict[f"{label}_len"] = 0
                class_amt_dict[f"{label}_avglen"] = 0

    @performance_monitor
    def _extract_line_features(self, pic_path_dst: str, class_amt_dict: Dict, pic_path: str):
        """提取线条特征"""
        for label in LINE_LABELS:
            try:
                bf.sum_user_feature_line_label(pic_path_dst, class_amt_dict, pic_path, label)
            except Exception as e:
                logging.warning(f"提取线条特征 {label} 时出错: {e}")
                # 设置默认值
                class_amt_dict[f"{label}_amt"] = 0
                class_amt_dict[f"{label}_len"] = 0
                class_amt_dict[f"{label}_avglen"] = 0

    def _calculate_derived_features(self, class_amt_dict: Dict):
        """计算衍生特征"""
        try:
            # 计算zhimaiqing_rate
            zhimai_len = class_amt_dict.get("zhimai_len", 0)
            zhimaiqing_len = class_amt_dict.get("zhimaiqing_len", 0)

            if zhimai_len != 0:
                class_amt_dict["zhimaiqing_rate"] = zhimaiqing_len / zhimai_len
            else:
                class_amt_dict["zhimaiqing_rate"] = 0
        except Exception as e:
            logging.warning(f"计算衍生特征时出错: {e}")
            class_amt_dict["zhimaiqing_rate"] = 0

    def _generate_cache_key(self, pic_path_dst: str) -> str:
        """生成缓存键"""
        try:
            json_path = bf.rename_to_json(pic_path_dst)
            if os.path.exists(json_path):
                mtime = os.path.getmtime(json_path)
                key_data = f"{json_path}_{mtime}"
                return hashlib.md5(key_data.encode()).hexdigest()
            else:
                return hashlib.md5(pic_path_dst.encode()).hexdigest()
        except:
            return hashlib.md5(pic_path_dst.encode()).hexdigest()


# ==================== 优化的文件处理器 ====================

class OptimizedFileProcessor:
    """优化的文件处理器 - 批量处理和并行化"""

    def __init__(self, config: OptimizedSumYanyeConfig):
        self.config = config
        self.feature_extractor = OptimizedFeatureExtractor(config)

    def process_single_file(self, image_path: str, json_dir_path: Optional[str] = None,
                          use_simple_mode: bool = False) -> Dict[str, Any]:
        """处理单个文件 - 智能模式选择"""
        try:
            # 根据模式选择处理方式
            if use_simple_mode:
                return self._process_single_file_simple(image_path, json_dir_path)
            else:
                return self._process_single_file_optimized(image_path, json_dir_path)
        except Exception as e:
            logging.error(f"处理文件失败: {image_path}, 错误: {e}")
            raise

    def _process_single_file_simple(self, image_path: str, json_dir_path: Optional[str] = None) -> Dict[str, Any]:
        """简化单文件处理"""
        # 提取特征
        class_amt_dict = self.feature_extractor.extract_features(image_path, json_dir_path, use_simple_mode=True)

        # 确定JSON路径
        if json_dir_path is None:
            pic_path_dst = image_path
        else:
            pic_path_dst = bf.pathjoin(json_dir_path, bf.get_file_name(image_path))

        json_path_dst = bf.rename_to_json(pic_path_dst)

        # 更新JSON文件
        bf.labelme_json_add_userfeature_file(json_path_dst, class_amt_dict)

        print(f"✅ 处理完成: {bf.get_file_name(image_path)}")

        return class_amt_dict

    @performance_monitor
    @safe_execute(max_retries=2)
    def _process_single_file_optimized(self, image_path: str, json_dir_path: Optional[str] = None) -> Dict[str, Any]:
        """优化单文件处理"""
        # 提取特征
        class_amt_dict = self.feature_extractor.extract_features(image_path, json_dir_path, use_simple_mode=False)

        # 确定JSON路径
        if json_dir_path is None:
            pic_path_dst = image_path
        else:
            pic_path_dst = bf.pathjoin(json_dir_path, bf.get_file_name(image_path))

        json_path_dst = bf.rename_to_json(pic_path_dst)

        # 更新JSON文件
        bf.labelme_json_add_userfeature_file(json_path_dst, class_amt_dict)

        print(f"✅ 处理完成: {bf.get_file_name(image_path)}")

        return class_amt_dict

    @performance_monitor
    def process_directory_batch(self, input_image_dir: str, input_json_dir: str,
                               output_json_dir: Optional[str] = None) -> Dict[str, Any]:
        """批量处理目录"""
        try:
            # 扫描图像文件
            image_files = []
            for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
                files = [f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)]
                image_files.extend(files)

            if not image_files:
                raise SumYanyeProcessingError(f"未找到图像文件: {input_image_dir}")

            print(f"📊 找到 {len(image_files)} 个图像文件")

            # 文件配对和预处理
            file_pairs = self._prepare_file_pairs(image_files, input_image_dir,
                                                 input_json_dir, output_json_dir)

            if not file_pairs:
                raise SumYanyeProcessingError("未找到匹配的文件对")

            print(f"📋 配对成功 {len(file_pairs)} 个文件对")

            # 智能策略选择
            file_count = len(file_pairs)
            use_parallel = self.config.should_use_parallel(file_count)
            use_cache = self.config.should_use_cache(file_count)
            use_simple_mode = file_count < 10  # 小数据集使用简化模式

            print(f"🧠 智能策略: 并行={use_parallel}, 缓存={use_cache}, 简化模式={use_simple_mode}")

            # 根据策略处理文件
            if use_parallel:
                return self._process_files_parallel(file_pairs, use_simple_mode)
            else:
                return self._process_files_sequential(file_pairs, use_simple_mode)

        except Exception as e:
            logging.error(f"批量处理目录失败: {e}")
            raise

    def _prepare_file_pairs(self, image_files: List[str], input_image_dir: str,
                           input_json_dir: str, output_json_dir: Optional[str]) -> List[Tuple[str, str]]:
        """准备文件对"""
        file_pairs = []

        for image_file in image_files:
            image_path = os.path.join(input_image_dir, image_file)
            json_file = os.path.splitext(image_file)[0] + '.json'
            input_json_path = os.path.join(input_json_dir, json_file)

            if not os.path.exists(input_json_path):
                print(f"⚠️  跳过: 未找到对应的JSON文件 {json_file}")
                continue

            # 如果有输出目录，复制JSON文件
            target_json_dir = output_json_dir or input_json_dir
            if output_json_dir:
                output_json_path = os.path.join(output_json_dir, json_file)
                if not os.path.exists(output_json_path):
                    import shutil
                    shutil.copy2(input_json_path, output_json_path)
                    print(f"📋 复制JSON文件: {json_file}")

            file_pairs.append((image_path, target_json_dir))

        return file_pairs

    @performance_monitor
    def _process_files_parallel(self, file_pairs: List[Tuple[str, str]], use_simple_mode: bool = False) -> Dict[str, Any]:
        """并行处理文件"""
        # 智能线程数调整
        file_count = len(file_pairs)
        if file_count <= 5:
            actual_workers = min(file_count, 4)
        elif file_count <= 20:
            actual_workers = min(file_count, self.config.max_workers // 2)
        else:
            actual_workers = self.config.max_workers

        print(f"🚀 使用 {actual_workers} 个线程并行处理 {file_count} 个文件")

        results = {
            "success_count": 0,
            "error_count": 0,
            "processed_files": []
        }

        with ThreadPoolExecutor(max_workers=actual_workers) as executor:
            # 提交任务
            future_to_files = {
                executor.submit(self.process_single_file, img_path, json_dir, use_simple_mode): (img_path, json_dir)
                for img_path, json_dir in file_pairs
            }

            # 收集结果
            for future in tqdm(future_to_files, desc="并行处理烟叶特征汇总"):
                img_path, json_dir = future_to_files[future]
                try:
                    result = future.result(timeout=300)  # 5分钟超时

                    results["success_count"] += 1
                    results["processed_files"].append({
                        "image": bf.get_file_name(img_path),
                        "features": len(result)
                    })

                    # 高性能模式下跳过内存清理
                    if not self.config.disable_memory_limit:
                        self._smart_memory_cleanup()

                except Exception as e:
                    results["error_count"] += 1
                    logging.error(f"并行处理失败: {img_path}, 错误: {e}")

        return results

    def _process_files_sequential(self, file_pairs: List[Tuple[str, str]], use_simple_mode: bool = False) -> Dict[str, Any]:
        """串行处理文件"""
        mode_desc = "简化模式" if use_simple_mode else "优化模式"
        print(f"🔄 使用串行方式处理文件 ({mode_desc})")

        results = {
            "success_count": 0,
            "error_count": 0,
            "processed_files": []
        }

        # 简化模式下不使用tqdm和性能监控，减少开销
        if use_simple_mode:
            for img_path, json_dir in file_pairs:
                try:
                    result = self.process_single_file(img_path, json_dir, use_simple_mode=True)

                    results["success_count"] += 1
                    results["processed_files"].append({
                        "image": bf.get_file_name(img_path),
                        "features": len(result)
                    })

                except Exception as e:
                    results["error_count"] += 1
                    print(f"❌ 处理失败: {bf.get_file_name(img_path)} - {e}")
        else:
            for img_path, json_dir in tqdm(file_pairs, desc="串行处理烟叶特征汇总"):
                try:
                    result = self.process_single_file(img_path, json_dir, use_simple_mode=False)

                    results["success_count"] += 1
                    results["processed_files"].append({
                        "image": bf.get_file_name(img_path),
                        "features": len(result)
                    })

                    # 高性能模式下跳过内存清理
                    if not self.config.disable_memory_limit:
                        self._smart_memory_cleanup()

                except Exception as e:
                    results["error_count"] += 1
                    logging.error(f"串行处理失败: {img_path}, 错误: {e}")

        return results

    def _smart_memory_cleanup(self):
        """智能内存清理机制"""
        if self.config.disable_memory_limit:
            return

        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            # 只在内存使用极高时才清理
            memory_threshold = 8192  # 8GB阈值
            if memory_mb > memory_threshold:
                # 部分清理缓存
                if len(feature_cache.cache) > self.config.feature_cache_size * 0.8:
                    feature_cache._evict_batch()
                if len(geometry_cache.cache) > self.config.geometry_cache_size * 0.8:
                    geometry_cache._evict_batch()

                logging.info(f"内存使用: {memory_mb:.2f}MB")
        except ImportError:
            pass


# ==================== 主要API函数 ====================

def run_sum_yanye_user_feature_main(input_image_dir: str, input_json_dir: str,
                                   output_json_dir: Optional[str] = None) -> bool:
    """
    烟叶特征汇总主函数 - 优化版本

    Args:
        input_image_dir: 输入图像目录路径
        input_json_dir: 输入JSON文件目录路径
        output_json_dir: 输出JSON文件目录路径，如果为None则输出到input_json_dir

    Returns:
        bool: 处理是否成功
    """
    try:
        print("🚀 开始烟叶特征汇总处理（优化版本）...")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输出JSON目录: {output_json_dir or input_json_dir}")

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入图像目录不存在: {input_image_dir}")
            return False

        if not os.path.exists(input_json_dir):
            print(f"❌ 输入JSON目录不存在: {input_json_dir}")
            return False

        # 创建输出目录
        if output_json_dir and not os.path.exists(output_json_dir):
            os.makedirs(output_json_dir, exist_ok=True)
            print(f"✅ 创建输出目录: {output_json_dir}")

        # 创建优化配置
        config = create_optimized_config()

        # 创建文件处理器
        processor = OptimizedFileProcessor(config)

        # 执行批量处理
        start_time = time.time()
        results = processor.process_directory_batch(
            input_image_dir=input_image_dir,
            input_json_dir=input_json_dir,
            output_json_dir=output_json_dir
        )
        end_time = time.time()

        # 显示结果
        print(f"\n📊 烟叶特征汇总处理完成:")
        print(f"✅ 成功: {results['success_count']} 个文件")
        print(f"❌ 失败: {results['error_count']} 个文件")
        print(f"⏱️  处理耗时: {end_time - start_time:.2f}秒")

        return results['success_count'] > 0

    except Exception as e:
        print(f"❌ 烟叶特征汇总处理异常: {e}")
        traceback.print_exc()
        return False


# ==================== 兼容性函数 ====================

def do_one(pic_path: str, json_dir_path: Optional[str] = None):
    """兼容性函数 - 保持原有接口"""
    config = OptimizedSumYanyeConfig()
    processor = OptimizedFileProcessor(config)
    processor.process_single_file(pic_path, json_dir_path)


def do_dir(dirpath: str, json_dirpath: str):
    """兼容性函数 - 保持原有接口"""
    return run_sum_yanye_user_feature_main(
        input_image_dir=dirpath,
        input_json_dir=json_dirpath,
        output_json_dir=None
    )


# ==================== 配置创建函数 ====================

def create_optimized_config() -> OptimizedSumYanyeConfig:
    """创建高性能优化配置"""
    cpu_count = mp.cpu_count()

    print(f"🔧 检测到 {cpu_count} 个CPU核心")

    # 高性能配置：使用更多线程
    if cpu_count <= 4:
        max_workers = cpu_count
    elif cpu_count <= 8:
        max_workers = cpu_count - 1
    else:
        max_workers = min(cpu_count - 2, 16)

    print(f"🚀 配置线程数: {max_workers}")

    return OptimizedSumYanyeConfig(
        enable_parallel=True,
        max_workers=max_workers,
        disable_memory_limit=True,
        enable_cache=True,
        feature_cache_size=5000,
        geometry_cache_size=3000,
        batch_size=20,
        enable_batch_processing=True,
        enable_vectorized_computation=True,
        enable_aggressive_optimization=True
    )


@performance_monitor
def main():
    """优化的主函数"""
    try:
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sum_yanye_user_feature_optimized.log'),
                logging.StreamHandler()
            ]
        )

        # 创建优化配置
        config = create_optimized_config()

        # 更新全局实例
        global memory_pool, feature_cache, geometry_cache
        memory_pool = MemoryPool(disable_limit=config.disable_memory_limit)
        feature_cache = ThreadSafeCache(max_size=config.feature_cache_size)
        geometry_cache = ThreadSafeCache(max_size=config.geometry_cache_size)

        print("🧪 烟叶特征汇总部署程序（高性能优化版本）")
        print("=" * 60)
        print(f"📊 配置信息:")
        print(f"   并行处理: {'启用' if config.enable_parallel else '禁用'}")
        print(f"   最大工作线程: {config.max_workers}")
        print(f"   缓存: {'启用' if config.enable_cache else '禁用'}")
        print(f"   特征缓存大小: {config.feature_cache_size}")
        print(f"   几何缓存大小: {config.geometry_cache_size}")
        print(f"   内存限制: {'禁用' if config.disable_memory_limit else '启用'}")
        print(f"   批处理大小: {config.batch_size}")
        print(f"   向量化计算: {'启用' if config.enable_vectorized_computation else '禁用'}")
        print(f"   激进优化: {'启用' if config.enable_aggressive_optimization else '禁用'}")

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_json_dir = os.path.join(current_dir, '..', 'correlation_json_generate', 'test_output')
        input_image_dir = os.path.join(current_dir, '..', 'yanye_user_feature', 'vis')
        output_json_dir = os.path.join(current_dir, 'test_output')

        # 路径标准化
        input_json_dir = os.path.abspath(input_json_dir)
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输出目录: {output_json_dir}")

        start_time = time.time()

        # 运行主处理函数
        success = run_sum_yanye_user_feature_main(
            input_image_dir=input_image_dir,
            input_json_dir=input_json_dir,
            output_json_dir=output_json_dir
        )

        end_time = time.time()
        total_time = end_time - start_time

        if success:
            print("\n🎉 烟叶特征汇总处理完成！")
            print(f"⏱️  总耗时: {total_time:.2f}秒")

            # 显示性能统计
            _show_performance_stats()

            return True
        else:
            print("\n💥 烟叶特征汇总处理失败！")
            return False

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        traceback.print_exc()
        return False


def _show_performance_stats():
    """显示性能统计信息"""
    try:
        print("\n📊 性能统计:")

        # 缓存统计
        feature_stats = feature_cache.get_stats()
        geometry_stats = geometry_cache.get_stats()

        print(f"   特征缓存: {feature_stats['size']}/{feature_stats['max_size']} (命中率: {feature_stats['hit_rate']:.2%})")
        print(f"   几何缓存: {geometry_stats['size']}/{geometry_stats['max_size']} (命中率: {geometry_stats['hit_rate']:.2%})")
        print(f"   内存池: 高性能模式 (无限制)")

        print("🚀 高性能模式: 跳过内存清理以保持最佳性能")

    except Exception as e:
        logging.warning(f"性能统计显示失败: {e}")


if __name__ == "__main__":
    print("🚀 烟叶特征汇总 - 多线程优化版本")
    print("=" * 50)

    success = main()
    if success:
        print("🎉 程序执行成功！")
    else:
        print("💥 程序执行失败！")
        sys.exit(1)
