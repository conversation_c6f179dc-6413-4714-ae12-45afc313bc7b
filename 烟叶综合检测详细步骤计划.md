# 烟叶综合检测详细步骤计划

## 项目概述

将12个独立的烟叶检测模块集成到一个统一的GPU流水线并行处理系统中，实现高效的综合检测和结果合并。

## 需求详情

### 检测模块列表
1. **主脉打开检测** - `/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py`
2. **主脉走势检测** - `/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py`
3. **折痕检测** - `/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py`
4. **支脉检测** - `/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py`
5. **支脉青检测** - `/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py`
6. **轮廓残缺补全检测** - `/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py`
7. **焦点浮青等检测** - `/seg_det_jiaodian/deploy_seg_det_condition_onnx_gpu_final.py`
8. **横纹检测** - `/seg_det_hengwen/deploy_seg_det_hengwen_onnx_gpu_final.py`
9. **烤红检测** - `/seg_det_kaohong/deploy_seg_det_kaohong_onnx_gpu_final.py`
10. **挂灰检测** - `/seg_det_kaohong/deploy_seg_det_guahui_onnx_gpu_final.py`
11. **皱缩检测** - `/seg_det_zhousuo/deploy_seg_det_zhousuo_onnx_gpu_final.py`
12. **潮红检测** - `/seg_det_chaohong/deploy_seg_det_chaohong_onnx_gpu_final.py`

### 硬件资源
- **GPU资源**: 2块24GB显存的GPU
- **目标**: 合理分配GPU资源，避免冲突，最大化处理效率

### 输入输出
- **测试输入**: `/home/<USER>/xm/code/coderafactor/test_images/` 前5张图像
- **输出目录**: `/home/<USER>/xm/code/coderafactor/test_data/test_output/`
- **输出内容**: 合并的JSON文件 + 可视化结果

## 详细实施步骤

### 第一阶段：架构设计与分析

#### 1.1 现有模块分析
- [ ] **分析每个模块的GPU使用情况**
  - 检查每个模块的GPU设备分配
  - 分析模型大小和显存占用
  - 评估推理时间和性能特征

- [ ] **分析模块间的依赖关系**
  - 识别哪些模块可以并行执行
  - 确定是否存在数据依赖
  - 分析输出格式的兼容性

- [ ] **评估GPU资源分配策略**
  - GPU 0: 分配6个模块
  - GPU 1: 分配6个模块
  - 考虑模型大小和计算复杂度的平衡

#### 1.2 架构设计
- [ ] **设计统一的数据流架构**
  - 定义统一的PipelineData结构
  - 设计多模块异步队列系统
  - 规划GPU内存池管理策略

- [ ] **设计模块管理器**
  - 创建ComprehensiveModelManager类
  - 实现模块的动态加载和卸载
  - 设计模块间的协调机制

- [ ] **设计结果合并策略**
  - 定义统一的JSON输出格式
  - 设计shapes合并算法
  - 处理标签冲突和重复检测

### 第二阶段：核心组件实现

#### 2.1 基础架构组件
- [ ] **实现统一的数据结构**
  ```python
  @dataclass
  class ComprehensivePipelineData:
      image_path: str
      image_data: Optional[np.ndarray] = None
      # 各模块的检测结果
      zhumaidakai_result: Optional[Dict] = None
      zhumaizoushi_result: Optional[Dict] = None
      # ... 其他模块结果
      merged_shapes: Optional[List] = None
  ```

- [ ] **实现GPU资源管理器**
  ```python
  class GPUResourceManager:
      def __init__(self):
          self.gpu0_modules = []  # GPU 0分配的模块
          self.gpu1_modules = []  # GPU 1分配的模块
      
      def allocate_modules(self):
          # 智能分配模块到GPU
      
      def get_gpu_usage(self):
          # 监控GPU使用情况
  ```

- [ ] **实现多模块内存池**
  ```python
  class MultiModuleMemoryPool:
      def __init__(self):
          self.gpu0_pool = GPUMemoryPool()
          self.gpu1_pool = GPUMemoryPool()
      
      def get_buffer(self, gpu_id, module_name):
          # 为指定模块获取GPU缓冲区
  ```

#### 2.2 模块集成组件
- [ ] **实现模块包装器**
  ```python
  class ModuleWrapper:
      def __init__(self, module_name, gpu_id, model_path):
          self.module_name = module_name
          self.gpu_id = gpu_id
          self.model_manager = None
      
      def load_model(self):
          # 加载指定GPU上的模型
      
      def process_image(self, image_data):
          # 处理图像并返回结果
  ```

- [ ] **实现模块调度器**
  ```python
  class ModuleScheduler:
      def __init__(self, modules):
          self.modules = modules
          self.execution_queue = queue.Queue()
      
      def schedule_modules(self, pipeline_data):
          # 调度模块执行顺序
  ```

### 第三阶段：流水线处理器实现

#### 3.1 主流水线处理器
- [ ] **实现ComprehensivePipelineProcessor**
  ```python
  class ComprehensivePipelineProcessor:
      def __init__(self):
          self.gpu_manager = GPUResourceManager()
          self.module_wrappers = {}
          self.scheduler = ModuleScheduler()
      
      def initialize_modules(self):
          # 初始化所有检测模块
      
      def process_images_pipeline(self, image_paths):
          # 流水线并行处理图像
  ```

#### 3.2 工作线程设计
- [ ] **预处理工作线程**
  - 图像读取和预处理
  - 数据分发到各个模块队列

- [ ] **模块处理工作线程**
  - GPU 0模块处理线程 (6个模块)
  - GPU 1模块处理线程 (6个模块)
  - 每个GPU使用独立的线程池

- [ ] **后处理工作线程**
  - 结果收集和合并
  - JSON生成和保存
  - 统计信息汇总

### 第四阶段：结果合并与输出

#### 4.1 结果合并策略
- [ ] **实现智能合并算法**
  ```python
  class ResultMerger:
      def merge_shapes(self, all_results):
          # 合并所有模块的检测结果
          # 处理重叠和冲突
          # 生成统一的shapes列表
      
      def resolve_conflicts(self, shapes):
          # 解决标签冲突
          # 处理重复检测
  ```

- [ ] **定义输出JSON格式**
  ```json
  {
      "version": "4.5.6",
      "flags": {},
      "shapes": [
          // 合并后的所有检测结果
      ],
      "imagePath": "image.jpg",
      "imageData": null,
      "imageHeight": 512,
      "imageWidth": 1152,
      "detection_summary": {
          "total_detections": 100,
          "module_counts": {
              "zhumaidakai": 5,
              "zhumaizoushi": 8,
              // ... 其他模块统计
          }
      }
  }
  ```

#### 4.2 可视化组件
- [ ] **实现可视化处理器**
  ```python
  class ComprehensiveVisualizer:
      def __init__(self):
          self.color_map = {}  # 为每个模块分配颜色
      
      def visualize_results(self, image_path, shapes):
          # 在图像上绘制所有检测结果
          # 使用不同颜色区分不同模块
      
      def create_legend(self):
          # 创建图例说明
  ```

### 第五阶段：性能优化与测试

#### 5.1 性能优化
- [ ] **GPU负载均衡**
  - 动态调整模块分配
  - 监控GPU使用率
  - 优化内存使用

- [ ] **流水线优化**
  - 调整队列大小
  - 优化线程数量
  - 减少数据拷贝开销

#### 5.2 测试验证
- [ ] **单元测试**
  - 测试每个模块的集成
  - 验证GPU资源分配
  - 检查结果合并正确性

- [ ] **集成测试**
  - 使用前5张测试图像
  - 验证完整流水线处理
  - 检查输出文件格式

- [ ] **性能测试**
  - 测量处理速度
  - 监控GPU使用率
  - 评估并行效率

## GPU资源分配策略

### 方案A：按模型复杂度分配
**GPU 0 (6个模块)**:
1. 主脉打开检测 (轻量级)
2. 主脉走势检测 (轻量级)
3. 折痕检测 (中等)
4. 支脉检测 (中等)
5. 支脉青检测 (中等)
6. 横纹检测 (轻量级)

**GPU 1 (6个模块)**:
1. 轮廓残缺补全检测 (重量级，双模型)
2. 焦点浮青等检测 (重量级)
3. 烤红检测 (中等)
4. 挂灰检测 (中等)
5. 皱缩检测 (中等)
6. 潮红检测 (中等)

### 方案B：按处理时间分配
- 根据实际测试的处理时间进行动态调整
- 确保两个GPU的负载基本均衡

## 文件结构规划

```
/home/<USER>/xm/code/coderafactor/
├── comprehensive_det_onnx_gpu.py          # 主程序
├── vis_comprehensive_det_onnx_gpu.py      # 可视化程序
├── comprehensive_detection/               # 综合检测模块
│   ├── __init__.py
│   ├── pipeline_processor.py             # 流水线处理器
│   ├── module_manager.py                 # 模块管理器
│   ├── gpu_resource_manager.py           # GPU资源管理器
│   ├── result_merger.py                  # 结果合并器
│   └── visualizer.py                     # 可视化器
└── test_data/
    └── test_output/                       # 输出目录
        ├── *.json                         # 合并的JSON文件
        └── *.png                          # 可视化结果
```

## 预期性能目标

- **处理速度**: ≥ 2张/秒 (综合12个模块)
- **GPU利用率**: 两块GPU均 > 80%
- **内存使用**: < 20GB per GPU
- **准确性**: 与单独运行各模块结果一致

## 风险评估与应对

### 主要风险
1. **GPU显存不足** - 某些模块组合可能超出24GB限制
2. **模块冲突** - 不同模块可能使用相同的GPU设备ID
3. **性能瓶颈** - 某些重量级模块可能成为瓶颈
4. **结果冲突** - 不同模块可能产生重叠或冲突的检测结果

### 应对策略
1. **动态内存管理** - 实现模型的动态加载/卸载
2. **设备隔离** - 严格控制每个模块的GPU设备分配
3. **负载均衡** - 根据实际性能调整模块分配
4. **智能合并** - 实现冲突检测和解决算法

## 验证标准

### 功能验证
- [ ] 所有12个模块成功加载
- [ ] 前5张测试图像成功处理
- [ ] 生成合并的JSON文件
- [ ] 生成可视化结果图像

### 性能验证
- [ ] 处理速度达到预期目标
- [ ] GPU资源合理利用
- [ ] 内存使用在安全范围内

### 质量验证
- [ ] 检测结果与单独运行一致
- [ ] JSON格式正确完整
- [ ] 可视化结果清晰准确

## 后续扩展计划

1. **支持更多图像格式**
2. **实现批量处理模式**
3. **添加实时监控界面**
4. **支持分布式处理**
5. **集成模型版本管理**

## 实施时间线

### 第1天：架构设计与分析
- 上午：分析现有模块，评估GPU资源需求
- 下午：设计统一架构，制定GPU分配策略

### 第2天：核心组件实现
- 上午：实现基础架构组件（数据结构、内存池等）
- 下午：实现模块集成组件（包装器、调度器等）

### 第3天：流水线处理器实现
- 上午：实现主流水线处理器
- 下午：实现各类工作线程

### 第4天：结果合并与可视化
- 上午：实现结果合并算法
- 下午：实现可视化组件

### 第5天：测试与优化
- 上午：单元测试和集成测试
- 下午：性能优化和最终验证

## 技术难点与解决方案

### 难点1：GPU显存管理
**问题**: 12个模型同时加载可能超出显存限制
**解决方案**:
- 实现模型的懒加载机制
- 使用模型缓存和动态卸载
- 优化模型加载顺序

### 难点2：模块间协调
**问题**: 不同模块可能有不同的输入输出格式
**解决方案**:
- 设计统一的适配器接口
- 实现格式转换层
- 标准化输入输出协议

### 难点3：结果合并复杂性
**问题**: 12个模块的结果可能存在重叠和冲突
**解决方案**:
- 实现基于置信度的冲突解决
- 使用空间重叠检测算法
- 设计优先级规则

### 难点4：性能瓶颈识别
**问题**: 某些模块可能成为整体性能瓶颈
**解决方案**:
- 实现细粒度性能监控
- 动态调整处理优先级
- 使用异步处理机制

## 代码实现要点

### 1. 模块加载策略
```python
class ModuleLoader:
    def __init__(self):
        self.loaded_modules = {}
        self.gpu_allocation = {0: [], 1: []}

    def load_module_on_demand(self, module_name, gpu_id):
        # 按需加载模块，避免显存浪费
        pass

    def unload_unused_modules(self):
        # 卸载长时间未使用的模块
        pass
```

### 2. 智能GPU分配
```python
def allocate_gpu_resources():
    # 基于模型大小和复杂度的智能分配
    gpu0_modules = [
        'zhumaidakai',    # ~2GB
        'zhumaizoushi',   # ~2GB
        'zheheng',        # ~3GB
        'zhimai',         # ~3GB
        'zhimaiqing',     # ~3GB
        'hengwen'         # ~2GB
    ]  # 总计 ~15GB

    gpu1_modules = [
        'lunkuo_canque_fill',  # ~8GB (双模型)
        'jiaodian',            # ~4GB
        'kaohong',             # ~3GB
        'guahui',              # ~3GB
        'zhousuo',             # ~3GB
        'chaohong'             # ~3GB
    ]  # 总计 ~24GB
```

### 3. 流水线同步机制
```python
class PipelineSynchronizer:
    def __init__(self):
        self.completion_flags = {}
        self.result_buffer = {}

    def wait_for_all_modules(self, image_id):
        # 等待所有模块完成处理
        while not all(self.completion_flags[image_id].values()):
            time.sleep(0.001)
        return self.result_buffer[image_id]
```

## 测试验证计划

### 单模块测试
- [ ] 测试每个模块在指定GPU上的独立运行
- [ ] 验证GPU设备分配正确性
- [ ] 检查显存使用情况

### 集成测试
- [ ] 测试2个模块同时运行
- [ ] 测试6个模块同时运行
- [ ] 测试全部12个模块同时运行

### 压力测试
- [ ] 连续处理100张图像
- [ ] 监控GPU温度和使用率
- [ ] 检查内存泄漏情况

### 结果验证
- [ ] 对比单独运行vs集成运行的结果
- [ ] 验证JSON合并的正确性
- [ ] 检查可视化结果的准确性

## 性能基准

### 目标性能指标
- **总体处理速度**: ≥ 2.0张/秒
- **单模块平均时间**: ≤ 0.5秒
- **GPU利用率**: GPU0 > 80%, GPU1 > 80%
- **显存使用**: GPU0 < 20GB, GPU1 < 22GB
- **CPU使用率**: < 50%

### 性能监控指标
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'processing_speed': [],
            'gpu_utilization': {'gpu0': [], 'gpu1': []},
            'memory_usage': {'gpu0': [], 'gpu1': []},
            'module_timing': {}
        }

    def record_metrics(self):
        # 记录各项性能指标
        pass

    def generate_report(self):
        # 生成性能报告
        pass
```

## 错误处理与恢复

### 错误类型与处理
1. **GPU显存不足**: 动态卸载部分模型，重新分配
2. **模型加载失败**: 重试机制，降级处理
3. **推理超时**: 跳过当前图像，记录错误
4. **结果合并失败**: 保存部分结果，标记错误

### 恢复策略
```python
class ErrorRecoveryManager:
    def __init__(self):
        self.retry_count = {}
        self.max_retries = 3

    def handle_gpu_oom(self, gpu_id):
        # 处理GPU显存不足
        self.free_gpu_memory(gpu_id)
        self.reload_essential_modules(gpu_id)

    def handle_module_failure(self, module_name):
        # 处理模块失败
        if self.retry_count[module_name] < self.max_retries:
            self.retry_module(module_name)
        else:
            self.skip_module(module_name)
```

---

**注意**: 此计划需要在实施过程中根据实际情况进行调整和优化。每个阶段完成后需要进行充分的测试验证。

**重要提醒**: 在开始编程实现之前，请仔细审查此计划，确认所有技术方案的可行性和完整性。
