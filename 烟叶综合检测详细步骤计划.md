# 烟叶综合检测详细步骤计划

## 📋 项目概述

### 🎯 目标
将12个独立的烟叶检测模块集成到一个统一的GPU流水线并行处理程序中，实现高效的综合检测和可视化。

### 📊 检测模块清单
1. **主脉打开检测** - `/seg_det_zhumaidakai/deploy_seg_det_zhumaidakai_onnx_gpu_final.py`
2. **主脉走势检测** - `/seg_det_zhumaizoushi/deploy_seg_det_zhumaizoushi_onnx_gpu_final.py`
3. **折痕检测** - `/seg_det_zheheng/deploy_seg_det_zheheng_onnx_gpu_final.py`
4. **支脉检测** - `/seg_det_zhimai/deploy_seg_det_zhimai_onnx_gpu_final.py`
5. **支脉青检测** - `/seg_det_zhimaiqing/deploy_seg_det_zhimaiqing_onnx_gpu_final.py`
6. **轮廓残缺补全检测** - `/seg_det_lunkuo_canque_fill/deploy_seg_lunkuo_canque_fill_onnx_gpu_final.py`
7. **焦点浮青等检测** - `/seg_det_jiaodian/deploy_seg_det_condition_onnx_gpu_final.py`
8. **横纹检测** - `/seg_det_hengwen/deploy_seg_det_hengwen_onnx_gpu_final.py`
9. **烤红检测** - `/seg_det_kaohong/deploy_seg_det_kaohong_onnx_gpu_final.py`
10. **挂灰检测** - `/seg_det_kaohong/deploy_seg_det_guahui_onnx_gpu_final.py`
11. **皱缩检测** - `/seg_det_zhousuo/deploy_seg_det_zhousuo_onnx_gpu_final.py`
12. **潮红检测** - `/seg_det_chaohong/deploy_seg_det_chaohong_onnx_gpu_final.py`

### 🔧 硬件资源
- **GPU资源**: 2块24G显存GPU
- **测试数据**: `/test_images/` 前5张图像
- **输出目录**: `/test_data/test_output/`

## 🚀 详细实施步骤

### 第一阶段：架构设计与分析

#### 步骤1.1：模块架构分析
- [ ] 分析每个检测模块的GPU使用情况
- [ ] 识别模块的输入输出格式
- [ ] 分析模块的依赖关系和冲突点
- [ ] 评估每个模块的GPU内存需求

#### 步骤1.2：GPU资源分配策略
- [ ] **GPU 0分配**：
  - 主脉打开检测
  - 折痕检测
  - 支脉青检测
  - 焦点浮青等检测
  - 烤红检测
  - 皱缩检测
- [ ] **GPU 1分配**：
  - 主脉走势检测
  - 支脉检测
  - 轮廓残缺补全检测
  - 横纹检测
  - 挂灰检测
  - 潮红检测

#### 步骤1.3：流水线架构设计
- [ ] 设计统一的数据流结构
- [ ] 设计模块间的通信机制
- [ ] 设计结果合并策略
- [ ] 设计错误处理和恢复机制

### 第二阶段：核心程序开发

#### 步骤2.1：创建comprehensive_det_onnx_gpu.py
- [ ] **导入模块管理**：
  ```python
  # 导入所有检测模块的核心类
  from seg_det_zhumaidakai.deploy_seg_det_zhumaidakai_onnx_gpu_final import ZhumaidakaiPipelineProcessor
  from seg_det_zhumaizoushi.deploy_seg_det_zhumaizoushi_onnx_gpu_final import ZhumaizoushiPipelineProcessor
  # ... 其他模块
  ```

- [ ] **GPU资源管理器**：
  ```python
  class GPUResourceManager:
      def __init__(self):
          self.gpu0_modules = []  # GPU 0上的模块
          self.gpu1_modules = []  # GPU 1上的模块
      
      def allocate_gpu_resources(self):
          # 分配GPU资源给各个模块
  ```

- [ ] **综合检测处理器**：
  ```python
  class ComprehensiveDetectionProcessor:
      def __init__(self):
          self.modules = {}  # 存储所有检测模块
          self.gpu_manager = GPUResourceManager()
      
      def initialize_all_modules(self):
          # 初始化所有检测模块
      
      def process_image_comprehensive(self, image_path):
          # 对单张图像进行综合检测
      
      def merge_detection_results(self, results):
          # 合并所有模块的检测结果
  ```

#### 步骤2.2：模块集成策略
- [ ] **顺序执行策略**：
  - 按模块重要性排序执行
  - 避免GPU内存冲突
  - 实现结果传递机制

- [ ] **并行执行策略**：
  - GPU 0和GPU 1并行处理不同模块
  - 实现同步机制
  - 处理依赖关系

#### 步骤2.3：结果合并机制
- [ ] **JSON结果合并**：
  ```python
  def merge_json_results(self, module_results):
      merged_shapes = []
      for module_name, result in module_results.items():
          for shape in result.get('shapes', []):
              shape['module'] = module_name  # 标记来源模块
              merged_shapes.append(shape)
      return merged_shapes
  ```

- [ ] **冲突处理**：
  - 处理重叠检测结果
  - 实现优先级机制
  - 去重和合并策略

### 第三阶段：可视化程序开发

#### 步骤3.1：创建vis_comprehensive_det_onnx_gpu.py
- [ ] **可视化核心类**：
  ```python
  class ComprehensiveVisualization:
      def __init__(self):
          self.colors = {}  # 为每个模块分配颜色
          self.legend = {}  # 图例信息
      
      def visualize_comprehensive_results(self, image_path, json_path):
          # 可视化综合检测结果
      
      def draw_module_results(self, image, shapes, module_name):
          # 绘制特定模块的检测结果
  ```

- [ ] **模块颜色分配**：
  - 主脉打开：红色
  - 主脉走势：蓝色
  - 折痕：绿色
  - 支脉：黄色
  - 支脉青：青色
  - 轮廓残缺：紫色
  - 焦点浮青：橙色
  - 横纹：粉色
  - 烤红：深红色
  - 挂灰：灰色
  - 皱缩：棕色
  - 潮红：浅红色

#### 步骤3.2：可视化功能实现
- [ ] **多层叠加显示**：
  - 支持显示/隐藏特定模块结果
  - 透明度控制
  - 图例显示

- [ ] **交互功能**：
  - 鼠标悬停显示详细信息
  - 点击切换模块显示
  - 缩放和平移功能

### 第四阶段：测试与优化

#### 步骤4.1：单元测试
- [ ] 测试每个模块的独立运行
- [ ] 测试GPU资源分配
- [ ] 测试结果合并功能
- [ ] 测试可视化功能

#### 步骤4.2：集成测试
- [ ] 使用前5张测试图像进行完整流程测试
- [ ] 验证所有模块的检测结果
- [ ] 检查JSON输出格式
- [ ] 验证可视化效果

#### 步骤4.3：性能优化
- [ ] GPU内存使用优化
- [ ] 处理速度优化
- [ ] 并行度提升
- [ ] 错误处理完善

### 第五阶段：部署与验证

#### 步骤5.1：最终测试
- [ ] 运行完整的测试命令：
  ```bash
  source ~/.bashrc && conda activate vllm && python comprehensive_det_onnx_gpu.py
  ```
- [ ] 验证所有检测结果
- [ ] 检查输出文件完整性
- [ ] 验证可视化程序运行

#### 步骤5.2：结果验证
- [ ] 对比各模块独立运行结果
- [ ] 验证结果合并的正确性
- [ ] 检查可视化的准确性
- [ ] 性能指标评估

## 📁 文件结构

```
/home/<USER>/xm/code/coderafactor/
├── comprehensive_det_onnx_gpu.py          # 主程序
├── vis_comprehensive_det_onnx_gpu.py      # 可视化程序
├── test_images/                           # 测试图像（前5张）
├── test_data/
│   └── test_output/                       # 输出目录
│       ├── *.json                         # 合并的检测结果
│       └── *.png                          # 可视化结果
└── 各个检测模块目录/                       # 现有的检测模块
```

## ⚠️ 关键注意事项

### 技术挑战
1. **GPU内存管理**：确保24G显存足够支持多个模型同时加载
2. **模块冲突**：避免CUDA上下文冲突和模型加载冲突
3. **结果一致性**：确保集成后的结果与独立运行一致
4. **性能优化**：平衡并行度和资源使用

### 实施风险
1. **内存不足**：可能需要实现模型动态加载/卸载
2. **兼容性问题**：不同模块可能有依赖冲突
3. **性能瓶颈**：I/O和数据传输可能成为瓶颈
4. **结果冲突**：多个模块可能检测到重叠区域

### 解决方案
1. **分批处理**：如果内存不足，实现模块分批加载
2. **隔离机制**：使用进程隔离避免模块间冲突
3. **缓存机制**：实现中间结果缓存提高效率
4. **优先级机制**：为不同模块设置优先级处理冲突

## 🎯 成功标准

1. **功能完整性**：所有12个模块都能正常运行并产生结果
2. **结果一致性**：集成后的结果与独立运行结果一致
3. **性能要求**：整体处理时间不超过独立运行总时间的150%
4. **可视化质量**：能够清晰显示所有模块的检测结果
5. **稳定性**：连续处理多张图像不出现内存泄漏或崩溃

## 📅 预估时间

- **第一阶段**：2-3小时（架构设计与分析）
- **第二阶段**：4-6小时（核心程序开发）
- **第三阶段**：2-3小时（可视化程序开发）
- **第四阶段**：2-3小时（测试与优化）
- **第五阶段**：1-2小时（部署与验证）

**总计**：11-17小时

---

*本计划将确保烟叶综合检测系统的成功实施，实现高效、准确、可视化的综合检测功能。*
