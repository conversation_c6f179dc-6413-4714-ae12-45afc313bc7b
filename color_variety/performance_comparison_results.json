{"comparison_time": "2025-07-29 09:46:07", "original": {"result": {"version": "原版本", "duration": 6.406347990036011, "success": true, "output": "✅ 成功导入base_function\n🧪 烟叶颜色多样性分析部署程序\n============================================================\n🚀 开始烟叶颜色多样性分析处理...\n📁 输入图像目录: /home/<USER>/xm/code/coderafactor/yanye_user_feature/vis\n📁 输入JSON目录: /home/<USER>/xm/code/coderafactor/sum_yanye_user_feature/test_output\n📁 输出JSON目录: /home/<USER>/xm/code/coderafactor/color_variety/test_output\n✅ 创建输出目录: /home/<USER>/xm/code/coderafactor/color_variety/test_output\n📊 找到 5 个图像文件\n\n🔄 处理文件: hn-cz-2023-C2F-C21-6.bmp\n📋 复制JSON文件: hn-cz-2023-C2F-C21-6.json\nhn-cz-2023-C2F-C21-6.bmp gray_dist_total=190.57204842014806 均匀度=3\ncolor_avg_list=[np.float64(162.31199559210893), np.float64(103.60507553984922), np.float64(13.887243360927572), np.float64(108.7249880011885)]\ncolor_dist_total_list=[np.float64(357.625141857827), np.float64(237.3208428851671), np.float64(105.5588466114782), np.float64(190.57204842014806)]\nfinish=/home/<USER>/xm/code/coderafactor/yanye_user_feature/vis/hn-cz-2023-C2F-C21-6.bmp\n✅ 处理成功: hn-cz-2023-C2F-C21-6.bmp\n\n🔄 处理文件: hn-cz-2023-C2F-C21-4.bmp\n📋 复制JSON文件: hn-cz-2023-C2F-C21-4.json\nhn-cz-2023-C2F-C21-4.bmp gray_dist_total=253.02377689491163 均匀度=2\ncolor_avg_list=[np.float64(152.93621742119282), np.float64(93.04896158900891), np.float64(5.481970223987031), np.float64(100.3416118433055)]\ncolor_dist_total_list=[np.float64(469.6771794761961), np.float64(308.6645841420565), np.float64(103.32177383774024), np.float64(253.02377689491163)]\nfinish=/home/<USER>/xm/code/coderafactor/yanye_user_feature/vis/hn-cz-2023-C2F-C21-4.bmp\n✅ 处理成功: hn-cz-2023-C2F-C21-4.bmp\n\n🔄 处理文件: hn-cz-2023-C2F-C21-3.bmp\n📋 复制JSON文件: hn-cz-2023-C2F-C21-3.json\nhn-cz-2023-C2F-C21-3.bmp gray_dist_total=125.82301097595214 均匀度=3\ncolor_avg_list=[np.float64(154.24644566762214), np.float64(94.53715324617286), np.float64(9.433255633255634), np.float64(100.88746025216612)]\ncolor_dist_total_list=[np.float64(249.89268648562768), np.float64(172.0626831120949), np.float64(146.26697746697747), np.float64(125.82301097595214)]\nfinish=/home/<USER>/xm/code/coderafactor/yanye_user_feature/vis/hn-cz-2023-C2F-C21-3.bmp\n✅ 处理成功: hn-cz-2023-C2F-C21-3.bmp\n\n🔄 处理文件: hn-cz-2023-C2F-C21-5.bmp\n📋 复制JSON文件: hn-cz-2023-C2F-C21-5.json\nhn-cz-2023-C2F-C21-5.bmp gray_dist_total=152.75108437661646 均匀度=3\ncolor_avg_list=[np.float64(160.22235069894643), np.float64(101.35609355183821), np.float64(18.23726495726496), np.float64(106.9075123458102)]\ncolor_dist_total_list=[np.float64(425.3221898668711), np.float64(177.0262928560802), np.float64(123.52547008547009), np.float64(152.75108437661646)]\nfinish=/home/<USER>/xm/code/coderafactor/yanye_user_feature/vis/hn-cz-2023-C2F-C21-5.bmp\n✅ 处理成功: hn-cz-2023-C2F-C21-5.bmp\n\n🔄 处理文件: hn-cz-2023-C2F-C21-7.bmp\n📋 复制JSON文件: hn-cz-2023-C2F-C21-7.json\nhn-cz-2023-C2F-C21-7.bmp gray_dist_total=188.44435452502677 均匀度=3\ncolor_avg_list=[np.float64(147.8486713233412), np.float64(88.91306964344179), np.float64(10.862585034013605), np.float64(96.6336547006415)]\ncolor_dist_total_list=[np.float64(368.6226278456772), np.float64(232.92315655532943), np.float64(111.60816326530613), np.float64(188.44435452502677)]\nfinish=/home/<USER>/xm/code/coderafactor/yanye_user_feature/vis/hn-cz-2023-C2F-C21-7.bmp\n✅ 处理成功: hn-cz-2023-C2F-C21-7.bmp\n\n📊 处理完成:\n✅ 成功: 5 个文件\n❌ 失败: 0 个文件\n\n🎉 烟叶颜色多样性分析处理完成！\n", "error": ""}, "stats": {"success_files": 5, "error_files": 0, "processing_time": 0.0, "total_time": 0.0}}, "optimized": {"result": {"version": "优化版本", "duration": 9.952656745910645, "success": true, "output": "🚀 烟叶颜色多样性分析 - 多线程优化版本\n==================================================\n🔧 检测到 96 个CPU核心\n🚀 配置线程数: 16\n🧪 烟叶颜色多样性分析部署程序（高性能优化版本）\n============================================================\n📊 配置信息:\n   并行处理: 启用\n   最大工作线程: 16\n   缓存: 启用\n   掩码缓存大小: 5000\n   直方图缓存大小: 3000\n   内存限制: 禁用\n   批处理大小: 20\n   向量化计算: 启用\n   激进优化: 启用\n📁 输入JSON目录: /home/<USER>/xm/code/coderafactor/sum_yanye_user_feature/test_output\n📁 输入图像目录: /home/<USER>/xm/code/coderafactor/yanye_user_feature/vis\n📁 输出目录: /home/<USER>/xm/code/coderafactor/color_variety/test_output\n🚀 开始烟叶颜色多样性分析处理（优化版本）...\n📁 输入图像目录: /home/<USER>/xm/code/coderafactor/yanye_user_feature/vis\n📁 输入JSON目录: /home/<USER>/xm/code/coderafactor/sum_yanye_user_feature/test_output\n📁 输出JSON目录: /home/<USER>/xm/code/coderafactor/color_variety/test_output\n✅ 创建输出目录: /home/<USER>/xm/code/coderafactor/color_variety/test_output\n🔧 检测到 96 个CPU核心\n🚀 配置线程数: 16\n📊 找到 5 个图像文件\n📋 配对成功 5 个文件对\n🚀 使用 4 个线程并行处理 5 个文件\nhn-cz-2023-C2F-C21-5.bmp gray_dist_total=152.75108437661646 均匀度=3\ncolor_avg_list=[np.float64(160.22235069894643), np.float64(101.35609355183821), np.float64(18.23726495726496), np.float64(106.9075123458102)]\ncolor_dist_total_list=[np.float64(425.3221898668711), np.float64(177.0262928560802), np.float64(123.52547008547009), np.float64(152.75108437661646)]\nhn-cz-2023-C2F-C21-3.bmp gray_dist_total=125.82301097595214 均匀度=3\ncolor_avg_list=[np.float64(154.24644566762214), np.float64(94.53715324617286), np.float64(9.433255633255634), np.float64(100.88746025216612)]\ncolor_dist_total_list=[np.float64(249.89268648562768), np.float64(172.0626831120949), np.float64(146.26697746697747), np.float64(125.82301097595214)]\n✅ 处理完成: hn-cz-2023-C2F-C21-5.bmp\nhn-cz-2023-C2F-C21-4.bmp gray_dist_total=253.02377689491163 均匀度=2\ncolor_avg_list=[np.float64(152.93621742119282), np.float64(93.04896158900891), np.float64(5.481970223987031), np.float64(100.3416118433055)]\ncolor_dist_total_list=[np.float64(469.6771794761961), np.float64(308.6645841420565), np.float64(103.32177383774024), np.float64(253.02377689491163)]\n✅ 处理完成: hn-cz-2023-C2F-C21-3.bmp\nhn-cz-2023-C2F-C21-6.bmp gray_dist_total=190.57204842014806 均匀度=3\ncolor_avg_list=[np.float64(162.31199559210893), np.float64(103.60507553984922), np.float64(13.887243360927572), np.float64(108.7249880011885)]\ncolor_dist_total_list=[np.float64(357.625141857827), np.float64(237.3208428851671), np.float64(105.5588466114782), np.float64(190.57204842014806)]\n✅ 处理完成: hn-cz-2023-C2F-C21-4.bmp\n✅ 处理完成: hn-cz-2023-C2F-C21-6.bmp\nhn-cz-2023-C2F-C21-7.bmp gray_dist_total=188.44435452502677 均匀度=3\ncolor_avg_list=[np.float64(147.8486713233412), np.float64(88.91306964344179), np.float64(10.862585034013605), np.float64(96.6336547006415)]\ncolor_dist_total_list=[np.float64(368.6226278456772), np.float64(232.92315655532943), np.float64(111.60816326530613), np.float64(188.44435452502677)]\n✅ 处理完成: hn-cz-2023-C2F-C21-7.bmp\n\n📊 颜色多样性分析处理完成:\n✅ 成功: 5 个文件\n❌ 失败: 0 个文件\n⏱️  处理耗时: 9.38秒\n\n🎉 烟叶颜色多样性分析处理完成！\n⏱️  总耗时: 9.38秒\n\n📊 性能统计:\n   掩码缓存: 5/5000 (命中率: 0.00%)\n   直方图缓存: 2976/3000 (命中率: 0.00%)\n   内存池: 高性能模式 (无限制)\n🚀 高性能模式: 跳过内存清理以保持最佳性能\n🎉 程序执行成功！\n", "error": "\n并行处理颜色多样性:   0%|          | 0/5 [00:00<?, ?it/s]\n并行处理颜色多样性:  20%|██        | 1/5 [00:08<00:34,  8.53s/it]\n并行处理颜色多样性: 100%|██████████| 5/5 [00:09<00:00,  1.41s/it]\n并行处理颜色多样性: 100%|██████████| 5/5 [00:09<00:00,  1.84s/it]\n"}, "stats": {"success_files": 5, "error_files": 0, "processing_time": 9.38, "total_time": 9.38}}}