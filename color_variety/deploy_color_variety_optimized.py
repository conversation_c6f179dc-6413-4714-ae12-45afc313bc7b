#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶颜色多样性分析部署程序 - 优化版本
基于Clean Code原则和SOLID设计模式的高性能实现
集成多线程处理、智能缓存、内存管理等优化技术
"""

import os
import sys
import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
import traceback
import time
import logging
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import List, Tuple, Dict, Optional, Any, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from functools import lru_cache, wraps
import weakref
import gc
import hashlib
from tqdm import tqdm

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    # print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

# ==================== 常量定义 ====================

# 颜色多样性分析配置
DEFAULT_SMALL_WIDTH = 100
DEFAULT_SMALL_HEIGHT = 100
DEFAULT_YEROU_THD = 0.2
DEFAULT_YEROU_RECT_AMT = 3
HIST_BINS = 16
HIST_RANGE = [0.0, 255.0]

# 忽略标签配置
DEFAULT_IGNORE_LABELS = [
    {"label_name": "canque", "label_type": "polygon", "thickness": 10},
    {"label_name": "zhumaizoushi", "label_type": "linestrip", "thickness": 10}
]

# ==================== 配置管理类 ====================

@dataclass
class OptimizedColorVarietyConfig:
    """优化颜色多样性分析配置类 - 遵循单一职责原则"""
    # 性能优化参数
    enable_parallel: bool = True
    max_workers: Optional[int] = None
    disable_memory_limit: bool = True
    
    # 缓存配置
    enable_cache: bool = True
    mask_cache_size: int = 5000
    hist_cache_size: int = 3000
    
    # I/O配置
    batch_size: int = 20
    enable_batch_processing: bool = True
    
    # 颜色分析配置
    small_width: int = DEFAULT_SMALL_WIDTH
    small_height: int = DEFAULT_SMALL_HEIGHT
    yerou_thd: float = DEFAULT_YEROU_THD
    yerou_rect_amt: int = DEFAULT_YEROU_RECT_AMT
    enable_vectorized_computation: bool = True
    enable_aggressive_optimization: bool = True
    
    def __post_init__(self):
        if self.max_workers is None:
            # 根据CPU核心数动态调整
            cpu_count = mp.cpu_count()
            if cpu_count <= 4:
                self.max_workers = cpu_count
            elif cpu_count <= 8:
                self.max_workers = cpu_count - 1
            else:
                self.max_workers = min(cpu_count - 2, 16)

# ==================== 异常处理类 ====================

class ColorVarietyProcessingError(Exception):
    """颜色多样性分析处理错误基类"""
    pass

class MaskGenerationError(ColorVarietyProcessingError):
    """掩码生成错误"""
    pass

class ColorAnalysisError(ColorVarietyProcessingError):
    """颜色分析错误"""
    pass

# ==================== 性能监控装饰器 ====================

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            logging.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}s")
            return result
        except Exception as e:
            end_time = time.time()
            logging.error(f"{func.__name__} 执行失败 (耗时: {end_time - start_time:.4f}s): {e}")
            raise
    return wrapper

def safe_execute(max_retries: int = 2):
    """安全执行装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        logging.error(f"{func.__name__} 最终失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        raise
                    logging.warning(f"{func.__name__} 尝试 {attempt + 1} 失败: {e}, 重试中...")
                    time.sleep(0.1 * (attempt + 1))
            return None
        return wrapper
    return decorator

# ==================== 内存管理类 ====================

class MemoryPool:
    """高性能内存池管理器"""
    
    def __init__(self, disable_limit: bool = True):
        self.disable_limit = disable_limit
        self.arrays = {}
        self.current_size = 0
        self._lock = threading.Lock()
        self._access_count = {}
    
    def get_array(self, shape: Tuple[int, ...], dtype=np.uint8) -> np.ndarray:
        """获取指定形状的数组"""
        if self.disable_limit:
            return np.empty(shape, dtype=dtype)
        
        with self._lock:
            key = (shape, dtype)
            if key not in self.arrays:
                self.arrays[key] = np.empty(shape, dtype=dtype)
                array_size = np.prod(shape) * np.dtype(dtype).itemsize
                self.current_size += array_size
                self._access_count[key] = 0
            
            self._access_count[key] += 1
            return self.arrays[key]
    
    def clear(self):
        """清空内存池"""
        if not self.disable_limit:
            with self._lock:
                self.arrays.clear()
                self.current_size = 0
                self._access_count.clear()
                gc.collect()

# ==================== 缓存管理类 ====================

class ThreadSafeCache:
    """高性能线程安全缓存类"""
    
    def __init__(self, max_size: int = 5000):
        self.cache = {}
        self.max_size = max_size
        self._lock = threading.Lock()
        self.access_count = {}
        self.hit_count = 0
        self.miss_count = 0
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key in self.cache:
                self.access_count[key] = self.access_count.get(key, 0) + 1
                self.hit_count += 1
                return self.cache[key]
            self.miss_count += 1
            return None
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self._lock:
            if len(self.cache) >= self.max_size:
                self._evict_batch()
            
            self.cache[key] = value
            self.access_count[key] = 1
    
    def _evict_batch(self):
        """批量淘汰缓存项"""
        if not self.cache:
            return
        
        evict_count = max(1, len(self.cache) // 4)
        sorted_items = sorted(self.access_count.items(), key=lambda x: x[1])
        
        for key, _ in sorted_items[:evict_count]:
            if key in self.cache:
                del self.cache[key]
                del self.access_count[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_rate": hit_rate,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count
            }
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_count.clear()
            self.hit_count = 0
            self.miss_count = 0

# 全局实例
memory_pool = MemoryPool(disable_limit=True)
mask_cache = ThreadSafeCache(max_size=5000)
hist_cache = ThreadSafeCache(max_size=3000)


# ==================== 优化的掩码生成器 ====================

class OptimizedMaskGenerator:
    """优化的掩码生成器 - 缓存和向量化优化"""

    def __init__(self, config: OptimizedColorVarietyConfig):
        self.config = config

    @performance_monitor
    def generate_mask(self, image: np.ndarray, json_path: str,
                     ignore_label_list: List[Dict], flag_show: bool = False) -> np.ndarray:
        """生成优化的掩码"""
        height, width = image.shape[:2]

        # 生成缓存键
        cache_key = self._generate_mask_cache_key(json_path, ignore_label_list, height, width)

        # 检查缓存
        if self.config.enable_cache:
            cached_mask = mask_cache.get(cache_key)
            if cached_mask is not None:
                return cached_mask

        # 生成基础掩码
        mask = self._create_base_mask(image, flag_show)

        # 应用忽略标签
        mask = self._apply_ignore_labels(mask, json_path, ignore_label_list, width, height)

        # 缓存结果
        if self.config.enable_cache:
            mask_cache.set(cache_key, mask.copy())

        return mask

    def _create_base_mask(self, image: np.ndarray, flag_show: bool = False) -> np.ndarray:
        """创建基础掩码"""
        gray_image = bf.cv2_gray(image)
        mask, _, _ = bf.make_shadow_mask(gray_image, flag_show=flag_show)
        return mask.astype("uint8")

    def _apply_ignore_labels(self, mask: np.ndarray, json_path: str,
                           ignore_label_list: List[Dict], width: int, height: int) -> np.ndarray:
        """应用忽略标签到掩码"""
        for item in ignore_label_list:
            try:
                label_list = bf.labelme_json_shape_read_point_onlyjson(
                    json_path, label_name=item["label_name"], shapes_name="shapes"
                )

                for label in label_list:
                    label_real = [[int(x[0] * width), int(x[1] * height)] for x in label]

                    if item["label_type"] == "polygon":
                        cv2.fillPoly(mask, [np.array(label_real)], 0)
                    elif item["label_type"] == "linestrip":
                        self._draw_linestrip(mask, label_real, item["thickness"])
                    elif item["label_type"] == "rectangle":
                        cv2.rectangle(mask, bf.arr_to_tuple(label_real[0]),
                                    bf.arr_to_tuple(label_real[1]), 0, -1)
            except Exception as e:
                logging.warning(f"处理忽略标签 {item['label_name']} 时出错: {e}")
                continue

        return mask

    def _draw_linestrip(self, mask: np.ndarray, label_real: List, thickness: int):
        """绘制线条"""
        for i in range(1, len(label_real)):
            cv2.line(mask, bf.arr_to_tuple(label_real[i - 1]),
                    bf.arr_to_tuple(label_real[i]), 0, thickness)

    def _generate_mask_cache_key(self, json_path: str, ignore_label_list: List[Dict],
                                height: int, width: int) -> str:
        """生成掩码缓存键"""
        # 使用文件修改时间和配置生成缓存键
        try:
            mtime = os.path.getmtime(json_path)
            key_data = f"{json_path}_{mtime}_{height}_{width}_{len(ignore_label_list)}"
            return hashlib.md5(key_data.encode()).hexdigest()
        except:
            return hashlib.md5(json_path.encode()).hexdigest()


# ==================== 优化的颜色分析器 ====================

class OptimizedColorAnalyzer:
    """优化的颜色分析器 - 向量化计算和并行处理"""

    def __init__(self, config: OptimizedColorVarietyConfig):
        self.config = config
        self.mask_generator = OptimizedMaskGenerator(config)

    @performance_monitor
    def analyze_color_variety(self, bmp_path: str, json_path: str,
                            ignore_label_list: List[Dict], flag_show: bool = False,
                            flag_rgb: bool = True) -> Tuple[List[float], List[float]]:
        """分析颜色多样性 - 优化版本"""
        try:
            # 读取图像
            image = bf.cv2_read_file(bmp_path)
            height, width, _ = bf.cv2_size(image)

            # 生成掩码
            mask = self.mask_generator.generate_mask(image, json_path, ignore_label_list, flag_show)

            if flag_show:
                self._show_debug_images(image, mask)

            # 向量化颜色分析
            if self.config.enable_vectorized_computation:
                return self._analyze_color_vectorized(image, mask, width, height, bmp_path, flag_show, flag_rgb)
            else:
                return self._analyze_color_traditional(image, mask, width, height, bmp_path, flag_show, flag_rgb)

        except Exception as e:
            logging.error(f"颜色分析失败: {bmp_path}, 错误: {e}")
            raise ColorAnalysisError(f"颜色分析失败: {e}")

    def _show_debug_images(self, image: np.ndarray, mask: np.ndarray):
        """显示调试图像"""
        image2 = cv2.bitwise_and(image, image, mask=mask)
        bf.show_cv2_img_screen_idx(image, name='orig image', idx=2, waitms=10, flag_resize_adapt=True)
        bf.show_cv2_img_screen_idx(image2, name='image_after_mask', idx=3, waitms=10, flag_resize_adapt=True)

    @performance_monitor
    def _analyze_color_vectorized(self, image: np.ndarray, mask: np.ndarray,
                                width: int, height: int, bmp_path: str,
                                flag_show: bool, flag_rgb: bool) -> Tuple[List[float], List[float]]:
        """向量化颜色分析"""
        small_width = self.config.small_width
        small_height = self.config.small_height
        yerou_thd = self.config.yerou_thd
        yerou_rect_amt = self.config.yerou_rect_amt

        # 预分配数组
        w_steps = range(0, width, small_width)
        h_steps = range(0, height, small_height)

        avg_color_lists = {
            'r': [],
            'g': [],
            'b': [],
            'gray': []
        }

        # 向量化处理每个区域
        for w in w_steps:
            color_temps = {'r': [], 'g': [], 'b': [], 'gray': []}
            valid_regions = 0

            for h in h_steps:
                # 提取小区域
                image_small = image[h:h + small_height, w:w + small_width]
                mask_small = mask[h:h + small_height, w:w + small_width]

                # 快速检查区域有效性
                mask_ratio = np.sum(mask_small) / (255 * mask_small.size)

                if mask_ratio > yerou_thd:
                    valid_regions += 1

                    # 向量化直方图计算
                    hist_results = self._calculate_histograms_vectorized(image_small, mask_small)

                    color_temps['r'].append(hist_results['r'])
                    color_temps['g'].append(hist_results['g'])
                    color_temps['b'].append(hist_results['b'])
                    color_temps['gray'].append(hist_results['gray'])

            # 计算平均值
            if valid_regions >= yerou_rect_amt:
                for color in ['r', 'g', 'b', 'gray']:
                    avg_value = bf.avg_list(color_temps[color]) if color_temps[color] else 0
                    avg_color_lists[color].append(avg_value)
            else:
                for color in ['r', 'g', 'b', 'gray']:
                    avg_color_lists[color].append(0)

        # 计算最终结果
        return self._calculate_final_results(avg_color_lists, bmp_path, flag_show, flag_rgb)

    @performance_monitor
    def _calculate_histograms_vectorized(self, image_small: np.ndarray,
                                       mask_small: np.ndarray) -> Dict[str, float]:
        """向量化直方图计算"""
        # 生成缓存键
        cache_key = hashlib.md5(image_small.tobytes() + mask_small.tobytes()).hexdigest()

        # 检查缓存
        if self.config.enable_cache:
            cached_result = hist_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

        # 计算直方图
        image_small_gray = bf.cv2_gray(image_small)

        # 并行计算所有通道的直方图
        hist_b = cv2.calcHist([image_small], [0], mask_small, [HIST_BINS], HIST_RANGE)
        hist_g = cv2.calcHist([image_small], [1], mask_small, [HIST_BINS], HIST_RANGE)
        hist_r = cv2.calcHist([image_small], [2], mask_small, [HIST_BINS], HIST_RANGE)
        hist_gray = cv2.calcHist([image_small_gray], [0], mask_small, [HIST_BINS], HIST_RANGE)

        # 向量化找到最大值索引
        result = {
            'r': np.argmax(hist_r) * 16,
            'g': np.argmax(hist_g) * 16,
            'b': np.argmax(hist_b) * 16,
            'gray': np.argmax(hist_gray) * 16
        }

        # 缓存结果
        if self.config.enable_cache:
            hist_cache.set(cache_key, result)

        return result

    def _analyze_color_traditional(self, image: np.ndarray, mask: np.ndarray,
                                 width: int, height: int, bmp_path: str,
                                 flag_show: bool, flag_rgb: bool) -> Tuple[List[float], List[float]]:
        """传统颜色分析方法 - 兼容性保证"""
        # 保持原有逻辑，但使用优化的数据结构
        small_width = self.config.small_width
        small_height = self.config.small_height
        yerou_thd = self.config.yerou_thd
        yerou_rect_amt = self.config.yerou_rect_amt

        avg_color_list_r = []
        avg_color_list_g = []
        avg_color_list_b = []
        avg_color_list_gray = []

        for w in range(0, width, small_width):
            avg_color_list_r_tmp = []
            avg_color_list_g_tmp = []
            avg_color_list_b_tmp = []
            avg_color_list_gray_tmp = []
            amt = 0

            for h in range(0, height, small_height):
                image_small = image[h:h + small_height, w:w + small_width]
                mask_small = mask[h:h + small_height, w:w + small_width]
                image_small_gray = bf.cv2_gray(image_small)
                mask_height, mask_width, _ = bf.cv2_size(mask_small)
                mask_sum = np.sum(mask_small) / 255

                if mask_sum / (mask_height * mask_width) > yerou_thd:
                    amt += 1
                    hist_results = self._calculate_histograms_vectorized(image_small, mask_small)

                    avg_color_list_r_tmp.append(hist_results['r'])
                    avg_color_list_g_tmp.append(hist_results['g'])
                    avg_color_list_b_tmp.append(hist_results['b'])
                    avg_color_list_gray_tmp.append(hist_results['gray'])

            if amt >= yerou_rect_amt:
                avg_color_list_r.append(bf.avg_list(avg_color_list_r_tmp) if avg_color_list_r_tmp else 0)
                avg_color_list_g.append(bf.avg_list(avg_color_list_g_tmp) if avg_color_list_g_tmp else 0)
                avg_color_list_b.append(bf.avg_list(avg_color_list_b_tmp) if avg_color_list_b_tmp else 0)
                avg_color_list_gray.append(bf.avg_list(avg_color_list_gray_tmp) if avg_color_list_gray_tmp else 0)
            else:
                avg_color_list_r.append(0)
                avg_color_list_g.append(0)
                avg_color_list_b.append(0)
                avg_color_list_gray.append(0)

        avg_color_lists = {
            'r': avg_color_list_r,
            'g': avg_color_list_g,
            'b': avg_color_list_b,
            'gray': avg_color_list_gray
        }

        return self._calculate_final_results(avg_color_lists, bmp_path, flag_show, flag_rgb)

    def _calculate_final_results(self, avg_color_lists: Dict[str, List[float]],
                               bmp_path: str, flag_show: bool, flag_rgb: bool) -> Tuple[List[float], List[float]]:
        """计算最终结果"""
        # 计算平均值
        averages = {}
        for color in ['r', 'g', 'b', 'gray']:
            non_zero_values = [x for x in avg_color_lists[color] if x > 0]
            averages[color] = bf.avg_list(non_zero_values) if non_zero_values else 0

        # 计算距离总和
        gray_dist_list = [abs(x - averages['gray']) for x in avg_color_lists['gray'] if x > 0]
        gray_dist_total = np.sum(gray_dist_list[2:-2]) if len(gray_dist_list) > 4 else np.sum(gray_dist_list)

        r_dist_total = np.sum([abs(x - averages['r']) for x in avg_color_lists['r'] if x > 0])
        g_dist_total = np.sum([abs(x - averages['g']) for x in avg_color_lists['g'] if x > 0])
        b_dist_total = np.sum([abs(x - averages['b']) for x in avg_color_lists['b'] if x > 0])

        color_rgbg_avg_list = [averages['r'], averages['g'], averages['b'], averages['gray']]
        color_rgbg_dist_total_list = [r_dist_total, g_dist_total, b_dist_total, gray_dist_total]

        # 计算均匀度
        junyundu = bf.cal_junyundu(gray_dist_total)

        print(f"{bf.get_file_name(bmp_path)} gray_dist_total={gray_dist_total} 均匀度={junyundu}")
        print(f"color_avg_list={color_rgbg_avg_list}")
        print(f"color_dist_total_list={color_rgbg_dist_total_list}")

        # 显示图表
        if flag_show:
            self._show_color_plot(avg_color_lists, averages, bmp_path, gray_dist_total, flag_rgb)

        return color_rgbg_avg_list, color_rgbg_dist_total_list

    def _show_color_plot(self, avg_color_lists: Dict[str, List[float]],
                        averages: Dict[str, float], bmp_path: str,
                        gray_dist_total: float, flag_rgb: bool):
        """显示颜色分析图表"""
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        filename = bf.get_file_name(bmp_path)

        plt.title(f"{filename}的颜色不均匀指标={int(gray_dist_total)}(值越大越不均匀)")
        plt.xlabel('从左到右的叶肉部位')
        plt.ylim(0, 200)
        plt.ylabel('颜色越淡值越大')

        gray_line = [averages['gray']] * len(avg_color_lists['gray'])
        zero_line = [0] * len(avg_color_lists['gray'])

        plt.plot(gray_line, label="GrayAVG", color="pink")

        if flag_rgb:
            plt.plot(avg_color_lists['r'], label="R", color="red")
            plt.plot(avg_color_lists['g'], label="G", color="green")
            plt.plot(avg_color_lists['b'], label="B", color="blue")

        plt.plot(avg_color_lists['gray'], label="Gray", color="gray")
        plt.fill_between(range(len(avg_color_lists['gray'])), gray_line, avg_color_lists['gray'],
                        where=np.array(avg_color_lists['gray']) > np.array(zero_line),
                        color="lightgray", interpolate=True)
        plt.legend(loc=0, ncol=1)
        plt.show()


# ==================== 优化的文件处理器 ====================

class OptimizedFileProcessor:
    """优化的文件处理器 - 批量处理和并行化"""

    def __init__(self, config: OptimizedColorVarietyConfig):
        self.config = config
        self.color_analyzer = OptimizedColorAnalyzer(config)

    @performance_monitor
    @safe_execute(max_retries=2)
    def process_single_file(self, bmp_path: str, json_path: str,
                          ignore_label_list: List[Dict]) -> Dict[str, float]:
        """处理单个文件"""
        try:
            # 分析颜色多样性
            color_rgbg_avg_list, color_rgbg_dist_total_list = self.color_analyzer.analyze_color_variety(
                bmp_path, json_path, ignore_label_list, flag_show=False, flag_rgb=False
            )

            # 构建用户特征
            user_feature = {
                "color_avg_total_red": color_rgbg_avg_list[0],
                "color_avg_total_green": color_rgbg_avg_list[1],
                "color_avg_total_blue": color_rgbg_avg_list[2],
                "color_avg_total_gray": color_rgbg_avg_list[3],
                "color_dist_total_red": color_rgbg_dist_total_list[0],
                "color_dist_total_green": color_rgbg_dist_total_list[1],
                "color_dist_total_blue": color_rgbg_dist_total_list[2],
                "color_dist_total_gray": color_rgbg_dist_total_list[3]
            }

            # 更新JSON文件
            bf.labelme_json_add_userfeature_file(json_path, user_feature)

            print(f"✅ 处理完成: {bf.get_file_name(bmp_path)}")

            return user_feature

        except Exception as e:
            logging.error(f"处理文件失败: {bmp_path}, {json_path}, 错误: {e}")
            raise

    @performance_monitor
    def process_directory_batch(self, image_root: str, json_root: str,
                               output_json_dir: Optional[str] = None) -> Dict[str, Any]:
        """批量处理目录"""
        try:
            # 扫描文件
            image_files, _ = bf.scan_files_2(image_root, except_midfix="mid", postfix="bmp")

            # 支持多种图像格式
            for ext in ['png', 'jpg', 'jpeg']:
                additional_files, _ = bf.scan_files_2(image_root, except_midfix="mid", postfix=ext)
                image_files.extend(additional_files)

            if not image_files:
                raise ColorVarietyProcessingError(f"未找到图像文件: {image_root}")

            print(f"📊 找到 {len(image_files)} 个图像文件")

            # 文件配对
            file_pairs = self._pair_files(image_files, json_root, output_json_dir)

            if not file_pairs:
                raise ColorVarietyProcessingError("未找到匹配的文件对")

            print(f"📋 配对成功 {len(file_pairs)} 个文件对")

            # 处理文件
            if self.config.enable_parallel and len(file_pairs) > 1:
                return self._process_files_parallel(file_pairs)
            else:
                return self._process_files_sequential(file_pairs)

        except Exception as e:
            logging.error(f"批量处理目录失败: {e}")
            raise

    def _pair_files(self, image_files: List[str], json_root: str,
                   output_json_dir: Optional[str]) -> List[Tuple[str, str]]:
        """配对图像和JSON文件"""
        file_pairs = []
        ignore_label_list = DEFAULT_IGNORE_LABELS

        for image_path in image_files:
            base_name = bf.get_file_name(image_path)
            json_file = bf.rename_add_post(base_name, post="json")
            json_path = bf.pathjoin(json_root, json_file)

            if bf.fileexist(json_path):
                # 如果有输出目录，复制JSON文件
                if output_json_dir:
                    output_json_path = bf.pathjoin(output_json_dir, json_file)
                    if not bf.fileexist(output_json_path):
                        import shutil
                        shutil.copy2(json_path, output_json_path)
                    json_path = output_json_path

                file_pairs.append((image_path, json_path))

        return file_pairs

    @performance_monitor
    def _process_files_parallel(self, file_pairs: List[Tuple[str, str]]) -> Dict[str, Any]:
        """并行处理文件"""
        # 智能线程数调整
        file_count = len(file_pairs)
        if file_count <= 5:
            actual_workers = min(file_count, 4)
        elif file_count <= 20:
            actual_workers = min(file_count, self.config.max_workers // 2)
        else:
            actual_workers = self.config.max_workers

        print(f"🚀 使用 {actual_workers} 个线程并行处理 {file_count} 个文件")

        results = {
            "success_count": 0,
            "error_count": 0,
            "processed_files": []
        }

        ignore_label_list = DEFAULT_IGNORE_LABELS

        with ThreadPoolExecutor(max_workers=actual_workers) as executor:
            # 提交任务
            future_to_files = {
                executor.submit(self.process_single_file, img_path, json_path, ignore_label_list): (img_path, json_path)
                for img_path, json_path in file_pairs
            }

            # 收集结果
            for future in tqdm(future_to_files, desc="并行处理颜色多样性"):
                img_path, json_path = future_to_files[future]
                try:
                    result = future.result(timeout=300)  # 5分钟超时

                    results["success_count"] += 1
                    results["processed_files"].append({
                        "image": bf.get_file_name(img_path),
                        "features": result
                    })

                    # 高性能模式下跳过内存清理
                    if not self.config.disable_memory_limit:
                        self._smart_memory_cleanup()

                except Exception as e:
                    results["error_count"] += 1
                    logging.error(f"并行处理失败: {img_path}, {json_path}, 错误: {e}")

        return results

    @performance_monitor
    def _process_files_sequential(self, file_pairs: List[Tuple[str, str]]) -> Dict[str, Any]:
        """串行处理文件"""
        print("🔄 使用串行方式处理文件")

        results = {
            "success_count": 0,
            "error_count": 0,
            "processed_files": []
        }

        ignore_label_list = DEFAULT_IGNORE_LABELS

        for img_path, json_path in tqdm(file_pairs, desc="串行处理颜色多样性"):
            try:
                result = self.process_single_file(img_path, json_path, ignore_label_list)

                results["success_count"] += 1
                results["processed_files"].append({
                    "image": bf.get_file_name(img_path),
                    "features": result
                })

                # 高性能模式下跳过内存清理
                if not self.config.disable_memory_limit:
                    self._smart_memory_cleanup()

            except Exception as e:
                results["error_count"] += 1
                logging.error(f"串行处理失败: {img_path}, {json_path}, 错误: {e}")

        return results

    def _smart_memory_cleanup(self):
        """智能内存清理机制"""
        if self.config.disable_memory_limit:
            return

        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            # 只在内存使用极高时才清理
            memory_threshold = 8192  # 8GB阈值
            if memory_mb > memory_threshold:
                # 部分清理缓存
                if len(mask_cache.cache) > self.config.mask_cache_size * 0.8:
                    mask_cache._evict_batch()
                if len(hist_cache.cache) > self.config.hist_cache_size * 0.8:
                    hist_cache._evict_batch()

                logging.info(f"内存使用: {memory_mb:.2f}MB")
        except ImportError:
            pass


# ==================== 主要API函数 ====================

def run_color_variety_main(input_image_dir: str, input_json_dir: str,
                          output_json_dir: Optional[str] = None) -> bool:
    """
    烟叶颜色多样性分析主函数 - 优化版本

    Args:
        input_image_dir: 输入图像目录路径
        input_json_dir: 输入JSON文件目录路径
        output_json_dir: 输出JSON文件目录路径，如果为None则输出到input_json_dir

    Returns:
        bool: 处理是否成功
    """
    try:
        print("🚀 开始烟叶颜色多样性分析处理（优化版本）...")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输出JSON目录: {output_json_dir or input_json_dir}")

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入图像目录不存在: {input_image_dir}")
            return False

        if not os.path.exists(input_json_dir):
            print(f"❌ 输入JSON目录不存在: {input_json_dir}")
            return False

        # 创建输出目录
        if output_json_dir and not os.path.exists(output_json_dir):
            os.makedirs(output_json_dir, exist_ok=True)
            print(f"✅ 创建输出目录: {output_json_dir}")

        # 创建优化配置
        config = create_optimized_config()

        # 创建文件处理器
        processor = OptimizedFileProcessor(config)

        # 执行批量处理
        start_time = time.time()
        results = processor.process_directory_batch(
            image_root=input_image_dir,
            json_root=input_json_dir,
            output_json_dir=output_json_dir
        )
        end_time = time.time()

        # 显示结果
        print(f"\n📊 颜色多样性分析处理完成:")
        print(f"✅ 成功: {results['success_count']} 个文件")
        print(f"❌ 失败: {results['error_count']} 个文件")
        print(f"⏱️  处理耗时: {end_time - start_time:.2f}秒")

        return results['success_count'] > 0

    except Exception as e:
        print(f"❌ 烟叶颜色多样性分析处理异常: {e}")
        traceback.print_exc()
        return False


# ==================== 兼容性函数 ====================

def do_one(bmp_path: str, json_path: str, ignore_label_list: List[Dict],
          small_width: int = DEFAULT_SMALL_WIDTH, small_height: int = DEFAULT_SMALL_HEIGHT,
          yerou_thd: float = DEFAULT_YEROU_THD, yerou_rect_amt: int = DEFAULT_YEROU_RECT_AMT,
          flag_show: bool = False, flag_rgb: bool = True) -> Tuple[List[float], List[float]]:
    """兼容性函数 - 保持原有接口"""
    config = OptimizedColorVarietyConfig(
        small_width=small_width,
        small_height=small_height,
        yerou_thd=yerou_thd,
        yerou_rect_amt=yerou_rect_amt
    )
    analyzer = OptimizedColorAnalyzer(config)
    return analyzer.analyze_color_variety(bmp_path, json_path, ignore_label_list, flag_show, flag_rgb)


def do_one_write_json(bmp_path: str, json_path: str, ignore_label_list: List[Dict]):
    """兼容性函数 - 保持原有接口"""
    config = OptimizedColorVarietyConfig()
    processor = OptimizedFileProcessor(config)
    processor.process_single_file(bmp_path, json_path, ignore_label_list)


def do_dir(image_root: str, json_root: str):
    """兼容性函数 - 保持原有接口"""
    return run_color_variety_main(
        input_image_dir=image_root,
        input_json_dir=json_root,
        output_json_dir=None
    )


# ==================== 配置创建函数 ====================

def create_optimized_config() -> OptimizedColorVarietyConfig:
    """创建高性能优化配置"""
    cpu_count = mp.cpu_count()

    print(f"🔧 检测到 {cpu_count} 个CPU核心")

    # 高性能配置：使用更多线程
    if cpu_count <= 4:
        max_workers = cpu_count
    elif cpu_count <= 8:
        max_workers = cpu_count - 1
    else:
        max_workers = min(cpu_count - 2, 16)

    print(f"🚀 配置线程数: {max_workers}")

    return OptimizedColorVarietyConfig(
        enable_parallel=True,
        max_workers=max_workers,
        disable_memory_limit=True,
        enable_cache=True,
        mask_cache_size=5000,
        hist_cache_size=3000,
        batch_size=20,
        enable_batch_processing=True,
        enable_vectorized_computation=True,
        enable_aggressive_optimization=True
    )


@performance_monitor
def main():
    """优化的主函数"""
    try:
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('color_variety_optimized.log'),
                logging.StreamHandler()
            ]
        )

        # 创建优化配置
        config = create_optimized_config()

        # 更新全局实例
        global memory_pool, mask_cache, hist_cache
        memory_pool = MemoryPool(disable_limit=config.disable_memory_limit)
        mask_cache = ThreadSafeCache(max_size=config.mask_cache_size)
        hist_cache = ThreadSafeCache(max_size=config.hist_cache_size)

        print("🧪 烟叶颜色多样性分析部署程序（高性能优化版本）")
        print("=" * 60)
        print(f"📊 配置信息:")
        print(f"   并行处理: {'启用' if config.enable_parallel else '禁用'}")
        print(f"   最大工作线程: {config.max_workers}")
        print(f"   缓存: {'启用' if config.enable_cache else '禁用'}")
        print(f"   掩码缓存大小: {config.mask_cache_size}")
        print(f"   直方图缓存大小: {config.hist_cache_size}")
        print(f"   内存限制: {'禁用' if config.disable_memory_limit else '启用'}")
        print(f"   批处理大小: {config.batch_size}")
        print(f"   向量化计算: {'启用' if config.enable_vectorized_computation else '禁用'}")
        print(f"   激进优化: {'启用' if config.enable_aggressive_optimization else '禁用'}")

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_json_dir = os.path.join(current_dir, '..', 'sum_yanye_user_feature', 'test_output')
        input_image_dir = os.path.join(current_dir, '..', 'yanye_user_feature', 'vis')
        output_json_dir = os.path.join(current_dir, 'test_output')

        # 路径标准化
        input_json_dir = os.path.abspath(input_json_dir)
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输出目录: {output_json_dir}")

        start_time = time.time()

        # 运行主处理函数
        success = run_color_variety_main(
            input_image_dir=input_image_dir,
            input_json_dir=input_json_dir,
            output_json_dir=output_json_dir
        )

        end_time = time.time()
        total_time = end_time - start_time

        if success:
            print("\n🎉 烟叶颜色多样性分析处理完成！")
            print(f"⏱️  总耗时: {total_time:.2f}秒")

            # 显示性能统计
            _show_performance_stats()

            return True
        else:
            print("\n💥 烟叶颜色多样性分析处理失败！")
            return False

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        traceback.print_exc()
        return False


def _show_performance_stats():
    """显示性能统计信息"""
    try:
        print("\n📊 性能统计:")

        # 缓存统计
        mask_stats = mask_cache.get_stats()
        hist_stats = hist_cache.get_stats()

        print(f"   掩码缓存: {mask_stats['size']}/{mask_stats['max_size']} (命中率: {mask_stats['hit_rate']:.2%})")
        print(f"   直方图缓存: {hist_stats['size']}/{hist_stats['max_size']} (命中率: {hist_stats['hit_rate']:.2%})")
        print(f"   内存池: 高性能模式 (无限制)")

        print("🚀 高性能模式: 跳过内存清理以保持最佳性能")

    except Exception as e:
        logging.warning(f"性能统计显示失败: {e}")


if __name__ == "__main__":
    print("🚀 烟叶颜色多样性分析 - 多线程优化版本")
    print("=" * 50)

    success = main()
    if success:
        print("🎉 程序执行成功！")
    else:
        print("💥 程序执行失败！")
        sys.exit(1)
