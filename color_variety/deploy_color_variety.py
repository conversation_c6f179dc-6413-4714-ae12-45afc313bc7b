#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶颜色多样性分析部署程序
从start_predict.py中独立出来的run_color_variety模块
保持原有推理逻辑不变，直接import调用base_function_for_test3中的处理方法
"""

import os
import sys
import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
import multiprocessing
import traceback

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

def do_one(bmp_path, json_path, ignore_label_list, small_width=100, small_height=100, yerou_thd=0.2, yerou_rect_amt=3,
           flag_show=False, flag_rgb=True):
    image = bf.cv2_read_file(bmp_path)
    height, width, _ = bf.cv2_size(image)
    # mask = get_mask(bmp_path)
    mask, _, _ = bf.make_shadow_mask(bf.cv2_gray(image), flag_show=flag_show)
    # print('mask={}'.format(mask))
    mask = mask.astype("uint8")
    # mask = np.zeros(image.shape[:2], dtype="uint8")
    # binary = cal_binary_image2(image)
    # binary = bf.cv2_erode(binary, iterations=0)
    # edge, _ = get_edge_canques(binary, False)
    # cv2.fillPoly(mask, [edge], 255)
    for item in ignore_label_list:
        label_list = bf.labelme_json_shape_read_point_onlyjson(json_path, label_name=item["label_name"],
                                                               shapes_name="shapes")
        for label in label_list:
            label_real = [[int(x[0] * width), int(x[1] * height)] for x in label]
            if item["label_type"] == "polygon":
                cv2.fillPoly(mask, [np.array(label_real)], 0)
            elif item["label_type"] == "linestrip":
                for i in range(1, len(label_real)):
                    cv2.line(mask, bf.arr_to_tuple(label_real[i - 1]), bf.arr_to_tuple(label_real[i]), 0,
                             item["thickness"])
            elif item["label_type"] == "rectangle":
                cv2.rectangle(mask, bf.arr_to_tuple(label_real[0]), bf.arr_to_tuple(label_real[1]), 0, -1)
    if flag_show:
        image2 = cv2.bitwise_and(image, image, mask=mask)
        bf.show_cv2_img_screen_idx(image, name='orig image', idx=2, waitms=10, flag_resize_adapt=True)
        bf.show_cv2_img_screen_idx(image2, name='image_after_mask', idx=3, waitms=10, flag_resize_adapt=True)
    avg_color_list_r = []
    avg_color_list_g = []
    avg_color_list_b = []
    avg_color_list_gray = []
    for w in range(0, width, small_width):
        avg_color_list_r_tmp = []
        avg_color_list_g_tmp = []
        avg_color_list_b_tmp = []
        avg_color_list_gray_tmp = []
        amt = 0
        for h in range(0, height, small_height):
            image_small = image[h: h + small_height, w: w + small_width]
            mask_small = mask[h: h + small_height, w: w + small_width]
            image_small_gray = bf.cv2_gray(image_small)
            mask_height, mask_width, _ = bf.cv2_size(mask_small)
            mask_sum = np.sum(mask_small) / 255
            if mask_sum / (mask_height * mask_width) > yerou_thd:
                amt += 1
                hist_list_b = cv2.calcHist([image_small], [2], mask_small, [16], [0.0, 255.0])
                hist_list_b = [x[0] for x in hist_list_b]
                hist_list_g = cv2.calcHist([image_small], [1], mask_small, [16], [0.0, 255.0])
                hist_list_g = [x[0] for x in hist_list_g]
                hist_list_r = cv2.calcHist([image_small], [0], mask_small, [16], [0.0, 255.0])
                hist_list_r = [x[0] for x in hist_list_r]
                hist_list_gray = cv2.calcHist([image_small_gray], [0], mask_small, [16], [0.0, 255.0])
                hist_list_gray = [x[0] for x in hist_list_gray]
                hist_b = hist_list_b.index(max(hist_list_b)) * 16
                hist_g = hist_list_g.index(max(hist_list_g)) * 16
                hist_r = hist_list_r.index(max(hist_list_r)) * 16
                hist_gray = hist_list_gray.index(max(hist_list_gray)) * 16
                avg_color_list_r_tmp.append(hist_b)
                avg_color_list_g_tmp.append(hist_g)
                avg_color_list_b_tmp.append(hist_r)
                avg_color_list_gray_tmp.append(hist_gray)
                # avg_color_list_gray_tmp.append(hist_r * 0.299 + hist_g * 0.587 + hist_b * 0.114)
        if amt >= yerou_rect_amt:
            avg_color_list_r.append(bf.avg_list(avg_color_list_r_tmp) if len(avg_color_list_r_tmp) > 0 else 0)
            avg_color_list_g.append(bf.avg_list(avg_color_list_g_tmp) if len(avg_color_list_g_tmp) > 0 else 0)
            avg_color_list_b.append(bf.avg_list(avg_color_list_b_tmp) if len(avg_color_list_b_tmp) > 0 else 0)
            avg_color_list_gray.append(bf.avg_list(avg_color_list_gray_tmp) if len(avg_color_list_gray_tmp) > 0 else 0)
        else:
            avg_color_list_r.append(0)
            avg_color_list_g.append(0)
            avg_color_list_b.append(0)
            avg_color_list_gray.append(0)
    if np.count_nonzero(avg_color_list_gray) != 0:
        gray_avg = bf.avg_list([x for x in avg_color_list_gray if x > 0])
    else:
        gray_avg = 0
    if np.count_nonzero(avg_color_list_r) != 0:
        r_avg = bf.avg_list([x for x in avg_color_list_r if x > 0])
    else:
        r_avg = 0
    if np.count_nonzero(avg_color_list_g) != 0:
        g_avg = bf.avg_list([x for x in avg_color_list_g if x > 0])
    else:
        g_avg = 0
    if np.count_nonzero(avg_color_list_b) != 0:
        b_avg = bf.avg_list([x for x in avg_color_list_b if x > 0])
    else:
        b_avg = 0
    gray_line = []
    zero_line = []
    for i in range(len(avg_color_list_gray)):
        gray_line.append(gray_avg)
        zero_line.append(0)
    gray_dist_list = [abs(x - gray_avg) for x in avg_color_list_gray if x > 0]
    gray_dist_total = np.sum(gray_dist_list[2:-2])
    r_dist_total = np.sum([abs(x - r_avg) for x in avg_color_list_r if x > 0])
    g_dist_total = np.sum([abs(x - g_avg) for x in avg_color_list_g if x > 0])
    b_dist_total = np.sum([abs(x - b_avg) for x in avg_color_list_b if x > 0])
    color_rgbg_avg_list = [r_avg, g_avg, b_avg, gray_avg]
    color_rgbg_dist_total_list = [r_dist_total, g_dist_total, b_dist_total, gray_dist_total]
    junyundu = bf.cal_junyundu(gray_dist_total)
    print("{} gray_dist_total={} 均匀度={}".format(bf.get_file_name(bmp_path), gray_dist_total, junyundu))
    print("color_avg_list={}".format(color_rgbg_avg_list))
    print("color_dist_total_list={}".format(color_rgbg_dist_total_list))
    if flag_show:
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        filename = bf.get_file_name(bmp_path)
        plt.title(u"{}的颜色不均匀指标={}(值越大越不均匀)".format(filename, int(gray_dist_total)))
        plt.xlabel('从左到右的叶肉部位')
        plt.ylim(0, 200)
        plt.ylabel('颜色越淡值越大')
        plt.plot(gray_line, label="GrayAVG", color="pink")
        if flag_rgb:
            plt.plot(avg_color_list_r, label="R", color="red")
            plt.plot(avg_color_list_g, label="G", color="green")
            plt.plot(avg_color_list_b, label="B", color="blue")
        plt.plot(avg_color_list_gray, label="Gray", color="gray")
        plt.fill_between(range(len(avg_color_list_gray)), gray_line, avg_color_list_gray,
                         where=np.array(avg_color_list_gray) > np.array(zero_line), color="lightgray", interpolate=True)
        plt.legend(loc=0, ncol=1)
        plt.show()

    return color_rgbg_avg_list, color_rgbg_dist_total_list

def do_one_write_json(bmp_path, json_path, ignore_label_list):
    color_rgbg_avg_list, color_rgbg_dist_total_list = do_one(bmp_path, json_path, ignore_label_list, flag_show=False,
                                                             flag_rgb=False)
    user_feature = {"color_avg_total_red": color_rgbg_avg_list[0],
                    "color_avg_total_green": color_rgbg_avg_list[1],
                    "color_avg_total_blue": color_rgbg_avg_list[2],
                    "color_avg_total_gray": color_rgbg_avg_list[3],
                    "color_dist_total_red": color_rgbg_dist_total_list[0],
                    "color_dist_total_green": color_rgbg_dist_total_list[1],
                    "color_dist_total_blue": color_rgbg_dist_total_list[2],
                    "color_dist_total_gray": color_rgbg_dist_total_list[3]}
    bf.labelme_json_add_userfeature_file(json_path, user_feature)
    print("finish={}".format(bmp_path))

def do_dir(image_root, json_root):
    """
    批量处理目录中的图像文件
    """
    # 定义忽略标签列表 - 保持原始颜色多样性分析的标签配置
    ignore_label_list = [
        {"label_name": "canque", "label_type": "polygon", "thickness": 10},
        {"label_name": "zhumaizoushi", "label_type": "linestrip", "thickness": 10}
    ]

    image_list, _ = bf.scan_files_2(image_root, except_midfix="mid", postfix="bmp")
    pool = multiprocessing.Pool(30)
    for image_path in image_list:
        json_path = bf.pathjoin(json_root, bf.rename_add_post(bf.get_file_name(image_path), post="json"))
        pool.apply_async(do_one_write_json, (image_path, json_path, ignore_label_list))
    pool.close()
    pool.join()


def run_color_variety_main(input_image_dir, input_json_dir, output_json_dir=None):
    """
    烟叶颜色多样性分析主函数

    Args:
        input_image_dir: 输入图像目录路径
        input_json_dir: 输入JSON文件目录路径
        output_json_dir: 输出JSON文件目录路径，如果为None则输出到input_json_dir

    Returns:
        bool: 处理是否成功
    """
    try:
        print("🚀 开始烟叶颜色多样性分析处理...")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输出JSON目录: {output_json_dir or input_json_dir}")

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入图像目录不存在: {input_image_dir}")
            return False

        if not os.path.exists(input_json_dir):
            print(f"❌ 输入JSON目录不存在: {input_json_dir}")
            return False

        # 创建输出目录
        if output_json_dir and not os.path.exists(output_json_dir):
            os.makedirs(output_json_dir, exist_ok=True)
            print(f"✅ 创建输出目录: {output_json_dir}")

        # 获取图像文件列表
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        print(f"📊 找到 {len(image_files)} 个图像文件")

        # 定义忽略标签列表 - 保持原始颜色多样性分析的标签配置
        ignore_label_list = [
            {"label_name": "canque", "label_type": "polygon", "thickness": 10},
            {"label_name": "zhumaizoushi", "label_type": "linestrip", "thickness": 10}
        ]

        # 处理每个图像文件
        success_count = 0
        error_count = 0

        for image_file in image_files:
            try:
                image_path = os.path.join(input_image_dir, image_file)
                print(f"\n🔄 处理文件: {image_file}")

                # 检查对应的JSON文件是否存在
                json_file = os.path.splitext(image_file)[0] + '.json'
                input_json_path = os.path.join(input_json_dir, json_file)

                if not os.path.exists(input_json_path):
                    print(f"⚠️  跳过: 未找到对应的JSON文件 {json_file}")
                    continue

                # 如果有输出目录，先复制JSON文件到输出目录
                if output_json_dir:
                    output_json_path = os.path.join(output_json_dir, json_file)
                    if not os.path.exists(output_json_path):
                        import shutil
                        shutil.copy2(input_json_path, output_json_path)
                        print(f"📋 复制JSON文件: {json_file}")
                    json_path_to_use = output_json_path
                else:
                    json_path_to_use = input_json_path

                # 处理单个文件
                do_one_write_json(image_path, json_path_to_use, ignore_label_list)
                success_count += 1
                print(f"✅ 处理成功: {image_file}")

            except Exception as e:
                error_count += 1
                print(f"❌ 处理失败: {image_file} - {e}")
                traceback.print_exc()

        print(f"\n📊 处理完成:")
        print(f"✅ 成功: {success_count} 个文件")
        print(f"❌ 失败: {error_count} 个文件")

        return success_count > 0

    except Exception as e:
        print(f"❌ 烟叶颜色多样性分析处理异常: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_json_dir = os.path.join(current_dir, '..', 'sum_yanye_user_feature', 'test_output')
        input_image_dir = os.path.join(current_dir, '..', 'yanye_user_feature', 'vis')
        output_json_dir = os.path.join(current_dir, 'test_output')

        # 路径标准化
        input_json_dir = os.path.abspath(input_json_dir)
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        print("🧪 烟叶颜色多样性分析部署程序")
        print("="*60)

        # 运行主处理函数
        success = run_color_variety_main(
            input_image_dir=input_image_dir,
            input_json_dir=input_json_dir,
            output_json_dir=output_json_dir
        )

        if success:
            print("\n🎉 烟叶颜色多样性分析处理完成！")
            return True
        else:
            print("\n💥 烟叶颜色多样性分析处理失败！")
            return False

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)