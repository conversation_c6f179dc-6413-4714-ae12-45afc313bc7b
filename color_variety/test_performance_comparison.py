#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色多样性分析性能对比测试脚本
比较原版本和优化版本的性能差异
"""

import os
import sys
import time
import subprocess
import json
from typing import Dict, Any

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

def run_original_version() -> Dict[str, Any]:
    """运行原版本"""
    print("🔄 运行原版本...")
    start_time = time.time()
    
    try:
        # 运行原版本
        result = subprocess.run([
            'python', 'color_variety/deploy_color_variety.py'
        ], capture_output=True, text=True, timeout=600)
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "version": "原版本",
            "duration": duration,
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr
        }
    except subprocess.TimeoutExpired:
        return {
            "version": "原版本",
            "duration": 600,
            "success": False,
            "output": "",
            "error": "超时"
        }
    except Exception as e:
        return {
            "version": "原版本",
            "duration": time.time() - start_time,
            "success": False,
            "output": "",
            "error": str(e)
        }

def run_optimized_version() -> Dict[str, Any]:
    """运行优化版本"""
    print("🚀 运行优化版本...")
    start_time = time.time()
    
    try:
        # 运行优化版本
        result = subprocess.run([
            'python', 'color_variety/deploy_color_variety_optimized.py'
        ], capture_output=True, text=True, timeout=600)
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            "version": "优化版本",
            "duration": duration,
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr
        }
    except subprocess.TimeoutExpired:
        return {
            "version": "优化版本",
            "duration": 600,
            "success": False,
            "output": "",
            "error": "超时"
        }
    except Exception as e:
        return {
            "version": "优化版本",
            "duration": time.time() - start_time,
            "success": False,
            "output": "",
            "error": str(e)
        }

def extract_statistics(output: str) -> Dict[str, Any]:
    """从输出中提取统计信息"""
    stats = {
        "success_files": 0,
        "error_files": 0,
        "processing_time": 0.0,
        "total_time": 0.0
    }
    
    lines = output.split('\n')
    for line in lines:
        if "成功:" in line and "个文件" in line:
            try:
                stats["success_files"] = int(line.split("成功:")[1].split("个文件")[0].strip())
            except:
                pass
        elif "失败:" in line and "个文件" in line:
            try:
                stats["error_files"] = int(line.split("失败:")[1].split("个文件")[0].strip())
            except:
                pass
        elif "处理耗时:" in line:
            try:
                stats["processing_time"] = float(line.split("处理耗时:")[1].split("秒")[0].strip())
            except:
                pass
        elif "总耗时:" in line:
            try:
                stats["total_time"] = float(line.split("总耗时:")[1].split("秒")[0].strip())
            except:
                pass
    
    return stats

def compare_results(original_result: Dict[str, Any], optimized_result: Dict[str, Any]):
    """比较结果"""
    print("\n" + "="*60)
    print("📊 颜色多样性分析性能对比结果")
    print("="*60)
    
    # 提取统计信息
    original_stats = extract_statistics(original_result["output"])
    optimized_stats = extract_statistics(optimized_result["output"])
    
    print(f"\n🔍 执行状态:")
    print(f"   原版本: {'✅ 成功' if original_result['success'] else '❌ 失败'}")
    print(f"   优化版本: {'✅ 成功' if optimized_result['success'] else '❌ 失败'}")
    
    print(f"\n⏱️  执行时间:")
    print(f"   原版本: {original_result['duration']:.2f}秒")
    print(f"   优化版本: {optimized_result['duration']:.2f}秒")
    
    if original_result['duration'] > 0 and optimized_result['duration'] > 0:
        speedup = original_result['duration'] / optimized_result['duration']
        print(f"   性能提升: {speedup:.2f}x")
    
    print(f"\n📋 处理结果:")
    print(f"   原版本 - 成功: {original_stats['success_files']}, 失败: {original_stats['error_files']}")
    print(f"   优化版本 - 成功: {optimized_stats['success_files']}, 失败: {optimized_stats['error_files']}")
    
    print(f"\n⚡ 处理性能:")
    if original_stats['processing_time'] > 0 and optimized_stats['processing_time'] > 0:
        processing_speedup = original_stats['processing_time'] / optimized_stats['processing_time']
        print(f"   原版本处理时间: {original_stats['processing_time']:.2f}秒")
        print(f"   优化版本处理时间: {optimized_stats['processing_time']:.2f}秒")
        print(f"   处理性能提升: {processing_speedup:.2f}x")
    
    # 检查结果一致性
    if (original_stats['success_files'] == optimized_stats['success_files'] and 
        original_stats['error_files'] == optimized_stats['error_files']):
        print(f"\n✅ 结果一致性: 两个版本的处理结果完全一致")
    else:
        print(f"\n⚠️  结果一致性: 两个版本的处理结果存在差异")
    
    # 保存详细结果
    detailed_results = {
        "comparison_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "original": {
            "result": original_result,
            "stats": original_stats
        },
        "optimized": {
            "result": optimized_result,
            "stats": optimized_stats
        }
    }
    
    with open("color_variety/performance_comparison_results.json", "w", encoding="utf-8") as f:
        json.dump(detailed_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: color_variety/performance_comparison_results.json")

def main():
    """主函数"""
    print("🧪 烟叶颜色多样性分析性能对比测试")
    print("="*60)
    
    # 清理之前的输出
    output_dirs = [
        "color_variety/test_output"
    ]
    
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            import shutil
            shutil.rmtree(output_dir)
            print(f"🧹 清理输出目录: {output_dir}")
    
    # 运行两个版本
    original_result = run_original_version()
    
    # 清理输出目录，为优化版本准备
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            import shutil
            shutil.rmtree(output_dir)
    
    optimized_result = run_optimized_version()
    
    # 比较结果
    compare_results(original_result, optimized_result)

if __name__ == "__main__":
    main()
