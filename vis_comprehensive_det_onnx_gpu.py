#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合检测可视化程序
对综合检测结果进行高级可视化分析

作者: 系统架构师
日期: 2025-01-27
版本: 1.0.0
"""

import os
import sys
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from comprehensive_detection.visualizer import ComprehensiveVisualizer
    print("🔥🔥🔥 这是综合检测可视化程序 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入可视化模块 {e}")
    sys.exit(1)


class AdvancedVisualizer:
    """高级可视化器 - 提供多种可视化分析功能"""
    
    def __init__(self):
        self.base_visualizer = ComprehensiveVisualizer()
        
        # 扩展的模块颜色映射
        self.module_colors = {
            'zhumaidakai': (255, 0, 0),      # 红色
            'zhumaizoushi': (255, 128, 0),   # 橙色
            'zheheng': (255, 255, 0),        # 黄色
            'zhimai': (128, 255, 0),         # 黄绿色
            'zhimaiqing': (0, 255, 0),       # 绿色
            'lunkuo_canque_fill': (0, 255, 128),  # 青绿色
            'jiaodian': (0, 255, 255),       # 青色
            'hengwen': (0, 128, 255),        # 浅蓝色
            'kaohong': (0, 0, 255),          # 蓝色
            'guahui': (128, 0, 255),         # 紫色
            'zhousuo': (255, 0, 255),        # 品红色
            'chaohong': (255, 0, 128),       # 粉红色
        }
    
    def create_module_comparison_grid(self, json_files: List[str], output_path: str):
        """创建模块对比网格图"""
        print("🎨 创建模块对比网格图...")
        
        if not json_files:
            print("❌ 没有JSON文件可供可视化")
            return False
        
        try:
            # 读取第一个JSON文件获取基本信息
            with open(json_files[0], 'r', encoding='utf-8') as f:
                sample_data = json.load(f)
            
            # 统计所有模块的检测数量
            all_module_stats = {}
            for json_file in json_files:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                image_name = data.get('imagePath', os.path.basename(json_file))
                detection_summary = data.get('detection_summary', {})
                module_counts = detection_summary.get('module_counts', {})
                
                all_module_stats[image_name] = module_counts
            
            # 创建统计图表
            self._create_statistics_chart(all_module_stats, output_path)
            
            return True
            
        except Exception as e:
            print(f"❌ 创建模块对比网格图失败: {e}")
            return False
    
    def _create_statistics_chart(self, module_stats: Dict, output_path: str):
        """创建统计图表"""
        
        # 创建一个大的画布
        chart_width = 1200
        chart_height = 800
        chart = np.ones((chart_height, chart_width, 3), dtype=np.uint8) * 255
        
        # 标题
        title = "Comprehensive Detection Module Statistics"
        cv2.putText(chart, title, (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
        
        # 获取所有模块名称
        all_modules = list(self.module_colors.keys())
        
        # 绘制图例
        legend_x = 50
        legend_y = 100
        
        cv2.putText(chart, "Modules:", (legend_x, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        for i, module_name in enumerate(all_modules):
            y_pos = legend_y + 30 + i * 25
            color = self.module_colors[module_name]
            
            # 绘制颜色块
            cv2.rectangle(chart, (legend_x, y_pos - 10), (legend_x + 20, y_pos + 10), color, -1)
            
            # 绘制模块名称
            cv2.putText(chart, module_name, (legend_x + 30, y_pos + 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        # 绘制统计表格
        table_x = 400
        table_y = 100
        
        cv2.putText(chart, "Detection Counts by Image:", (table_x, table_y), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        # 表头
        header_y = table_y + 40
        cv2.putText(chart, "Image", (table_x, header_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
        cv2.putText(chart, "Total", (table_x + 200, header_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
        cv2.putText(chart, "Active Modules", (table_x + 300, header_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
        
        # 绘制分隔线
        cv2.line(chart, (table_x, header_y + 10), (table_x + 500, header_y + 10), (0, 0, 0), 1)
        
        # 数据行
        row_y = header_y + 30
        for image_name, module_counts in module_stats.items():
            total_count = sum(module_counts.values())
            active_modules = len([c for c in module_counts.values() if c > 0])
            
            # 截断长文件名
            display_name = image_name[:20] + "..." if len(image_name) > 23 else image_name
            
            cv2.putText(chart, display_name, (table_x, row_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            cv2.putText(chart, str(total_count), (table_x + 200, row_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            cv2.putText(chart, f"{active_modules}/12", (table_x + 300, row_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            row_y += 25
            
            if row_y > chart_height - 50:  # 防止超出画布
                break
        
        # 保存图表
        cv2.imwrite(output_path, chart)
        print(f"✅ 统计图表已保存: {output_path}")
    
    def create_detection_heatmap(self, json_files: List[str], output_path: str):
        """创建检测热力图"""
        print("🔥 创建检测热力图...")
        
        try:
            # 统计每个模块的总检测数量
            module_totals = {module: 0 for module in self.module_colors.keys()}
            
            for json_file in json_files:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                detection_summary = data.get('detection_summary', {})
                module_counts = detection_summary.get('module_counts', {})
                
                for module, count in module_counts.items():
                    if module in module_totals:
                        module_totals[module] += count
            
            # 创建热力图
            self._create_heatmap_visualization(module_totals, output_path)
            
            return True
            
        except Exception as e:
            print(f"❌ 创建检测热力图失败: {e}")
            return False
    
    def _create_heatmap_visualization(self, module_totals: Dict, output_path: str):
        """创建热力图可视化"""
        
        # 创建画布
        heatmap_width = 800
        heatmap_height = 600
        heatmap = np.ones((heatmap_height, heatmap_width, 3), dtype=np.uint8) * 255
        
        # 标题
        title = "Detection Heatmap by Module"
        cv2.putText(heatmap, title, (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
        
        # 计算最大值用于归一化
        max_count = max(module_totals.values()) if module_totals.values() else 1
        
        # 绘制热力图
        start_y = 100
        bar_height = 30
        bar_max_width = 400
        
        for i, (module, count) in enumerate(module_totals.items()):
            y_pos = start_y + i * (bar_height + 10)
            
            # 计算条形长度
            bar_width = int((count / max_count) * bar_max_width) if max_count > 0 else 0
            
            # 获取颜色
            color = self.module_colors[module]
            
            # 绘制条形
            if bar_width > 0:
                cv2.rectangle(heatmap, (200, y_pos), (200 + bar_width, y_pos + bar_height), color, -1)
            
            # 绘制模块名称
            cv2.putText(heatmap, module, (50, y_pos + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            # 绘制数值
            cv2.putText(heatmap, str(count), (210 + bar_width, y_pos + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        # 保存热力图
        cv2.imwrite(output_path, heatmap)
        print(f"✅ 检测热力图已保存: {output_path}")


def find_json_files(output_dir: str) -> List[str]:
    """查找综合检测的JSON文件"""
    print(f"🔍 搜索JSON文件: {output_dir}")
    
    json_files = []
    for file_path in Path(output_dir).glob("*_comprehensive.json"):
        json_files.append(str(file_path))
    
    if json_files:
        print(f"📊 找到 {len(json_files)} 个JSON文件")
        for json_file in json_files:
            print(f"   - {os.path.basename(json_file)}")
    else:
        print("❌ 未找到综合检测的JSON文件")
    
    return json_files


def main():
    """主函数"""
    try:
        print("🎨 烟叶综合检测可视化分析程序")
        print("="*60)
        
        # 配置路径
        output_dir = "/home/<USER>/xm/code/coderafactor/test_data/test_output"
        
        # 查找JSON文件
        json_files = find_json_files(output_dir)
        if not json_files:
            print("❌ 没有找到可视化的数据文件")
            return False
        
        # 初始化高级可视化器
        visualizer = AdvancedVisualizer()
        
        print(f"\n🎨 开始创建高级可视化...")
        
        # 创建模块对比网格图
        grid_output = os.path.join(output_dir, "module_comparison_grid.png")
        visualizer.create_module_comparison_grid(json_files, grid_output)
        
        # 创建检测热力图
        heatmap_output = os.path.join(output_dir, "detection_heatmap.png")
        visualizer.create_detection_heatmap(json_files, heatmap_output)
        
        print(f"\n🎉 可视化分析完成！")
        print(f"📁 输出文件:")
        print(f"   模块对比图: {grid_output}")
        print(f"   检测热力图: {heatmap_output}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化程序执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🔥🔥🔥 启动烟叶综合检测可视化程序 🔥🔥🔥")
    
    success = main()
    
    if success:
        print(f"✅ 可视化程序执行成功！")
        sys.exit(0)
    else:
        print(f"❌ 可视化程序执行失败！")
        sys.exit(1)
