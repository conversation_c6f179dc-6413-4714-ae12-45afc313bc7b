#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能瓶颈分析脚本
使用思维链推理分析deploy_correlation_json_generate_optimized.py的性能瓶颈
"""

import os
import sys
import time
import cProfile
import pstats
import io
import json
import numpy as np
import cv2
from typing import Dict, List, Tuple
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor
import psutil
import tracemalloc

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    from correlation_json_generate.deploy_correlation_json_generate_optimized import (
        get_features, extract_features, cv_humoments_optimized,
        OptimizedFeatureExtractor, OptimizedGeometryCalculator,
        get_polygon_area, get_polygon_perimeter, max_internally_circle,
        OptimizedCorrelationConfig, memory_pool, geometry_cache, feature_cache
    )
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)

class PerformanceAnalyzer:
    """性能分析器 - 使用思维链推理分析瓶颈"""
    
    def __init__(self):
        self.config = OptimizedCorrelationConfig()
        self.results = {}
        self.memory_snapshots = []
        
    def analyze_bottlenecks(self):
        """
        思维链分析性能瓶颈：
        1. I/O瓶颈分析
        2. 计算密集型操作分析
        3. 内存使用分析
        4. 并发效率分析
        5. 缓存效率分析
        """
        print("🔍 开始性能瓶颈分析...")
        print("=" * 60)
        
        # 1. 分析I/O瓶颈
        self._analyze_io_bottlenecks()
        
        # 2. 分析计算瓶颈
        self._analyze_computation_bottlenecks()
        
        # 3. 分析内存瓶颈
        self._analyze_memory_bottlenecks()
        
        # 4. 分析并发瓶颈
        self._analyze_concurrency_bottlenecks()
        
        # 5. 分析缓存效率
        self._analyze_cache_efficiency()
        
        # 6. 生成优化建议
        self._generate_optimization_recommendations()
        
    def _analyze_io_bottlenecks(self):
        """分析I/O瓶颈"""
        print("\n📁 I/O瓶颈分析:")
        
        # 测试JSON文件读取性能
        test_files = self._get_test_files()
        if not test_files:
            print("   ⚠️  没有找到测试文件")
            return
            
        # 测试单个文件读取
        start_time = time.time()
        for _ in range(10):
            with open(test_files[0], 'r') as f:
                data = json.load(f)
        single_read_time = (time.time() - start_time) / 10
        
        # 测试批量文件读取
        start_time = time.time()
        for file_path in test_files[:5]:
            with open(file_path, 'r') as f:
                data = json.load(f)
        batch_read_time = time.time() - start_time
        
        self.results['io_analysis'] = {
            'single_file_read_time': single_read_time,
            'batch_read_time': batch_read_time,
            'avg_file_size': self._get_avg_file_size(test_files[:5])
        }
        
        print(f"   单文件读取平均耗时: {single_read_time:.4f}秒")
        print(f"   批量读取耗时: {batch_read_time:.4f}秒")
        print(f"   平均文件大小: {self.results['io_analysis']['avg_file_size']:.2f}MB")
        
        # I/O瓶颈判断
        if single_read_time > 0.1:
            print("   🚨 发现I/O瓶颈: 单文件读取时间过长")
        elif batch_read_time > 2.0:
            print("   🚨 发现I/O瓶颈: 批量读取时间过长")
        else:
            print("   ✅ I/O性能正常")
    
    def _analyze_computation_bottlenecks(self):
        """分析计算瓶颈"""
        print("\n🧮 计算瓶颈分析:")
        
        # 生成测试数据
        test_points = np.random.rand(1000, 2) * 1000
        imageHeight, imageWidth = 1024, 1024
        
        # 测试各个计算函数的性能
        computation_tests = {
            'polygon_area': lambda: get_polygon_area(test_points),
            'polygon_perimeter': lambda: get_polygon_perimeter(test_points),
            'max_internal_circle': lambda: max_internally_circle(test_points.astype(np.int32), imageHeight, imageWidth),
            'cv_humoments': lambda: cv_humoments_optimized(test_points),
            'feature_extraction': lambda: extract_features(test_points, imageHeight, imageWidth)
        }
        
        computation_results = {}
        for name, func in computation_tests.items():
            try:
                # 预热
                func()
                
                # 性能测试
                start_time = time.time()
                for _ in range(10):
                    func()
                avg_time = (time.time() - start_time) / 10
                computation_results[name] = avg_time
                
                print(f"   {name}: {avg_time:.4f}秒")
                
                # 瓶颈判断
                if avg_time > 0.1:
                    print(f"     🚨 {name} 存在性能瓶颈")
                    
            except Exception as e:
                print(f"   ❌ {name} 测试失败: {e}")
                computation_results[name] = float('inf')
        
        self.results['computation_analysis'] = computation_results
        
        # 找出最慢的操作
        slowest_op = max(computation_results.items(), key=lambda x: x[1])
        print(f"   🐌 最慢操作: {slowest_op[0]} ({slowest_op[1]:.4f}秒)")
    
    def _analyze_memory_bottlenecks(self):
        """分析内存瓶颈"""
        print("\n💾 内存瓶颈分析:")
        
        # 启动内存追踪
        tracemalloc.start()
        
        # 获取初始内存状态
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 模拟大量数据处理
        test_data = []
        for i in range(100):
            large_array = np.random.rand(1000, 1000)
            test_data.append(large_array)
            
            if i % 20 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                self.memory_snapshots.append({
                    'iteration': i,
                    'memory_mb': current_memory,
                    'memory_growth': current_memory - initial_memory
                })
        
        # 清理数据
        del test_data
        
        # 获取内存追踪结果
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        final_memory = process.memory_info().rss / 1024 / 1024
        
        self.results['memory_analysis'] = {
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': peak / 1024 / 1024,
            'final_memory_mb': final_memory,
            'memory_growth_mb': final_memory - initial_memory,
            'snapshots': self.memory_snapshots
        }
        
        print(f"   初始内存: {initial_memory:.2f}MB")
        print(f"   峰值内存: {peak / 1024 / 1024:.2f}MB")
        print(f"   最终内存: {final_memory:.2f}MB")
        print(f"   内存增长: {final_memory - initial_memory:.2f}MB")
        
        # 内存泄漏检测
        if final_memory - initial_memory > 100:  # 100MB阈值
            print("   🚨 可能存在内存泄漏")
        else:
            print("   ✅ 内存使用正常")
    
    def _analyze_concurrency_bottlenecks(self):
        """分析并发瓶颈"""
        print("\n🔄 并发瓶颈分析:")
        
        # 测试不同线程数的性能
        test_data = [np.random.rand(100, 2) for _ in range(20)]
        thread_counts = [1, 2, 4, 8, 16]
        
        concurrency_results = {}
        
        for thread_count in thread_counts:
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=thread_count) as executor:
                futures = [executor.submit(get_polygon_area, data) for data in test_data]
                results = [f.result() for f in futures]
            
            execution_time = time.time() - start_time
            concurrency_results[thread_count] = execution_time
            
            print(f"   {thread_count}线程: {execution_time:.4f}秒")
        
        self.results['concurrency_analysis'] = concurrency_results
        
        # 找出最优线程数
        optimal_threads = min(concurrency_results.items(), key=lambda x: x[1])
        print(f"   🎯 最优线程数: {optimal_threads[0]} ({optimal_threads[1]:.4f}秒)")
        
        # 并发效率分析
        single_thread_time = concurrency_results[1]
        for threads, time_taken in concurrency_results.items():
            if threads > 1:
                speedup = single_thread_time / time_taken
                efficiency = speedup / threads * 100
                print(f"   {threads}线程效率: {efficiency:.1f}% (加速比: {speedup:.2f}x)")
    
    def _analyze_cache_efficiency(self):
        """分析缓存效率"""
        print("\n🗄️ 缓存效率分析:")
        
        # 清空缓存
        geometry_cache.clear()
        feature_cache.clear()
        memory_pool.clear()
        
        # 测试缓存命中率
        test_points = np.random.rand(100, 2)
        
        # 第一次计算（缓存未命中）
        start_time = time.time()
        for _ in range(10):
            get_polygon_area(test_points)
        first_run_time = time.time() - start_time
        
        # 第二次计算（缓存命中）
        start_time = time.time()
        for _ in range(10):
            get_polygon_area(test_points)
        second_run_time = time.time() - start_time
        
        cache_speedup = first_run_time / second_run_time if second_run_time > 0 else 1
        
        self.results['cache_analysis'] = {
            'first_run_time': first_run_time,
            'second_run_time': second_run_time,
            'cache_speedup': cache_speedup,
            'geometry_cache_size': len(geometry_cache.cache),
            'feature_cache_size': len(feature_cache.cache),
            'memory_pool_size': memory_pool.current_size / 1024 / 1024  # MB
        }
        
        print(f"   首次运行: {first_run_time:.4f}秒")
        print(f"   缓存命中: {second_run_time:.4f}秒")
        print(f"   缓存加速比: {cache_speedup:.2f}x")
        print(f"   几何缓存大小: {len(geometry_cache.cache)}")
        print(f"   特征缓存大小: {len(feature_cache.cache)}")
        print(f"   内存池大小: {memory_pool.current_size / 1024 / 1024:.2f}MB")
        
        if cache_speedup < 1.5:
            print("   🚨 缓存效率较低")
        else:
            print("   ✅ 缓存效率良好")
    
    def _generate_optimization_recommendations(self):
        """生成优化建议"""
        print("\n💡 优化建议:")
        print("=" * 60)
        
        recommendations = []
        
        # I/O优化建议
        if 'io_analysis' in self.results:
            io_data = self.results['io_analysis']
            if io_data['single_file_read_time'] > 0.1:
                recommendations.append("🔧 I/O优化: 考虑使用异步I/O或内存映射文件")
            if io_data['avg_file_size'] > 10:
                recommendations.append("🔧 文件优化: 考虑压缩JSON文件或使用二进制格式")
        
        # 计算优化建议
        if 'computation_analysis' in self.results:
            comp_data = self.results['computation_analysis']
            slowest_ops = sorted(comp_data.items(), key=lambda x: x[1], reverse=True)[:3]
            for op, time_taken in slowest_ops:
                if time_taken > 0.05:
                    recommendations.append(f"🔧 计算优化: {op} 需要优化 (耗时: {time_taken:.4f}秒)")
        
        # 内存优化建议
        if 'memory_analysis' in self.results:
            mem_data = self.results['memory_analysis']
            if mem_data['memory_growth_mb'] > 50:
                recommendations.append("🔧 内存优化: 存在内存泄漏，需要改进内存管理")
            if mem_data['peak_memory_mb'] > 1000:
                recommendations.append("🔧 内存优化: 峰值内存过高，考虑分批处理")
        
        # 并发优化建议
        if 'concurrency_analysis' in self.results:
            conc_data = self.results['concurrency_analysis']
            optimal_threads = min(conc_data.items(), key=lambda x: x[1])[0]
            current_max = self.config.max_workers
            if optimal_threads != current_max:
                recommendations.append(f"🔧 并发优化: 建议使用 {optimal_threads} 线程 (当前: {current_max})")
        
        # 缓存优化建议
        if 'cache_analysis' in self.results:
            cache_data = self.results['cache_analysis']
            if cache_data['cache_speedup'] < 2.0:
                recommendations.append("🔧 缓存优化: 缓存效率较低，考虑改进缓存策略")
        
        # 输出建议
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"{i:2d}. {rec}")
        else:
            print("✅ 当前性能表现良好，暂无优化建议")
        
        # 保存分析结果
        self._save_analysis_results()
    
    def _get_test_files(self) -> List[str]:
        """获取测试文件列表"""
        test_dir = os.path.join(current_dir, 'test_output')
        if not os.path.exists(test_dir):
            return []
        return [os.path.join(test_dir, f) for f in os.listdir(test_dir) if f.endswith('.json')]
    
    def _get_avg_file_size(self, files: List[str]) -> float:
        """计算平均文件大小(MB)"""
        if not files:
            return 0.0
        total_size = sum(os.path.getsize(f) for f in files if os.path.exists(f))
        return total_size / len(files) / 1024 / 1024
    
    def _save_analysis_results(self):
        """保存分析结果"""
        output_file = os.path.join(current_dir, 'performance_analysis_results.json')
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"\n📊 分析结果已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存分析结果失败: {e}")

if __name__ == "__main__":
    print("🚀 性能瓶颈分析工具")
    print("使用思维链推理分析deploy_correlation_json_generate_optimized.py")
    
    analyzer = PerformanceAnalyzer()
    analyzer.analyze_bottlenecks()
    
    print("\n🎉 性能分析完成！")
