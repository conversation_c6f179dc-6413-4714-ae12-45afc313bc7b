#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版性能测试脚本
验证关键优化措施的效果
"""

import os
import sys
import time
import json
import gc
from typing import Dict, List
from concurrent.futures import ThreadPoolExecutor

def test_thread_efficiency():
    """测试线程效率优化"""
    print("⚡ 测试线程效率优化:")
    
    def dummy_task(x):
        """模拟计算任务"""
        result = 0
        for i in range(1000):
            result += i * x
        return result
    
    test_data = list(range(50))
    
    # 测试8线程（原始配置）
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=8) as executor:
        results_8 = list(executor.map(dummy_task, test_data))
    time_8_threads = time.time() - start_time
    
    # 测试2线程（优化配置）
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=2) as executor:
        results_2 = list(executor.map(dummy_task, test_data))
    time_2_threads = time.time() - start_time
    
    # 测试1线程（串行）
    start_time = time.time()
    results_1 = [dummy_task(x) for x in test_data]
    time_1_thread = time.time() - start_time
    
    print(f"   串行处理: {time_1_thread:.4f}秒")
    print(f"   2线程处理: {time_2_threads:.4f}秒")
    print(f"   8线程处理: {time_8_threads:.4f}秒")
    
    # 计算效率
    efficiency_2 = time_1_thread / time_2_threads if time_2_threads > 0 else 1
    efficiency_8 = time_1_thread / time_8_threads if time_8_threads > 0 else 1
    
    print(f"   2线程效率: {efficiency_2:.2f}x")
    print(f"   8线程效率: {efficiency_8:.2f}x")
    
    if time_2_threads < time_8_threads:
        improvement = (time_8_threads - time_2_threads) / time_8_threads * 100
        print(f"   ✅ 2线程比8线程快 {improvement:.1f}%")
    else:
        print(f"   ⚠️ 8线程仍然更快")
    
    return {
        'time_1_thread': time_1_thread,
        'time_2_threads': time_2_threads,
        'time_8_threads': time_8_threads,
        'efficiency_2': efficiency_2,
        'efficiency_8': efficiency_8
    }

def test_memory_management():
    """测试内存管理优化"""
    print("\n💾 测试内存管理优化:")
    
    try:
        import psutil
        process = psutil.Process()
        
        # 初始内存
        initial_memory = process.memory_info().rss / 1024 / 1024
        print(f"   初始内存: {initial_memory:.2f}MB")
        
        # 模拟原始内存使用（不清理）
        large_data = []
        for i in range(100):
            data = [j * i for j in range(10000)]
            large_data.append(data)
        
        peak_memory_original = process.memory_info().rss / 1024 / 1024
        print(f"   原始峰值内存: {peak_memory_original:.2f}MB")
        
        # 清理
        del large_data
        gc.collect()
        
        # 模拟优化内存使用（定期清理）
        large_data_optimized = []
        for i in range(100):
            data = [j * i for j in range(10000)]
            large_data_optimized.append(data)
            
            # 每20次清理一次
            if i % 20 == 0:
                gc.collect()
        
        peak_memory_optimized = process.memory_info().rss / 1024 / 1024
        print(f"   优化峰值内存: {peak_memory_optimized:.2f}MB")
        
        # 清理
        del large_data_optimized
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024
        print(f"   最终内存: {final_memory:.2f}MB")
        
        # 计算改进
        memory_growth_original = peak_memory_original - initial_memory
        memory_growth_optimized = peak_memory_optimized - initial_memory
        improvement = (memory_growth_original - memory_growth_optimized) / memory_growth_original * 100 if memory_growth_original > 0 else 0
        
        print(f"   原始内存增长: {memory_growth_original:.2f}MB")
        print(f"   优化内存增长: {memory_growth_optimized:.2f}MB")
        print(f"   内存改进: {improvement:.1f}%")
        
        return {
            'initial_memory': initial_memory,
            'peak_memory_original': peak_memory_original,
            'peak_memory_optimized': peak_memory_optimized,
            'final_memory': final_memory,
            'improvement_percent': improvement
        }
        
    except ImportError:
        print("   ⚠️ psutil不可用，跳过内存测试")
        return None

def test_json_processing():
    """测试JSON处理优化"""
    print("\n📁 测试JSON处理优化:")
    
    # 创建测试数据
    test_data = {
        'shapes': [
            {
                'label': f'test_shape_{i}',
                'points': [[j, j+1] for j in range(100)],
                'shape_type': 'polygon'
            }
            for i in range(100)
        ],
        'metadata': {
            'version': '1.0',
            'created_at': '2024-01-01',
            'data': [i for i in range(1000)]
        }
    }
    
    # 测试JSON序列化/反序列化性能
    start_time = time.time()
    for _ in range(10):
        json_str = json.dumps(test_data)
        parsed_data = json.loads(json_str)
    json_time = time.time() - start_time
    
    print(f"   JSON处理耗时: {json_time:.4f}秒")
    print(f"   数据大小: {len(json_str) / 1024:.2f}KB")
    
    # 测试压缩效果（模拟）
    import gzip
    import pickle
    
    # 使用pickle + gzip
    start_time = time.time()
    for _ in range(10):
        compressed_data = gzip.compress(pickle.dumps(test_data))
        decompressed_data = pickle.loads(gzip.decompress(compressed_data))
    compressed_time = time.time() - start_time
    
    compression_ratio = len(json_str) / len(compressed_data)
    speedup = json_time / compressed_time if compressed_time > 0 else 1
    
    print(f"   压缩处理耗时: {compressed_time:.4f}秒")
    print(f"   压缩后大小: {len(compressed_data) / 1024:.2f}KB")
    print(f"   压缩比: {compression_ratio:.2f}x")
    print(f"   处理加速比: {speedup:.2f}x")
    
    return {
        'json_time': json_time,
        'compressed_time': compressed_time,
        'compression_ratio': compression_ratio,
        'speedup': speedup,
        'original_size_kb': len(json_str) / 1024,
        'compressed_size_kb': len(compressed_data) / 1024
    }

def generate_optimization_summary(thread_results, memory_results, json_results):
    """生成优化总结"""
    print("\n📊 优化效果总结:")
    print("=" * 60)
    
    improvements = []
    
    # 线程优化效果
    if thread_results:
        if thread_results['time_2_threads'] < thread_results['time_8_threads']:
            thread_improvement = (thread_results['time_8_threads'] - thread_results['time_2_threads']) / thread_results['time_8_threads'] * 100
            improvements.append(thread_improvement)
            print(f"✅ 线程优化: 2线程比8线程快 {thread_improvement:.1f}%")
        else:
            print(f"⚠️ 线程优化: 需要进一步调整")
    
    # 内存优化效果
    if memory_results and memory_results['improvement_percent'] > 0:
        improvements.append(memory_results['improvement_percent'])
        print(f"✅ 内存优化: 减少内存使用 {memory_results['improvement_percent']:.1f}%")
    elif memory_results:
        print(f"⚠️ 内存优化: 改进有限")
    
    # JSON处理优化效果
    if json_results:
        if json_results['speedup'] > 1:
            json_improvement = (json_results['speedup'] - 1) * 100
            improvements.append(json_improvement)
            print(f"✅ I/O优化: 压缩处理快 {json_improvement:.1f}%")
        
        if json_results['compression_ratio'] > 2:
            print(f"✅ 存储优化: 压缩比 {json_results['compression_ratio']:.1f}x")
    
    # 总体评估
    if improvements:
        avg_improvement = sum(improvements) / len(improvements)
        print(f"\n🎯 平均性能改进: {avg_improvement:.1f}%")
        
        if avg_improvement > 30:
            print("🎉 优化效果卓越！")
        elif avg_improvement > 20:
            print("👍 优化效果显著！")
        elif avg_improvement > 10:
            print("📈 优化效果良好！")
        else:
            print("⚠️ 优化效果有限，需要进一步改进")
    else:
        print("⚠️ 未检测到明显的性能改进")
    
    # 具体建议
    print(f"\n💡 优化建议:")
    print(f"1. 🔧 线程配置: 使用2线程替代8线程")
    print(f"2. 🧠 内存管理: 定期执行垃圾回收")
    print(f"3. 📦 文件压缩: 使用gzip+pickle替代JSON")
    print(f"4. 📊 监控指标: 持续监控内存和CPU使用率")

def main():
    """主函数"""
    print("🚀 简化版性能测试")
    print("验证关键优化措施的效果")
    print("=" * 60)
    
    # 执行各项测试
    thread_results = test_thread_efficiency()
    memory_results = test_memory_management()
    json_results = test_json_processing()
    
    # 生成总结
    generate_optimization_summary(thread_results, memory_results, json_results)
    
    print("\n🎉 性能测试完成！")

if __name__ == "__main__":
    main()
