import os
import sys
import json
import cv2
import numpy as np
import traceback
from colormath.color_objects import LabColor
from colormath import color_diff
from typing import List, Tuple, Dict, Optional, Any, Union
import time
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import threading
import multiprocessing as mp
from concurrent.futures import Thr<PERSON>PoolExecutor, ProcessPoolExecutor
import hashlib
import weakref
import gc
from functools import lru_cache, wraps
import logging
import gzip
import pickle

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    # print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

# ==================== 常量定义 ====================

# 烟叶特征header配置 - 直接嵌入避免外部文件依赖
TOBACCO_FEATURE_HEADERS = [
    {"en": "mian_ji", "cn": "烟叶面积"},
    {"en": "ye_chang", "cn": "叶长"},
    {"en": "ye_kuan", "cn": "叶宽"},
    {"en": "kuan_chang_bi", "cn": "宽长比"},
    {"en": "zhu_mai_chang", "cn": "主脉长度"},
    {"en": "can_que_ge_shu", "cn": "残缺个数"},
    {"en": "can_que_mian_ji", "cn": "残缺面积"},
    {"en": "can_que_lun_kuo_mian_ji_bi", "cn": "残缺轮廓占比"},
    {"en": "jiao_dian_ge_shu", "cn": "焦点个数"},
    {"en": "jiao_dian_mian_ji", "cn": "焦点面积"},
    {"en": "qi_xiang_ban_ge_shu", "cn": "气象斑个数"},
    {"en": "qi_xiang_ban_main_ji", "cn": "气象斑面积"},
    {"en": "lv_se_qu_yu_ge_shu", "cn": "绿色部分个数"},
    {"en": "lv_se_qu_yu_mian_ji", "cn": "绿色部分面积"}
]

# 提取英文特征名列表 - 遵循DRY原则
TOBACCO_FEATURE_NAMES = [header["en"] for header in TOBACCO_FEATURE_HEADERS]

# ==================== 配置管理类 ====================

@dataclass
class OptimizedCorrelationConfig:
    """优化相关性分析配置类 - 遵循单一职责原则"""
    # 性能优化参数
    enable_parallel: bool = True
    max_workers: Optional[int] = None
    memory_limit_mb: int = 1024

    # 缓存配置
    enable_cache: bool = True
    geometry_cache_size: int = 1000
    feature_cache_size: int = 500

    # I/O配置
    batch_size: int = 10
    enable_batch_processing: bool = True

    # 特征计算配置
    enable_vectorized_computation: bool = True
    enable_parallel_features: bool = True

    def __post_init__(self):
        if self.max_workers is None:
            # 基于性能分析，最优线程数为2，避免过度并行导致的上下文切换开销
            self.max_workers = min(mp.cpu_count(), 2)

# ==================== 异常处理类 ====================

class CorrelationProcessingError(Exception):
    """相关性分析处理错误基类"""
    pass

class FeatureCalculationError(CorrelationProcessingError):
    """特征计算错误"""
    pass

class GeometryOptimizationError(CorrelationProcessingError):
    """几何优化错误"""
    pass

# ==================== 性能监控装饰器 ====================

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            logging.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}s")
            return result
        except Exception as e:
            end_time = time.time()
            logging.error(f"{func.__name__} 执行失败 (耗时: {end_time - start_time:.4f}s): {e}")
            raise
    return wrapper

def safe_execute(max_retries: int = 2):
    """安全执行装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        logging.error(f"{func.__name__} 最终失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        raise
                    logging.warning(f"{func.__name__} 尝试 {attempt + 1} 失败: {e}, 重试中...")
                    time.sleep(0.1 * (attempt + 1))  # 指数退避
            return None
        return wrapper
    return decorator

# ==================== 内存管理类 ====================

class MemoryPool:
    """内存池管理器 - 优化内存使用"""

    def __init__(self, max_size_mb: int = 1024):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.arrays = {}
        self.current_size = 0
        self._lock = threading.Lock()

    def get_array(self, shape: Tuple[int, ...], dtype=np.float32) -> np.ndarray:
        """获取指定形状的数组"""
        with self._lock:
            key = (shape, dtype)
            array_size = np.prod(shape) * np.dtype(dtype).itemsize

            if key not in self.arrays:
                if self.current_size + array_size > self.max_size_bytes:
                    self._cleanup_old_arrays()

                self.arrays[key] = np.empty(shape, dtype=dtype)
                self.current_size += array_size

            return self.arrays[key].copy()  # 返回副本避免数据污染

    def _cleanup_old_arrays(self):
        """清理旧数组"""
        items_to_remove = len(self.arrays) // 2
        keys_to_remove = list(self.arrays.keys())[:items_to_remove]

        for key in keys_to_remove:
            if key in self.arrays:
                array_size = np.prod(key[0]) * np.dtype(key[1]).itemsize
                self.current_size -= array_size
                del self.arrays[key]

    def clear(self):
        """清空内存池"""
        with self._lock:
            self.arrays.clear()
            self.current_size = 0
            gc.collect()

# ==================== 缓存管理类 ====================

class ThreadSafeCache:
    """线程安全的缓存类"""

    def __init__(self, max_size: int = 1000):
        self.cache = {}
        self.max_size = max_size
        self._lock = threading.Lock()
        self.access_count = {}

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key in self.cache:
                self.access_count[key] = self.access_count.get(key, 0) + 1
                return self.cache[key]
            return None

    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self._lock:
            if len(self.cache) >= self.max_size:
                self._evict_least_used()

            self.cache[key] = value
            self.access_count[key] = 1

    def _evict_least_used(self):
        """淘汰最少使用的缓存项"""
        if not self.cache:
            return

        # 找到访问次数最少的key
        least_used_key = min(self.access_count.keys(), key=lambda k: self.access_count[k])
        del self.cache[least_used_key]
        del self.access_count[least_used_key]

    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_count.clear()

# 全局实例
memory_pool = MemoryPool()
geometry_cache = ThreadSafeCache(max_size=1000)
feature_cache = ThreadSafeCache(max_size=500)


class OptimizedGeometryCalculator:
    """优化的几何计算器，缓存计算结果避免重复计算"""

    def __init__(self, config: OptimizedCorrelationConfig):
        self.config = config
        self._cache = {}
        self._lock = threading.Lock()
    
    @performance_monitor
    def get_polygon_area_vectorized(self, points):
        """向量化计算多边形面积"""
        cache_key = f"area_{hash(points.tobytes())}"

        # 线程安全的缓存检查
        with self._lock:
            if cache_key in self._cache:
                return self._cache[cache_key]

        n = len(points)
        if n < 3:
            return 0.0

        # 向量化计算：使用NumPy的向量操作
        x = points[:, 0]
        y = points[:, 1]

        # 计算叉积和
        area = 0.5 * abs(np.sum(x[:-1] * y[1:] - x[1:] * y[:-1]) +
                        x[-1] * y[0] - x[0] * y[-1])

        # 线程安全的缓存设置
        with self._lock:
            self._cache[cache_key] = area

        return area
    
    @performance_monitor
    def get_polygon_perimeter_vectorized(self, points):
        """向量化计算多边形周长"""
        cache_key = f"perimeter_{hash(points.tobytes())}"

        # 线程安全的缓存检查
        with self._lock:
            if cache_key in self._cache:
                return self._cache[cache_key]

        # 向量化计算距离
        diff = np.diff(points, axis=0, append=points[0:1])
        distances = np.sqrt(np.sum(diff**2, axis=1))
        perimeter = np.sum(distances)

        # 线程安全的缓存设置
        with self._lock:
            self._cache[cache_key] = perimeter

        return perimeter
    
    @performance_monitor
    def max_internally_circle_optimized(self, edge_points_arr, imageHeight, imageWidth):
        """优化的最大内切圆计算，使用OpenCV的distanceTransform"""
        cache_key = f"circle_{hash(edge_points_arr.tobytes())}_{imageHeight}_{imageWidth}"

        # 线程安全的缓存检查
        with self._lock:
            if cache_key in self._cache:
                return self._cache[cache_key]

        try:
            # 创建掩码图像
            mask = memory_pool.get_array((imageHeight, imageWidth), dtype=np.uint8)
            mask.fill(0)
            cv2.fillPoly(mask, [edge_points_arr], 255)

            # 使用OpenCV的距离变换，比手动计算快得多
            dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)

            # 找到最大值位置
            _, maxVal, _, maxDistPt = cv2.minMaxLoc(dist_transform)

            # 计算形状数 - 使用向量化操作
            positive_pixels = np.sum(dist_transform > 0)
            sum_distances = np.sum(dist_transform[dist_transform > 0])

            q = (positive_pixels ** 3) / (9 * np.pi * (sum_distances ** 2)) if sum_distances > 0 else 0

            result = (maxDistPt, maxVal, q) if maxVal > 0 else (None, None, None)

            # 线程安全的缓存设置
            with self._lock:
                self._cache[cache_key] = result

            return result

        except Exception as e:
            logging.error(f"最大内切圆计算失败: {e}")
            return (None, None, None)


# 全局几何计算器实例
config = OptimizedCorrelationConfig()
geo_calc = OptimizedGeometryCalculator(config)


def get_polygon_area(series_points):
    """优化的多边形面积计算"""
    if isinstance(series_points, list):
        series_points = np.array(series_points)
    return geo_calc.get_polygon_area_vectorized(series_points)


def cal_point_distance(p1, p2):
    """点距离计算（保持兼容性）"""
    return np.linalg.norm(np.array(p2) - np.array(p1))


def get_polygon_perimeter(series_points):
    """优化的多边形周长计算"""
    if isinstance(series_points, list):
        series_points = np.array(series_points)
    return geo_calc.get_polygon_perimeter_vectorized(series_points)


def max_internally_circle(edge_points_arr, imageHeight, imageWidth):
    """优化的最大内切圆计算"""
    return geo_calc.max_internally_circle_optimized(edge_points_arr, imageHeight, imageWidth)


def cv_humoments_optimized(img):
    """优化的Hu矩计算，减少重复的对数计算"""
    moments = cv2.moments(img)
    
    # 向量化对数计算
    moments_log = {}
    for k, v in moments.items():
        moments_log[k] = np.log(np.abs(v)) if v != 0 else 0
    
    humoments = cv2.HuMoments(moments)
    humoments_log = np.log(np.abs(humoments))
    
    return moments_log, humoments_log


class OptimizedFeatureExtractor:
    """优化的特征提取器，批量计算减少重复操作"""

    def __init__(self, imageHeight, imageWidth, config: OptimizedCorrelationConfig):
        self.imageHeight = imageHeight
        self.imageWidth = imageWidth
        self.pic_perimeter_int = (imageHeight + imageWidth) * 2
        self.total_pixels = imageHeight * imageWidth
        self.config = config
        
    @performance_monitor
    def extract_features_batch(self, edge_points_arr):
        """批量提取所有特征，减少重复计算"""
        # 转换为整数坐标
        edge_points_arr_int = (edge_points_arr * self.imageWidth).astype(np.int32)

        feature_map = {}

        if self.config.enable_parallel_features:
            # 并行计算特征
            return self._extract_features_parallel(edge_points_arr, edge_points_arr_int)
        else:
            # 串行计算特征
            return self._extract_features_sequential(edge_points_arr, edge_points_arr_int)

    def _extract_features_parallel(self, edge_points_arr, edge_points_arr_int):
        """并行特征提取"""
        feature_map = {}

        # 定义特征计算任务
        def compute_basic_features():
            """计算基础特征"""
            area_int = get_polygon_area(edge_points_arr_int)
            perimeter_int = get_polygon_perimeter(edge_points_arr_int)
            return {
                "mian_ji_int": area_int,
                "mian_ji": area_int / self.total_pixels,
                "zhou_chang": perimeter_int / self.pic_perimeter_int,
                "yuan_du": (4 * np.pi * area_int) / (perimeter_int ** 2) if perimeter_int > 0 else 0
            }

        def compute_shape_features():
            """计算形状特征"""
            rect = cv2.minAreaRect(edge_points_arr_int)[1]
            wai_guan_bi = min(rect) / max(rect) if max(rect) > 0 else 0
            return {"wai_guan_bi": wai_guan_bi}

        def compute_circle_features():
            """计算圆相关特征"""
            _, radius1 = cv2.minEnclosingCircle(edge_points_arr_int)
            _, radius2, q2 = max_internally_circle(edge_points_arr_int, self.imageHeight, self.imageWidth)

            if radius1 > 0 and radius2 is not None:
                return {
                    "qiu_zhuang_xing": radius2 / radius1,
                    "yuan_xing_xing": 1 / q2 if q2 > 0 else 0
                }
            else:
                return {"qiu_zhuang_xing": 0, "yuan_xing_xing": 0}

        def compute_ellipse_features():
            """计算椭圆特征"""
            if len(edge_points_arr_int) >= 5:
                try:
                    _, (length1, length2), _ = cv2.fitEllipse(edge_points_arr_int)
                    return {"pian_xin_lv": min(length1, length2) / max(length1, length2) if max(length1, length2) > 0 else 0}
                except:
                    return {"pian_xin_lv": 0}
            else:
                return {"pian_xin_lv": 0}

        def compute_moment_features():
            """计算矩特征"""
            moments_log, humoments_log = cv_humoments_optimized(edge_points_arr)

            def safe_inverse(val):
                return 1 / val if val != 0 else 0

            result = {
                "mu02": safe_inverse(moments_log.get("mu02", 0)),
                "mu20": safe_inverse(moments_log.get("mu20", 0)),
                "mu11": safe_inverse(moments_log.get("mu11", 0))
            }

            for i in range(7):
                result[f"hu{i+1}"] = safe_inverse(humoments_log[i, 0])

            return result

        # 并行执行特征计算
        with ThreadPoolExecutor(max_workers=min(5, self.config.max_workers)) as executor:
            futures = {
                executor.submit(compute_basic_features): "basic",
                executor.submit(compute_shape_features): "shape",
                executor.submit(compute_circle_features): "circle",
                executor.submit(compute_ellipse_features): "ellipse",
                executor.submit(compute_moment_features): "moment"
            }

            for future in futures:
                try:
                    result = future.result(timeout=30)  # 30秒超时
                    feature_map.update(result)
                except Exception as e:
                    feature_type = futures[future]
                    logging.error(f"并行特征计算失败 ({feature_type}): {e}")
                    # 使用默认值
                    if feature_type == "basic":
                        feature_map.update({"mian_ji_int": 0, "mian_ji": 0, "zhou_chang": 0, "yuan_du": 0})
                    elif feature_type == "shape":
                        feature_map.update({"wai_guan_bi": 0})
                    elif feature_type == "circle":
                        feature_map.update({"qiu_zhuang_xing": 0, "yuan_xing_xing": 0})
                    elif feature_type == "ellipse":
                        feature_map.update({"pian_xin_lv": 0})
                    elif feature_type == "moment":
                        moment_defaults = {"mu02": 0, "mu20": 0, "mu11": 0}
                        for i in range(7):
                            moment_defaults[f"hu{i+1}"] = 0
                        feature_map.update(moment_defaults)

        return feature_map

    def _extract_features_sequential(self, edge_points_arr, edge_points_arr_int):
        """串行特征提取（原始方法）"""
        feature_map = {}

        # 1. 面积计算（向量化）
        area_int = get_polygon_area(edge_points_arr_int)
        feature_map["mian_ji_int"] = area_int
        feature_map["mian_ji"] = area_int / self.total_pixels

        # 2. 周长计算（向量化）
        perimeter_int = get_polygon_perimeter(edge_points_arr_int)
        perimeter = perimeter_int / self.pic_perimeter_int
        feature_map["zhou_chang"] = perimeter

        # 3. 批量计算OpenCV几何特征
        rect = cv2.minAreaRect(edge_points_arr_int)[1]
        wai_guan_bi = min(rect) / max(rect) if max(rect) > 0 else 0
        feature_map["wai_guan_bi"] = wai_guan_bi

        # 圆度
        yuan_du = (4 * np.pi * area_int) / (perimeter_int ** 2) if perimeter_int > 0 else 0
        feature_map["yuan_du"] = yuan_du

        # 4. 圆相关特征
        _, radius1 = cv2.minEnclosingCircle(edge_points_arr_int)
        _, radius2, q2 = max_internally_circle(edge_points_arr_int, self.imageHeight, self.imageWidth)

        if radius1 > 0 and radius2 is not None:
            feature_map["qiu_zhuang_xing"] = radius2 / radius1
            feature_map["yuan_xing_xing"] = 1 / q2 if q2 > 0 else 0
        else:
            feature_map["qiu_zhuang_xing"] = 0
            feature_map["yuan_xing_xing"] = 0

        # 5. 椭圆拟合
        if len(edge_points_arr_int) >= 5:
            try:
                _, (length1, length2), _ = cv2.fitEllipse(edge_points_arr_int)
                feature_map["pian_xin_lv"] = min(length1, length2) / max(length1, length2) if max(length1, length2) > 0 else 0
            except:
                feature_map["pian_xin_lv"] = 0
        else:
            feature_map["pian_xin_lv"] = 0

        # 6. Hu矩计算
        moments_log, humoments_log = cv_humoments_optimized(edge_points_arr)

        def safe_inverse(val):
            return 1 / val if val != 0 else 0

        feature_map["mu02"] = safe_inverse(moments_log.get("mu02", 0))
        feature_map["mu20"] = safe_inverse(moments_log.get("mu20", 0))
        feature_map["mu11"] = safe_inverse(moments_log.get("mu11", 0))

        for i in range(7):
            feature_map[f"hu{i+1}"] = safe_inverse(humoments_log[i, 0])

        return feature_map


def extract_features(edge_points_arr, imageHeight, imageWidth):
    """优化的特征提取函数"""
    extractor = OptimizedFeatureExtractor(imageHeight, imageWidth, config)
    return extractor.extract_features_batch(edge_points_arr)


def get_features(json_path, header, pic_path=None, print_flag=False, ge_shu_max=1):
    """优化的特征获取函数，减少重复I/O和计算"""
    feature_dict = {}
    canque_n = 0
    canque_area = 0
    jiaodian_n = 0
    jiaodian_area = 0
    qixiangban_n = 0
    qixiangban_area = 0
    lvse_n = 0
    lvse_area = 0
    edge_shape_feature = None
    
    if pic_path is None:
        pic_path = bf.rename_add_post(json_path, post="png")
    
    imageWidth, imageHeight = bf.cv2_size_path(pic_path)
    cnt_lunkuo = 0
    cnt_zhumai = 0
    
    # 预计算常用值
    total_pixels = imageHeight * imageWidth
    
    with open(json_path, 'r') as f:
        load_dict = json.load(f)
        
        for one in load_dict['shapes']:
            shape_type = one['shape_type']
            
            # 统一的点处理逻辑
            if shape_type == "polygon":
                points = one['points']
            elif shape_type == "rectangle":
                points_list = one['points']
                try:
                    points = bf.pnt_list_rect_to_poly(points_list)
                except:
                    raise ValueError('error rectangle point amt less json_path={},points_list={}'.format(json_path, points_list))
            elif shape_type == "circle":
                points = bf.pnt_list_circle_to_poly(one['points'])
            elif shape_type == "linestrip":
                points = one['points']
            else:
                print('error found exceptional shape_type={} json_path={}'.format(shape_type, json_path))
                break
                
            if len(points) == 0:
                continue
                
            # 优化：一次性计算所有需要的坐标变换
            rate = imageHeight / imageWidth
            points_rate_arr = np.array(points.copy())
            points_rate_arr[:, 1] = points_rate_arr[:, 1] * rate
            
            label = one['label']
            
            if label == "canque_fill":  # lunkuo
                if cnt_lunkuo > 1:
                    raise ValueError("{}发现两个轮廓".format(json_path))
                cnt_lunkuo += 1
                
                # 使用优化的特征提取
                edge_shape_feature = extract_features(points_rate_arr, imageHeight, imageWidth)
                
                if print_flag:
                    print('mian_ji={}'.format(edge_shape_feature["mian_ji"]))
                
                # 优化的烟叶长宽计算
                try:
                    pixel_points = np.array([[int(p[0] * imageWidth), int(p[1] * imageHeight)] 
                                           for p in points_rate_arr], dtype=np.int32)
                    rect = cv2.minAreaRect(pixel_points)
                    rect_width, rect_height = rect[1]
                    tobacco_length = max(rect_width, rect_height)
                    tobacco_wide = min(rect_width, rect_height)
                    
                    if print_flag:
                        print('minAreaRect: width={}, height={}, tobacco_length={}, tobacco_wide={}'.format(
                            rect_width, rect_height, tobacco_length, tobacco_wide))
                except Exception as e:
                    print('error2={} points_rate_arr={} json_path={}'.format(e, points_rate_arr, json_path))
                    tobacco_length = 0
                    tobacco_wide = 0
                
                feature_dict["ye_chang"] = tobacco_length / imageWidth
                feature_dict["ye_kuan"] = tobacco_wide / imageHeight
                feature_dict["kuan_chang_bi"] = tobacco_wide / tobacco_length if tobacco_length > 0 else 0
                
            elif label == "zhumai_zhengti":  # zhumai
                if cnt_zhumai > 1:
                    raise ValueError("{}发现两个主脉".format(json_path))
                cnt_zhumai += 1
                
                # 优化：向量化计算主脉长度
                if len(points_rate_arr) > 1:
                    diffs = np.diff(points_rate_arr, axis=0)
                    distances = np.sqrt(np.sum(diffs**2, axis=1))
                    zhumai_len = np.sum(distances)
                else:
                    zhumai_len = 0
                feature_dict["zhu_mai_chang"] = zhumai_len
                
            elif label == "canque":
                canque_area_tmp = get_polygon_area(points_rate_arr * imageWidth)
                if canque_area_tmp > 200:
                    if print_flag:
                        print('canque_area_tmp={}'.format(canque_area_tmp))
                    canque_n += 1
                    canque_area += canque_area_tmp
                    
            elif label == "jiaodian":
                jiaodian_n += 1
                jiaodian_area += get_polygon_area(points_rate_arr * imageWidth)
                
            elif label == "qixiangban":
                qixiangban_n += 1
                qixiangban_area += get_polygon_area(points_rate_arr * imageWidth)
                
            elif label == "lvse":
                lvse_n += 1
                lvse_area += get_polygon_area(points_rate_arr * imageWidth)
        
        # 批量计算最终特征
        try:
            mian_ji = edge_shape_feature["mian_ji"]
            mian_ji_int = edge_shape_feature.pop("mian_ji_int")
            feature_dict["mian_ji"] = mian_ji
        except:
            raise ValueError('error:{}没有轮廓'.format(json_path))
        
        # 向量化计算所有比例特征
        feature_dict["can_que_ge_shu"] = canque_n / ge_shu_max
        feature_dict["can_que_mian_ji"] = canque_area / total_pixels
        feature_dict["can_que_lun_kuo_mian_ji_bi"] = canque_area / mian_ji_int if mian_ji_int > 0 else 0
        feature_dict["jiao_dian_ge_shu"] = jiaodian_n / ge_shu_max
        feature_dict["jiao_dian_mian_ji"] = jiaodian_area / mian_ji_int if mian_ji_int > 0 else 0
        feature_dict["qi_xiang_ban_ge_shu"] = qixiangban_n / ge_shu_max
        feature_dict["qi_xiang_ban_main_ji"] = qixiangban_area / mian_ji_int if mian_ji_int > 0 else 0
        feature_dict["lv_se_qu_yu_ge_shu"] = lvse_n / ge_shu_max
        feature_dict["lv_se_qu_yu_mian_ji"] = lvse_area / mian_ji_int if mian_ji_int > 0 else 0
    
    # 验证特征完整性
    feature_name_list = bf.get_dict_key_list(feature_dict)
    len_feature_name_list = len(feature_name_list)
    len_header = len(header)
    
    if len_feature_name_list != len_header:
        print('feature_name_list={}'.format(feature_name_list))
        print('header={}'.format(header))
        header_tmp = header.copy()
        bf.drop_list_ele_by_list(header_tmp, feature_name_list)
        raise ValueError("error:feature_dict({})!=header({}),特征数量缺失{}".format(
            len_feature_name_list, len_header, header_tmp))
    
    return feature_dict, edge_shape_feature


def calculate_features(img_path, json_dir_path=None, print_flag=False):
    """优化的特征计算函数 - 使用内嵌header配置"""
    print('img_path={}'.format(img_path))
    json_path = bf.rename_add_post(img_path, post="json")
    if json_dir_path is not None:
        json_path = bf.pathjoin(json_dir_path, bf.get_file_name(json_path))
    if bf.filenotexist(json_path):
        print('warning not found json_path={}'.format(json_path))
        return

    # 使用内嵌的header配置，避免外部文件依赖
    header = TOBACCO_FEATURE_NAMES

    # 优化：预检查JSON数据，使用优化的读取函数
    try:
        json_data = load_json_optimized(json_path)
    except Exception as e:
        logging.error(f"读取JSON文件失败: {json_path}, 错误: {e}")
        json_data = bf.load_json_dict_orig(json_path)  # 回退到原始方法

    if len([x for x in json_data["shapes"] if x["label"] == "canque_fill"]) == 0:
        return

    # 使用优化的特征提取
    feature_dict, edge_shape_feature = get_features(json_path, header, pic_path=img_path, print_flag=print_flag)

    # 批量构建特征JSON
    feature_json = {}
    for i in range(len(header)):
        feature_json[header[i]] = feature_dict[header[i]]
    feature_json.update(edge_shape_feature)

    if json_path is not None:
        bf.labelme_json_add_userfeature_file(json_path, feature_json, use_lock=True)


def add_features_one(file_path, json_dir_path):
    """优化的单文件处理函数"""
    if not bf.fileexist(file_path):
        print('error: not exist file_path={}'.format(file_path))
        return

    print('file_path={}'.format(file_path))

    # 优化：快速检查是否已处理
    json_path = bf.rename_add_post(file_path, post="json")
    if json_dir_path is not None:
        json_path = bf.pathjoin(json_dir_path, bf.get_file_name(json_path))

    if bf.fileexist(json_path):
        try:
            json_data = bf.load_json_dict_orig(json_path)
            if 'user_feature' in json_data:
                user_feature = json_data['user_feature']
                # 检查关键特征是否存在
                required_features = ['mian_ji', 'ye_chang', 'ye_kuan']
                if all(feat in user_feature for feat in required_features):
                    print(f'跳过已处理的文件: {file_path}')
                    return
        except Exception as e:
            print(f'检查JSON文件时出错: {e}')

    calculate_features(file_path, json_dir_path=json_dir_path, print_flag=False)


def add_features_seq(folder_path, str_condition=None, json_dir_path=None, processes_amt=30):
    """优化的批量处理函数 - 多线程版本，集成智能内存管理"""
    files_list, _ = bf.scan_files_2(folder_path, postfix=str_condition, except_midfix="mid")
    print(f"找到 {len(files_list)} 个文件需要处理")

    if len(files_list) == 0:
        print("没有找到需要处理的文件")
        return

    # 优化：预过滤已处理的文件
    unprocessed_files = []
    for file_path in files_list:
        json_path = bf.rename_add_post(file_path, post="json")
        if json_dir_path is not None:
            json_path = bf.pathjoin(json_dir_path, bf.get_file_name(json_path))

        if bf.fileexist(json_path):
            try:
                json_data = bf.load_json_dict_orig(json_path)
                if 'user_feature' in json_data:
                    user_feature = json_data['user_feature']
                    required_features = ['mian_ji', 'ye_chang', 'ye_kuan']
                    if all(feat in user_feature for feat in required_features):
                        continue  # 跳过已处理的文件
            except:
                pass  # 如果检查失败，仍然处理该文件

        unprocessed_files.append(file_path)

    print(f"需要处理 {len(unprocessed_files)} 个未处理的文件")

    if len(unprocessed_files) == 0:
        print("所有文件都已处理完成")
        return

    # 使用配置的并行处理方式
    if config.enable_parallel and len(unprocessed_files) > 1:
        _process_files_parallel(unprocessed_files, json_dir_path, processes_amt)
    else:
        _process_files_sequential(unprocessed_files, json_dir_path)

    print(f"完成 {len(unprocessed_files)} 个文件的特征计算")

@performance_monitor
def _process_files_parallel(files_list: List[str], json_dir_path: Optional[str], max_workers: int):
    """并行处理文件列表"""
    # 限制最大工作线程数
    actual_workers = min(max_workers, config.max_workers, len(files_list))

    print(f"使用 {actual_workers} 个线程并行处理文件")

    success_count = 0
    error_count = 0

    with ThreadPoolExecutor(max_workers=actual_workers) as executor:
        # 提交所有任务
        future_to_file = {
            executor.submit(add_features_one_safe, file_path, json_dir_path): file_path
            for file_path in files_list
        }

        # 收集结果
        for future in future_to_file:
            file_path = future_to_file[future]
            try:
                result = future.result(timeout=300)  # 5分钟超时
                if result:
                    success_count += 1
                else:
                    error_count += 1
                    logging.warning(f"文件处理失败: {file_path}")
            except Exception as e:
                error_count += 1
                logging.error(f"文件处理异常: {file_path}, 错误: {e}")

    print(f"并行处理完成: 成功 {success_count}, 失败 {error_count}")

def _process_files_sequential(files_list: List[str], json_dir_path: Optional[str]):
    """串行处理文件列表"""
    print("使用串行方式处理文件")

    success_count = 0
    error_count = 0

    for i, file_path in enumerate(files_list, 1):
        print(f"处理进度: {i}/{len(files_list)} - {os.path.basename(file_path)}")
        try:
            result = add_features_one_safe(file_path, json_dir_path)
            if result:
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            error_count += 1
            logging.error(f"文件处理异常: {file_path}, 错误: {e}")

    print(f"串行处理完成: 成功 {success_count}, 失败 {error_count}")

@safe_execute(max_retries=2)
def add_features_one_safe(file_path: str, json_dir_path: Optional[str]) -> bool:
    """安全的单文件处理函数，集成智能内存管理"""
    try:
        add_features_one(file_path, json_dir_path)

        # 智能内存清理：每处理一个文件后检查内存使用
        _smart_memory_cleanup()

        return True
    except Exception as e:
        logging.error(f"处理文件失败: {file_path}, 错误: {e}")
        return False

def _smart_memory_cleanup():
    """智能内存清理机制"""
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024

        # 如果内存使用超过阈值，执行清理
        if memory_mb > config.memory_limit_mb:
            # 清理缓存
            if len(geometry_cache.cache) > config.geometry_cache_size // 2:
                geometry_cache.clear()
            if len(feature_cache.cache) > config.feature_cache_size // 2:
                feature_cache.clear()

            # 清理内存池
            memory_pool.clear()

            # 强制垃圾回收
            gc.collect()

            logging.info(f"执行内存清理: {memory_mb:.2f}MB -> {process.memory_info().rss / 1024 / 1024:.2f}MB")
    except ImportError:
        # 如果psutil不可用，定期执行垃圾回收
        gc.collect()

def load_json_optimized(file_path: str) -> Dict:
    """优化的JSON文件读取，支持压缩格式"""
    # 检查是否有压缩版本
    compressed_path = file_path + '.gz'

    if os.path.exists(compressed_path):
        # 读取压缩文件
        try:
            with gzip.open(compressed_path, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            logging.warning(f"读取压缩文件失败: {compressed_path}, 错误: {e}")

    # 读取原始JSON文件
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json_compressed(data: Dict, file_path: str):
    """保存压缩的JSON文件"""
    compressed_path = file_path + '.gz'
    try:
        with gzip.open(compressed_path, 'wb') as f:
            pickle.dump(data, f)
        logging.info(f"保存压缩文件: {compressed_path}")
    except Exception as e:
        logging.error(f"保存压缩文件失败: {compressed_path}, 错误: {e}")
        # 回退到普通JSON保存
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)


@performance_monitor
def main():
    """优化的主函数 - 多线程版本"""
    print("🚀 开始执行烟叶相关性分析（多线程优化版本）...")

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('correlation_json_generate_optimized.log'),
            logging.StreamHandler()
        ]
    )

    # 显示配置信息
    print(f"📊 配置信息:")
    print(f"   并行处理: {'启用' if config.enable_parallel else '禁用'}")
    print(f"   最大工作线程: {config.max_workers}")
    print(f"   并行特征计算: {'启用' if config.enable_parallel_features else '禁用'}")
    print(f"   缓存: {'启用' if config.enable_cache else '禁用'}")
    print(f"   内存限制: {config.memory_limit_mb}MB")

    # 设置路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    input_json_dir = os.path.join(current_dir, '..', 'split_rgb_hist', 'test_output')
    input_image_dir = os.path.join(current_dir, '..', 'yanye_user_feature', 'vis')
    output_dir = os.path.join(current_dir, 'test_output')

    # 路径标准化
    input_json_dir = os.path.abspath(input_json_dir)
    input_image_dir = os.path.abspath(input_image_dir)
    output_dir = os.path.abspath(output_dir)

    print(f"📁 输入JSON目录: {input_json_dir}")
    print(f"📁 输入图像目录: {input_image_dir}")
    print(f"📁 输出目录: {output_dir}")

    # 验证路径存在性
    if not os.path.exists(input_json_dir):
        print(f"❌ 错误: 输入JSON目录不存在: {input_json_dir}")
        return False
    if not os.path.exists(input_image_dir):
        print(f"❌ 错误: 输入图像目录不存在: {input_image_dir}")
        return False

    # 创建输出目录
    bf.mkdirs(output_dir)

    # 初始化路径转换器
    lpw = bf.linux_path_to_win()

    start_time = time.time()

    try:
        # 优化：并行复制JSON文件
        print("📋 复制JSON文件到输出目录...")
        json_files = [f for f in os.listdir(input_json_dir) if f.endswith('.json')]

        if config.enable_parallel and len(json_files) > 1:
            copied_count = _copy_files_parallel(input_json_dir, output_dir, json_files)
        else:
            copied_count = _copy_files_sequential(input_json_dir, output_dir, json_files)

        print(f"✅ 复制了 {copied_count} 个JSON文件")

        # 执行相关性分析特征计算
        print("🔍 执行相关性分析特征计算（多线程优化版本）...")

        orig_folder_path = lpw.trans(input_image_dir)
        json_dir_path = lpw.trans(output_dir)

        add_features_seq(orig_folder_path, str_condition='bmp', json_dir_path=json_dir_path, processes_amt=config.max_workers)

        end_time = time.time()
        total_time = end_time - start_time

        print("✅ 烟叶相关性分析完成！")
        print(f"⏱️  总耗时: {total_time:.2f}秒")

        # 显示性能统计
        _show_performance_stats()

        return True

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        logging.error(f"主函数执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def _copy_files_parallel(src_dir: str, dst_dir: str, files: List[str]) -> int:
    """并行复制文件"""
    import shutil

    def copy_file(filename):
        try:
            src_path = os.path.join(src_dir, filename)
            dst_path = os.path.join(dst_dir, filename)
            if os.path.exists(src_path):
                shutil.copy2(src_path, dst_path)
                return True
        except Exception as e:
            logging.error(f"复制文件失败: {filename}, 错误: {e}")
        return False

    with ThreadPoolExecutor(max_workers=min(10, config.max_workers)) as executor:
        results = list(executor.map(copy_file, files))

    return sum(results)

def _copy_files_sequential(src_dir: str, dst_dir: str, files: List[str]) -> int:
    """串行复制文件"""
    import shutil

    copied_count = 0
    for filename in files:
        try:
            src_path = os.path.join(src_dir, filename)
            dst_path = os.path.join(dst_dir, filename)
            if os.path.exists(src_path):
                shutil.copy2(src_path, dst_path)
                copied_count += 1
        except Exception as e:
            logging.error(f"复制文件失败: {filename}, 错误: {e}")

    return copied_count

def _show_performance_stats():
    """显示性能统计信息"""
    try:
        print("\n📊 性能统计:")
        print(f"   几何缓存大小: {len(geometry_cache.cache)}")
        print(f"   特征缓存大小: {len(feature_cache.cache)}")
        print(f"   内存池当前大小: {memory_pool.current_size / 1024 / 1024:.2f}MB")

        # 清理缓存
        if len(geometry_cache.cache) > config.geometry_cache_size:
            geometry_cache.clear()
            print("🧹 已清理几何缓存")

        if len(feature_cache.cache) > config.feature_cache_size:
            feature_cache.clear()
            print("🧹 已清理特征缓存")

        if memory_pool.current_size > config.memory_limit_mb * 1024 * 1024:
            memory_pool.clear()
            print("🧹 已清理内存池")

        # 强制垃圾回收
        gc.collect()

    except Exception as e:
        logging.warning(f"性能统计显示失败: {e}")


def create_optimized_config() -> OptimizedCorrelationConfig:
    """创建优化配置"""
    # 根据系统资源动态调整配置
    cpu_count = mp.cpu_count()

    # 基于性能分析的最优线程数设置，避免过度并行导致的上下文切换开销
    max_workers = min(cpu_count, 2)

    # 根据可用内存调整内存限制
    try:
        import psutil
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        memory_limit_mb = min(int(available_memory_gb * 0.3 * 1024), 2048)  # 使用30%可用内存，最大2GB
    except ImportError:
        memory_limit_mb = 1024  # 默认1GB

    return OptimizedCorrelationConfig(
        enable_parallel=True,
        max_workers=max_workers,
        memory_limit_mb=memory_limit_mb,
        enable_cache=True,
        geometry_cache_size=1000,
        feature_cache_size=500,
        batch_size=10,
        enable_batch_processing=True,
        enable_vectorized_computation=True,
        enable_parallel_features=True
    )

def benchmark_performance():
    """性能基准测试"""
    print("\n🏃 开始性能基准测试...")

    # 测试不同配置的性能
    configs = [
        ("串行处理", OptimizedCorrelationConfig(enable_parallel=False, enable_parallel_features=False)),
        ("并行文件处理", OptimizedCorrelationConfig(enable_parallel=True, enable_parallel_features=False)),
        ("全并行处理", OptimizedCorrelationConfig(enable_parallel=True, enable_parallel_features=True))
    ]

    results = {}

    for config_name, test_config in configs:
        print(f"\n测试配置: {config_name}")

        # 临时替换全局配置
        global config
        original_config = config
        config = test_config

        start_time = time.time()
        try:
            success = main()
            end_time = time.time()

            if success:
                results[config_name] = end_time - start_time
                print(f"✅ {config_name} 完成，耗时: {results[config_name]:.2f}秒")
            else:
                print(f"❌ {config_name} 失败")
        except Exception as e:
            print(f"❌ {config_name} 异常: {e}")
        finally:
            # 恢复原始配置
            config = original_config

    # 显示基准测试结果
    if results:
        print(f"\n📊 性能基准测试结果:")
        fastest_config = min(results.keys(), key=lambda k: results[k])
        for config_name, duration in sorted(results.items(), key=lambda x: x[1]):
            speedup = results[fastest_config] / duration if duration > 0 else 0
            print(f"   {config_name}: {duration:.2f}秒 (相对最快: {speedup:.2f}x)")

if __name__ == "__main__":
    # 创建优化配置
    config = create_optimized_config()

    # 更新全局实例
    geo_calc = OptimizedGeometryCalculator(config)
    memory_pool = MemoryPool(config.memory_limit_mb)

    print("🚀 烟叶相关性分析 - 多线程优化版本")
    print("=" * 50)

    # 检查是否运行基准测试
    if len(sys.argv) > 1 and sys.argv[1] == "--benchmark":
        benchmark_performance()
    else:
        success = main()
        if success:
            print("🎉 程序执行成功！")
        else:
            print("💥 程序执行失败！")
            sys.exit(1)
