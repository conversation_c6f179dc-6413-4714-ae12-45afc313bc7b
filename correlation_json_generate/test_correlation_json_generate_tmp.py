#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶相关性分析模块测试程序
测试deploy_correlation_json_generate.py的功能
"""

import os
import sys
import json
import time
import traceback
from pathlib import Path

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)


class CorrelationAnalysisTestRunner:
    """烟叶相关性分析测试运行器"""
    
    def __init__(self):
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.setup_paths()
        self.test_results = {
            'total_files': 0,
            'processed_files': 0,
            'success_files': 0,
            'failed_files': 0,
            'errors': []
        }
        
        # 定义需要验证的特征列表（基于原始代码的header）
        self.expected_features = [
            "mian_ji", "ye_chang", "ye_kuan", "kuan_chang_bi", "zhu_mian_chang", 
            "can_que_ge_shu", "can_que_mian_ji", "can_que_lun_kuo_mian_ji_bi", 
            "jiao_dian_ge_shu", "jiao_dian_mian_ji", "qi_xiang_ban_ge_shu", 
            "qi_xiang_ban_main_ji", "lv_se_qu_yu_ge_shu", "lv_se_qu_yu_mian_ji"
        ]
    
    def setup_paths(self):
        """设置测试路径"""
        self.input_json_dir = os.path.join(self.current_dir, '..', 'split_rgb_hist', 'test_output')
        self.input_image_dir = os.path.join(self.current_dir, '..', 'yanye_user_feature', 'vis')
        self.output_dir = os.path.join(self.current_dir, 'test_output')
        
        # 路径标准化
        self.input_json_dir = os.path.abspath(self.input_json_dir)
        self.input_image_dir = os.path.abspath(self.input_image_dir)
        self.output_dir = os.path.abspath(self.output_dir)
    
    def validate_environment(self):
        """验证测试环境"""
        print("🔍 验证测试环境...")
        
        # 检查输入路径
        if not os.path.exists(self.input_json_dir):
            print(f"❌ 输入JSON目录不存在: {self.input_json_dir}")
            return False
        
        if not os.path.exists(self.input_image_dir):
            print(f"❌ 输入图像目录不存在: {self.input_image_dir}")
            return False
        
        # 统计输入文件数量
        json_files = [f for f in os.listdir(self.input_json_dir) if f.endswith('.json')]
        image_files = [f for f in os.listdir(self.input_image_dir) if f.endswith('.bmp')]
        
        print(f"✅ 输入JSON文件数量: {len(json_files)}")
        print(f"✅ 输入图像文件数量: {len(image_files)}")
        
        self.test_results['total_files'] = len(json_files)
        
        if len(json_files) == 0:
            print("❌ 没有找到输入JSON文件")
            return False
        
        if len(image_files) == 0:
            print("❌ 没有找到输入图像文件")
            return False
        
        return True
    
    def run_deployment(self):
        """运行部署程序"""
        print("🚀 运行部署程序...")
        
        try:
            start_time = time.time()
            success = deploy_main()
            end_time = time.time()
            
            if success:
                print(f"✅ 部署程序执行成功，耗时: {end_time - start_time:.2f}秒")
                return True
            else:
                print("❌ 部署程序执行失败")
                return False
                
        except Exception as e:
            print(f"❌ 部署程序执行异常: {e}")
            traceback.print_exc()
            return False
    
    def validate_output(self):
        """验证输出结果"""
        print("🔍 验证输出结果...")
        
        if not os.path.exists(self.output_dir):
            print(f"❌ 输出目录不存在: {self.output_dir}")
            return False
        
        # 检查输出文件
        input_json_files = [f for f in os.listdir(self.input_json_dir) if f.endswith('.json')]
        output_json_files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]
        
        print(f"📊 输入文件数量: {len(input_json_files)}")
        print(f"📊 输出文件数量: {len(output_json_files)}")
        
        for json_file in input_json_files:
            output_json_path = os.path.join(self.output_dir, json_file)
            
            try:
                if os.path.exists(output_json_path):
                    with open(output_json_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    
                    # 检查是否包含相关性分析特征
                    has_correlation_features = False
                    feature_count = 0
                    missing_features = []
                    
                    if 'user_feature' in json_data:
                        user_feature = json_data['user_feature']
                        
                        # 检查预期的特征
                        for feature_name in self.expected_features:
                            if feature_name in user_feature:
                                feature_count += 1
                            else:
                                missing_features.append(feature_name)
                        
                        # 如果有一半以上的特征存在，认为处理成功
                        if feature_count >= len(self.expected_features) // 2:
                            has_correlation_features = True
                    
                    self.test_results['processed_files'] += 1
                    
                    if has_correlation_features:
                        self.test_results['success_files'] += 1
                        print(f"✅ {json_file}: 相关性分析特征 ✓ ({feature_count}/{len(self.expected_features)})")
                    else:
                        self.test_results['failed_files'] += 1
                        error_msg = f"{json_file}: 缺少相关性分析特征 (仅有{feature_count}/{len(self.expected_features)})"
                        self.test_results['errors'].append(error_msg)
                        print(f"❌ {error_msg}")
                        if missing_features:
                            print(f"   缺少特征: {missing_features[:5]}...")  # 只显示前5个缺少的特征
                else:
                    self.test_results['failed_files'] += 1
                    error_msg = f"{json_file}: 输出文件不存在"
                    self.test_results['errors'].append(error_msg)
                    print(f"❌ {error_msg}")
                
            except Exception as e:
                self.test_results['failed_files'] += 1
                error_msg = f"{json_file}: 处理异常 - {e}"
                self.test_results['errors'].append(error_msg)
                print(f"❌ {error_msg}")
        
        return self.test_results['success_files'] > 0
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 测试报告")
        print("="*60)
        
        print(f"总文件数量: {self.test_results['total_files']}")
        print(f"处理文件数量: {self.test_results['processed_files']}")
        print(f"成功文件数量: {self.test_results['success_files']}")
        print(f"失败文件数量: {self.test_results['failed_files']}")
        
        if self.test_results['total_files'] > 0:
            success_rate = (self.test_results['success_files'] / self.test_results['total_files']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        if self.test_results['errors']:
            print("\n❌ 错误详情:")
            for error in self.test_results['errors'][:10]:  # 只显示前10个错误
                print(f"  - {error}")
            if len(self.test_results['errors']) > 10:
                print(f"  ... 还有 {len(self.test_results['errors']) - 10} 个错误")
        
        print("="*60)
        
        # 判断测试是否通过
        if self.test_results['success_files'] == self.test_results['total_files']:
            print("🎉 所有测试通过！")
            return True
        elif self.test_results['success_files'] > 0:
            print("⚠️  部分测试通过")
            return True
        else:
            print("💥 所有测试失败！")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始烟叶相关性分析模块测试")
        print("="*60)
        
        # 1. 验证环境
        if not self.validate_environment():
            return False
        
        # 2. 运行部署程序
        if not self.run_deployment():
            return False
        
        # 3. 验证输出
        if not self.validate_output():
            return False
        
        # 4. 生成报告
        return self.generate_report()


def main():
    """主函数"""
    test_runner = CorrelationAnalysisTestRunner()
    success = test_runner.run_all_tests()
    
    if success:
        print("\n✅ 测试程序执行成功！")
        return True
    else:
        print("\n❌ 测试程序执行失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
