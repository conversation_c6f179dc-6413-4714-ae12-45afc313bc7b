#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能对比测试脚本
对比优化前后的性能差异
"""

import os
import sys
import time
import json
import psutil
import gc
from typing import Dict, List
import numpy as np

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

class PerformanceComparator:
    """性能对比器 - 使用思维链推理对比优化效果"""
    
    def __init__(self):
        self.results = {}
        self.test_files = self._get_test_files()
        
    def run_comparison(self):
        """运行性能对比测试"""
        print("🔍 开始性能对比测试...")
        print("=" * 60)
        
        if not self.test_files:
            print("❌ 没有找到测试文件")
            return
        
        # 1. 测试I/O性能
        self._test_io_performance()
        
        # 2. 测试内存使用
        self._test_memory_usage()
        
        # 3. 测试并发效率
        self._test_concurrency_efficiency()
        
        # 4. 生成对比报告
        self._generate_comparison_report()
    
    def _test_io_performance(self):
        """测试I/O性能"""
        print("\n📁 I/O性能测试:")
        
        # 测试原始JSON读取
        start_time = time.time()
        original_data = []
        for file_path in self.test_files[:3]:
            with open(file_path, 'r') as f:
                data = json.load(f)
                original_data.append(data)
        original_time = time.time() - start_time
        
        # 测试优化后的读取（如果有压缩文件）
        start_time = time.time()
        optimized_data = []
        for file_path in self.test_files[:3]:
            try:
                from correlation_json_generate.deploy_correlation_json_generate_optimized import load_json_optimized
                data = load_json_optimized(file_path)
                optimized_data.append(data)
            except Exception as e:
                # 回退到原始方法
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    optimized_data.append(data)
        optimized_time = time.time() - start_time
        
        # 计算改进
        io_improvement = (original_time - optimized_time) / original_time * 100 if original_time > 0 else 0
        
        self.results['io_performance'] = {
            'original_time': original_time,
            'optimized_time': optimized_time,
            'improvement_percent': io_improvement
        }
        
        print(f"   原始I/O耗时: {original_time:.4f}秒")
        print(f"   优化I/O耗时: {optimized_time:.4f}秒")
        print(f"   性能改进: {io_improvement:.1f}%")
    
    def _test_memory_usage(self):
        """测试内存使用"""
        print("\n💾 内存使用测试:")
        
        # 获取初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        # 模拟大量数据处理（原始方式）
        print("   测试原始内存管理...")
        large_arrays = []
        for i in range(50):
            arr = np.random.rand(1000, 1000)
            large_arrays.append(arr)
        
        peak_memory_original = process.memory_info().rss / 1024 / 1024
        
        # 清理
        del large_arrays
        gc.collect()
        
        # 模拟优化后的内存管理
        print("   测试优化内存管理...")
        from correlation_json_generate.deploy_correlation_json_generate_optimized import _smart_memory_cleanup
        
        large_arrays_optimized = []
        for i in range(50):
            arr = np.random.rand(1000, 1000)
            large_arrays_optimized.append(arr)
            
            # 每10个数组执行一次智能清理
            if i % 10 == 0:
                _smart_memory_cleanup()
        
        peak_memory_optimized = process.memory_info().rss / 1024 / 1024
        
        # 清理
        del large_arrays_optimized
        gc.collect()
        
        # 计算改进
        memory_improvement = (peak_memory_original - peak_memory_optimized) / peak_memory_original * 100 if peak_memory_original > 0 else 0
        
        self.results['memory_usage'] = {
            'initial_memory': initial_memory,
            'peak_memory_original': peak_memory_original,
            'peak_memory_optimized': peak_memory_optimized,
            'improvement_percent': memory_improvement
        }
        
        print(f"   初始内存: {initial_memory:.2f}MB")
        print(f"   原始峰值内存: {peak_memory_original:.2f}MB")
        print(f"   优化峰值内存: {peak_memory_optimized:.2f}MB")
        print(f"   内存改进: {memory_improvement:.1f}%")
    
    def _test_concurrency_efficiency(self):
        """测试并发效率"""
        print("\n⚡ 并发效率测试:")
        
        from concurrent.futures import ThreadPoolExecutor
        import time
        
        def dummy_task(x):
            """模拟计算任务"""
            time.sleep(0.01)
            return x * x
        
        test_data = list(range(20))
        
        # 测试原始配置（8线程）
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=8) as executor:
            results_8_threads = list(executor.map(dummy_task, test_data))
        time_8_threads = time.time() - start_time
        
        # 测试优化配置（2线程）
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=2) as executor:
            results_2_threads = list(executor.map(dummy_task, test_data))
        time_2_threads = time.time() - start_time
        
        # 计算效率改进
        efficiency_improvement = (time_8_threads - time_2_threads) / time_8_threads * 100 if time_8_threads > 0 else 0
        
        self.results['concurrency_efficiency'] = {
            'time_8_threads': time_8_threads,
            'time_2_threads': time_2_threads,
            'improvement_percent': efficiency_improvement
        }
        
        print(f"   8线程耗时: {time_8_threads:.4f}秒")
        print(f"   2线程耗时: {time_2_threads:.4f}秒")
        print(f"   效率改进: {efficiency_improvement:.1f}%")
    
    def _generate_comparison_report(self):
        """生成对比报告"""
        print("\n📊 性能对比总结:")
        print("=" * 60)
        
        total_improvements = []
        
        # I/O性能改进
        if 'io_performance' in self.results:
            io_improvement = self.results['io_performance']['improvement_percent']
            total_improvements.append(io_improvement)
            status = "✅" if io_improvement > 0 else "⚠️"
            print(f"{status} I/O性能改进: {io_improvement:.1f}%")
        
        # 内存使用改进
        if 'memory_usage' in self.results:
            memory_improvement = self.results['memory_usage']['improvement_percent']
            total_improvements.append(memory_improvement)
            status = "✅" if memory_improvement > 0 else "⚠️"
            print(f"{status} 内存使用改进: {memory_improvement:.1f}%")
        
        # 并发效率改进
        if 'concurrency_efficiency' in self.results:
            concurrency_improvement = self.results['concurrency_efficiency']['improvement_percent']
            total_improvements.append(concurrency_improvement)
            status = "✅" if concurrency_improvement > 0 else "⚠️"
            print(f"{status} 并发效率改进: {concurrency_improvement:.1f}%")
        
        # 计算总体改进
        if total_improvements:
            avg_improvement = sum(total_improvements) / len(total_improvements)
            print(f"\n🎯 总体性能改进: {avg_improvement:.1f}%")
            
            if avg_improvement > 20:
                print("🎉 优化效果显著！")
            elif avg_improvement > 10:
                print("👍 优化效果良好！")
            elif avg_improvement > 0:
                print("📈 有一定改进空间")
            else:
                print("⚠️ 需要进一步优化")
        
        # 保存详细结果
        self._save_comparison_results()
    
    def _save_comparison_results(self):
        """保存对比结果"""
        output_file = os.path.join(current_dir, 'performance_comparison_results.json')
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            print(f"\n📄 详细对比结果已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存对比结果失败: {e}")
    
    def _get_test_files(self) -> List[str]:
        """获取测试文件列表"""
        test_dir = os.path.join(current_dir, 'test_output')
        if not os.path.exists(test_dir):
            return []
        return [os.path.join(test_dir, f) for f in os.listdir(test_dir) if f.endswith('.json')][:5]

def main():
    """主函数"""
    print("🚀 性能对比测试工具")
    print("对比优化前后的性能差异")
    
    comparator = PerformanceComparator()
    comparator.run_comparison()
    
    print("\n🎉 性能对比测试完成！")

if __name__ == "__main__":
    main()
