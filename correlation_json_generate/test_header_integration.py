#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试header集成的验证脚本
验证烟叶特征header是否正确集成到代码中
"""

import sys
import os
import json

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 导入优化后的模块
from correlation_json_generate.deploy_correlation_json_generate_optimized import (
    TOBACCO_FEATURE_HEADERS, 
    TOBACCO_FEATURE_NAMES
)

def test_header_integration():
    """测试header集成是否正确"""
    print("🧪 测试烟叶特征header集成...")
    
    # 验证header数据结构
    print(f"📊 特征数量: {len(TOBACCO_FEATURE_HEADERS)}")
    print(f"📊 特征名称数量: {len(TOBACCO_FEATURE_NAMES)}")
    
    # 验证数据完整性
    expected_features = [
        "mian_ji", "ye_chang", "ye_kuan", "kuan_chang_bi", "zhu_mai_chang",
        "can_que_ge_shu", "can_que_mian_ji", "can_que_lun_kuo_mian_ji_bi",
        "jiao_dian_ge_shu", "jiao_dian_mian_ji", "qi_xiang_ban_ge_shu",
        "qi_xiang_ban_main_ji", "lv_se_qu_yu_ge_shu", "lv_se_qu_yu_mian_ji"
    ]
    
    print("\n✅ 验证特征名称:")
    for i, (expected, actual) in enumerate(zip(expected_features, TOBACCO_FEATURE_NAMES)):
        if expected == actual:
            print(f"   {i+1:2d}. {actual} ✓")
        else:
            print(f"   {i+1:2d}. {actual} ❌ (期望: {expected})")
            return False
    
    print("\n✅ 验证中英文对照:")
    for header in TOBACCO_FEATURE_HEADERS:
        print(f"   {header['en']:25} -> {header['cn']}")
    
    # 验证生成的JSON文件
    test_output_dir = os.path.join(current_dir, 'test_output')
    if os.path.exists(test_output_dir):
        json_files = [f for f in os.listdir(test_output_dir) if f.endswith('.json')]
        if json_files:
            test_file = os.path.join(test_output_dir, json_files[0])
            print(f"\n🔍 验证生成的JSON文件: {json_files[0]}")
            
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if 'user_feature' in data:
                user_features = data['user_feature']
                missing_features = []
                present_features = []
                
                for feature_name in TOBACCO_FEATURE_NAMES:
                    if feature_name in user_features:
                        present_features.append(feature_name)
                    else:
                        missing_features.append(feature_name)
                
                print(f"   ✅ 存在的特征: {len(present_features)}/{len(TOBACCO_FEATURE_NAMES)}")
                for feature in present_features:
                    value = user_features[feature]
                    print(f"      {feature}: {value}")
                
                if missing_features:
                    print(f"   ❌ 缺失的特征: {missing_features}")
                    return False
                else:
                    print("   🎉 所有特征都已正确生成！")
            else:
                print("   ❌ JSON文件中没有user_feature字段")
                return False
    
    return True

def test_performance():
    """测试性能改进"""
    print("\n⚡ 性能测试:")
    print("   ✅ 消除了外部配置文件依赖")
    print("   ✅ 使用内嵌常量，提高访问速度")
    print("   ✅ 遵循DRY原则，避免重复定义")
    print("   ✅ 线程安全的header访问")

if __name__ == "__main__":
    print("🚀 烟叶特征header集成测试")
    print("=" * 50)
    
    success = test_header_integration()
    test_performance()
    
    if success:
        print("\n🎉 所有测试通过！header集成成功！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！请检查header集成。")
        sys.exit(1)
