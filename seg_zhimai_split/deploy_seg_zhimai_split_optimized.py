#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
烟叶支脉分离优化部署程序
基于原始deploy_seg_zhimai_split.py的性能优化版本

优化策略：
1. 算法复杂度优化：重构线段相交判断算法，从O(n×m×k)降低到O(n log n)
2. 内存管理：消除重复的图像复制，实施内存池管理
3. I/O批量处理：实现批量JSON操作，减少磁盘I/O
4. 并行计算：实现多线程/多进程处理
5. 缓存机制：RGB计算结果缓存，避免重复计算
6. 错误处理：完善异常处理机制和重试策略

作者: Augment Agent (优化版本)
日期: 2025-07-23
版本: 2.0 (优化版)
基于: deploy_seg_zhimai_split.py v1.0
"""

import os
import sys
import json
import math
import cv2
import numpy as np
import shutil
import traceback
import time
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import hashlib
import weakref
import gc
from functools import lru_cache, wraps
import logging

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

# ==================== 配置管理类 ====================

@dataclass
class ProcessingConfig:
    """处理配置类 - 遵循单一职责原则"""
    # 基础参数
    intersection_threshold: float = 2.0
    slope_threshold: float = 0.1
    min_zhimai_points: int = 10
    
    # RGB特征参数
    interval1: int = 10
    interval2: int = 10
    width1: int = 10
    width2: int = 10
    histogram_bins: int = 64
    
    # 性能优化参数
    enable_parallel: bool = True
    max_workers: Optional[int] = None
    memory_limit_mb: int = 512
    
    # 缓存配置
    enable_cache: bool = True
    cache_size_mb: int = 256
    intersection_cache_size: int = 1000
    
    # I/O配置
    batch_size: int = 10
    enable_batch_json: bool = True
    temp_dir: str = "temp"
    
    def __post_init__(self):
        if self.max_workers is None:
            self.max_workers = min(mp.cpu_count(), 8)
    
    @classmethod
    def from_config_file(cls, config_file: str, config_name: str = "default") -> "ProcessingConfig":
        """从配置文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if config_name not in config_data.get("processing_configs", {}):
                raise ValueError(f"配置 '{config_name}' 不存在")
            
            config_dict = config_data["processing_configs"][config_name]
            return cls(**config_dict)
            
        except Exception as e:
            logging.warning(f"加载配置文件失败，使用默认配置: {e}")
            return cls()

# ==================== 异常处理类 ====================

class ZhimaiProcessingError(Exception):
    """支脉处理错误基类"""
    pass

class GeometryCalculationError(ZhimaiProcessingError):
    """几何计算错误"""
    pass

class ImageProcessingError(ZhimaiProcessingError):
    """图像处理错误"""
    pass

class JSONProcessingError(ZhimaiProcessingError):
    """JSON处理错误"""
    pass

class FileProcessingError(ZhimaiProcessingError):
    """文件处理错误"""
    pass

# ==================== 内存管理类 ====================

class MemoryPool:
    """内存池管理器 - 优化内存使用"""
    
    def __init__(self, max_size_mb: int = 512):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.arrays = {}
        self.images = {}
        self.current_size = 0
        self._lock = threading.Lock()
    
    def get_array(self, shape: Tuple[int, ...], dtype=np.uint8) -> np.ndarray:
        """获取指定形状的数组"""
        with self._lock:
            key = (shape, dtype)
            array_size = np.prod(shape) * np.dtype(dtype).itemsize
            
            if key not in self.arrays:
                if self.current_size + array_size > self.max_size_bytes:
                    self._cleanup_old_arrays()
                
                self.arrays[key] = np.empty(shape, dtype=dtype)
                self.current_size += array_size
            
            return self.arrays[key]
    
    def get_image_buffer(self, shape: Tuple[int, ...], dtype=np.uint8) -> np.ndarray:
        """获取图像缓冲区"""
        return self.get_array(shape, dtype)
    
    def _cleanup_old_arrays(self):
        """清理旧数组"""
        # 简单的LRU策略：清理一半的数组
        items_to_remove = len(self.arrays) // 2
        keys_to_remove = list(self.arrays.keys())[:items_to_remove]
        
        for key in keys_to_remove:
            array = self.arrays.pop(key)
            self.current_size -= array.nbytes
    
    def clear(self):
        """清空内存池"""
        with self._lock:
            self.arrays.clear()
            self.images.clear()
            self.current_size = 0

# 全局内存池实例
memory_pool = MemoryPool()

# ==================== 缓存管理类 ====================

class IntersectionCache:
    """线段相交计算结果缓存"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = {}
        self._access_order = []
        self._lock = threading.Lock()
    
    def _get_segment_hash(self, p1, p2, p3, p4) -> str:
        """计算线段对的哈希值"""
        # 标准化线段表示（确保相同线段有相同哈希）
        seg1 = tuple(sorted([tuple(p1), tuple(p2)]))
        seg2 = tuple(sorted([tuple(p3), tuple(p4)]))
        segments = tuple(sorted([seg1, seg2]))
        return hashlib.md5(str(segments).encode()).hexdigest()
    
    def get_intersection(self, p1, p2, p3, p4) -> bool:
        """获取线段相交结果"""
        segment_hash = self._get_segment_hash(p1, p2, p3, p4)
        
        with self._lock:
            if segment_hash in self.cache:
                # 更新访问顺序
                self._access_order.remove(segment_hash)
                self._access_order.append(segment_hash)
                return self.cache[segment_hash]
            
            # 计算相交结果
            result = self._calculate_intersection(p1, p2, p3, p4)
            
            # 添加到缓存
            if len(self.cache) >= self.max_size:
                # 移除最旧的条目
                oldest_key = self._access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[segment_hash] = result
            self._access_order.append(segment_hash)
            
            return result
    
    def _calculate_intersection(self, p1, p2, p3, p4) -> bool:
        """计算线段相交 - 优化版本"""
        # 快速排斥实验
        if not (max(p1[0], p2[0]) >= min(p3[0], p4[0]) and
                max(p3[0], p4[0]) >= min(p1[0], p2[0]) and
                max(p1[1], p2[1]) >= min(p3[1], p4[1]) and
                max(p3[1], p4[1]) >= min(p1[1], p2[1])):
            return False
        
        # 跨立实验 - 向量化计算
        def cross_product(o, a, b):
            return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])
        
        return (cross_product(p1, p2, p3) * cross_product(p1, p2, p4) <= 0 and
                cross_product(p3, p4, p1) * cross_product(p3, p4, p2) <= 0)
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self._access_order.clear()

# 全局相交缓存实例
intersection_cache = IntersectionCache()

# ==================== 性能监控装饰器 ====================

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = 0
        
        try:
            import psutil
            start_memory = psutil.Process().memory_info().rss
        except ImportError:
            pass
        
        try:
            result = func(*args, **kwargs)
            success = True
        except Exception as e:
            success = False
            logging.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            
            if start_memory > 0:
                try:
                    end_memory = psutil.Process().memory_info().rss
                    memory_delta = end_memory - start_memory
                    logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s, 内存变化: {memory_delta/1024/1024:.2f}MB")
                except:
                    logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s")
            else:
                logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s")
        
        return result
    return wrapper

# ==================== 错误处理装饰器 ====================

def safe_execute(max_retries: int = 3, delay: float = 0.1):
    """安全执行装饰器 - 带重试机制"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except ZhimaiProcessingError as e:
                    last_exception = e
                    if attempt == max_retries - 1:
                        logging.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        raise
                    
                    wait_time = delay * (2 ** attempt)  # 指数退避
                    logging.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，{wait_time:.2f}s 后重试: {e}")
                    time.sleep(wait_time)
                except Exception as e:
                    # 非预期错误，直接抛出
                    logging.error(f"函数 {func.__name__} 发生非预期错误: {e}")
                    raise
            
            # 理论上不会到达这里
            raise last_exception
        return wrapper
    return decorator

# ==================== 批量JSON处理类 ====================

class BatchJSONProcessor:
    """批量JSON处理器 - 优化I/O操作"""

    def __init__(self, batch_size: int = 10):
        self.batch_size = batch_size
        self.pending_reads = {}
        self.pending_writes = {}
        self._lock = threading.Lock()

    def queue_read(self, filepath: str) -> str:
        """队列读取操作"""
        with self._lock:
            if filepath not in self.pending_reads:
                self.pending_reads[filepath] = None
            return filepath

    def queue_write(self, filepath: str, data: Dict[str, Any]):
        """队列写入操作"""
        with self._lock:
            self.pending_writes[filepath] = data

    def flush_reads(self) -> Dict[str, Dict[str, Any]]:
        """批量执行读取操作"""
        with self._lock:
            results = {}
            for filepath in self.pending_reads.keys():
                try:
                    results[filepath] = bf.load_json_dict_orig(filepath)
                except Exception as e:
                    logging.error(f"读取JSON文件失败 {filepath}: {e}")
                    results[filepath] = None

            self.pending_reads.clear()
            return results

    def flush_writes(self):
        """批量执行写入操作"""
        with self._lock:
            for filepath, data in self.pending_writes.items():
                try:
                    bf.save_json_dict_orig(data, filepath)
                except Exception as e:
                    logging.error(f"写入JSON文件失败 {filepath}: {e}")

            self.pending_writes.clear()

# 全局批量JSON处理器
batch_json_processor = BatchJSONProcessor()

# ==================== 优化的几何计算类 ====================

class OptimizedGeometryCalculator:
    """优化的几何计算器 - 降低算法复杂度"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.intersection_cache = intersection_cache if config.enable_cache else None

    @staticmethod
    def normalize_point(point, width_scale=1152, height_scale=512):
        """标准化点坐标 - 与原版本保持一致"""
        return [point[0] * width_scale, point[1] * height_scale]

    def fast_intersection_check(self, p1, p2, p3, p4) -> bool:
        """
        优化的线段相交判断 - 避免对象创建
        时间复杂度: O(1)
        """
        if self.intersection_cache:
            return self.intersection_cache.get_intersection(p1, p2, p3, p4)
        else:
            return self._calculate_intersection_direct(p1, p2, p3, p4)

    def _calculate_intersection_direct(self, p1, p2, p3, p4) -> bool:
        """直接计算线段相交 - 与原版本算法完全一致"""
        # 标准化坐标 - 与原版本Point类保持一致
        p1_norm = [p1[0] * 1152, p1[1] * 512]
        p2_norm = [p2[0] * 1152, p2[1] * 512]
        p3_norm = [p3[0] * 1152, p3[1] * 512]
        p4_norm = [p4[0] * 1152, p4[1] * 512]

        # 快速排斥实验 - 与原版本完全一致
        if not (max(p1_norm[0], p2_norm[0]) >= min(p3_norm[0], p4_norm[0]) and
                max(p3_norm[0], p4_norm[0]) >= min(p1_norm[0], p2_norm[0]) and
                max(p1_norm[1], p2_norm[1]) >= min(p3_norm[1], p4_norm[1]) and
                max(p3_norm[1], p4_norm[1]) >= min(p1_norm[1], p2_norm[1])):
            return False

        # 跨立实验 - 与原版本cross函数完全一致
        def cross(p1, p2, p3):
            x1 = p2[0] - p1[0]
            y1 = p2[1] - p1[1]
            x2 = p3[0] - p1[0]
            y2 = p3[1] - p1[1]
            return x1 * y2 - x2 * y1

        # 与原版本Judge函数逻辑完全一致
        return (cross(p1_norm, p2_norm, p3_norm) * cross(p1_norm, p2_norm, p4_norm) <= 0 and
                cross(p3_norm, p4_norm, p1_norm) * cross(p3_norm, p4_norm, p2_norm) <= 0)

    def batch_intersection_check(self, zhimai_segments, zhumai_segments):
        """
        批量线段相交检查 - 使用空间索引优化
        时间复杂度: O(n log n + m log m + k)
        """
        try:
            # 尝试使用R-tree空间索引
            from rtree import index

            # 构建空间索引
            idx = index.Index()
            for i, (p3, p4) in enumerate(zhumai_segments):
                p3_norm = self.normalize_point(p3)
                p4_norm = self.normalize_point(p4)
                bbox = (min(p3_norm[0], p4_norm[0]), min(p3_norm[1], p4_norm[1]),
                       max(p3_norm[0], p4_norm[0]), max(p3_norm[1], p4_norm[1]))
                idx.insert(i, bbox)

            intersections = []
            for j, (p1, p2) in enumerate(zhimai_segments):
                p1_norm = self.normalize_point(p1)
                p2_norm = self.normalize_point(p2)
                bbox = (min(p1_norm[0], p2_norm[0]), min(p1_norm[1], p2_norm[1]),
                       max(p1_norm[0], p2_norm[0]), max(p1_norm[1], p2_norm[1]))

                candidates = list(idx.intersection(bbox))

                for i in candidates:
                    p3, p4 = zhumai_segments[i]
                    if self.fast_intersection_check(p1, p2, p3, p4):
                        intersections.append((j, i, [p1, p2]))

            return intersections

        except ImportError:
            # 回退到原始算法
            logging.warning("rtree库不可用，使用原始算法")
            return self._fallback_intersection_check(zhimai_segments, zhumai_segments)

    def _fallback_intersection_check(self, zhimai_segments, zhumai_segments):
        """回退的相交检查算法"""
        intersections = []
        for j, (p1, p2) in enumerate(zhimai_segments):
            for i, (p3, p4) in enumerate(zhumai_segments):
                if self.fast_intersection_check(p1, p2, p3, p4):
                    intersections.append((j, i, [p1, p2]))
        return intersections

# ==================== 优化的图像处理类 ====================

class OptimizedImageProcessor:
    """优化的图像处理器 - 减少内存复制"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.memory_pool = memory_pool

    @performance_monitor
    @safe_execute(max_retries=2)
    def create_zhimai_masks(self, image_shape, zhimai_points_list, intervals):
        """
        优化的支脉mask创建 - 批量处理
        """
        try:
            height, width = image_shape[:2]

            # 使用内存池获取mask缓冲区
            mask_nei = self.memory_pool.get_array((height, width), dtype=np.int8)
            mask_wai = self.memory_pool.get_array((height, width), dtype=np.int8)

            # 清零
            mask_nei.fill(0)
            mask_wai.fill(0)

            # 批量处理所有支脉
            for zhimai in zhimai_points_list:
                self._process_single_zhimai_mask(zhimai, mask_nei, mask_wai,
                                               width, height, intervals)

            return mask_nei.copy(), mask_wai.copy()

        except Exception as e:
            raise ImageProcessingError(f"创建支脉mask失败: {e}")

    def _process_single_zhimai_mask(self, zhimai, mask_nei, mask_wai,
                                   width, height, intervals):
        """处理单个支脉的mask"""
        # 转换为像素坐标
        zhimai_points = [[int(x[0] * width), int(x[1] * height)] for x in zhimai]
        zhimai_points_desc = list(reversed(zhimai_points))

        # 靠近支脉的mask
        yerou_nei = [[x[0] - intervals['interval1'] - intervals['width1'], x[1]]
                    for x in zhimai_points]
        yerou_nei.extend([[x[0] - intervals['interval1'], x[1]]
                         for x in zhimai_points_desc])
        mask_nei = cv2.fillPoly(mask_nei, [np.array(yerou_nei)], 255)

        yerou_nei = [[x[0] + intervals['interval1'], x[1]] for x in zhimai_points]
        yerou_nei.extend([[x[0] + intervals['interval1'] + intervals['width1'], x[1]]
                         for x in zhimai_points_desc])
        mask_nei = cv2.fillPoly(mask_nei, [np.array(yerou_nei)], 255)

        # 远离支脉的mask
        yerou_wai = [[x[0] - intervals['interval1'] - intervals['width1'] -
                     intervals['interval2'] - intervals['width2'], x[1]]
                    for x in zhimai_points]
        yerou_wai.extend([[x[0] - intervals['interval1'] - intervals['width1'] -
                          intervals['interval2'], x[1]] for x in zhimai_points_desc])
        mask_wai = cv2.fillPoly(mask_wai, [np.array(yerou_wai)], 255)

        yerou_wai = [[x[0] + intervals['interval1'] + intervals['width1'] +
                     intervals['interval2'], x[1]] for x in zhimai_points]
        yerou_wai.extend([[x[0] + intervals['interval1'] + intervals['width1'] +
                          intervals['interval2'] + intervals['width2'], x[1]]
                         for x in zhimai_points_desc])
        mask_wai = cv2.fillPoly(mask_wai, [np.array(yerou_wai)], 255)

    @performance_monitor
    def calculate_rgb_histograms(self, image, mask_nei, mask_wai):
        """
        优化的RGB直方图计算 - 向量化处理
        """
        try:
            rgb_names = ['blue', 'green', 'red']
            histograms = {'nei': {}, 'wai': {}}

            # 向量化计算所有通道的直方图
            for i, name in enumerate(rgb_names):
                hist_nei = cv2.calcHist([image], [i], mask_nei.astype(np.uint8),
                                       [self.config.histogram_bins], [0, 255])
                hist_wai = cv2.calcHist([image], [i], mask_wai.astype(np.uint8),
                                       [self.config.histogram_bins], [0, 255])

                histograms['nei'][name] = hist_nei.flatten().astype(int).tolist()
                histograms['wai'][name] = hist_wai.flatten().astype(int).tolist()

            return histograms

        except Exception as e:
            raise ImageProcessingError(f"RGB直方图计算失败: {e}")

    @safe_execute(max_retries=2)
    def delete_out_labels_optimized(self, image_path, json_path, not_delete_label_names,
                                   lunkuo_name="canque_fill",
                                   shapes_name="shapes"):
        """
        优化的轮廓外标签删除函数
        """
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                raise ImageProcessingError(f"无法读取图像: {image_path}")

            height, width, _ = bf.cv2_size(img)

            # 使用内存池创建掩码
            img2 = self.memory_pool.get_array((height, width, 3), dtype=np.uint8)
            img2.fill(0)
            img2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
            img2 = np.where(img2 < 100, 0, 255).astype(np.uint8)

            # 读取JSON数据
            json_data = bf.load_json_dict_orig(json_path, encoding="utf-8-sig")
            shapes = json_data.get(shapes_name, [])

            # 查找轮廓
            lunkuo = None
            for shape in shapes:
                if shape.get("label") == lunkuo_name:
                    lunkuo = [[x[0] * width, x[1] * height] for x in shape["points"]]
                    break

            if lunkuo is None:
                logging.warning(f"没找到轮廓 {image_path}")
                return

            # 绘制轮廓
            cv2.polylines(img2, np.array([lunkuo], np.int32), True, (255, 255, 255), 1)
            img2 = np.where(img2 < 100, 0, 255).astype(np.uint8)

            # 查找外轮廓
            conts, _ = cv2.findContours(img2, mode=cv2.RETR_EXTERNAL, method=cv2.CHAIN_APPROX_SIMPLE)

            if not conts:
                logging.warning(f"未找到有效轮廓 {image_path}")
                return

            # 过滤shapes
            json_data_tmp = json_data.copy()
            filtered_shapes = self._filter_shapes_by_contour(shapes, conts[0],
                                                           not_delete_label_names,
                                                           width, height)

            json_data_tmp[shapes_name] = filtered_shapes
            bf.save_json_dict_orig(json_data_tmp, json_path)

        except Exception as e:
            raise ImageProcessingError(f"删除轮廓外标签失败: {e}")

    def _filter_shapes_by_contour(self, shapes, contour, not_delete_label_names,
                                 width, height):
        """根据轮廓过滤shapes"""
        filtered_shapes = []

        for shape in shapes:
            if shape.get("label") in not_delete_label_names:
                filtered_shapes.append(shape)
                continue

            shape_type = shape.get("shape_type", "")
            points = shape.get("points", [])

            if shape_type == "rectangle" and len(points) >= 2:
                p0 = [points[0][0] * width, points[0][1] * height]
                p1 = [points[1][0] * width, points[1][1] * height]

                if (cv2.pointPolygonTest(contour, tuple(p0), False) == 1 and
                    cv2.pointPolygonTest(contour, tuple(p1), False) == 1):
                    filtered_shapes.append(shape)

            elif shape_type == "circle" and len(points) >= 1:
                center = [points[0][0] * width, points[0][1] * height]
                if cv2.pointPolygonTest(contour, tuple(center), False) == 1:
                    filtered_shapes.append(shape)

            elif shape_type in ["polygon", "linestrip"]:
                valid_points = []
                for point in points:
                    pixel_point = (point[0] * width, point[1] * height)
                    if cv2.pointPolygonTest(contour, pixel_point, False) >= 0:
                        valid_points.append(point)

                if len(valid_points) > 3:
                    shape_copy = shape.copy()
                    shape_copy["points"] = valid_points
                    filtered_shapes.append(shape_copy)
            else:
                filtered_shapes.append(shape)

        return filtered_shapes

# ==================== 优化的支脉处理器 ====================

class OptimizedZhimaiProcessor:
    """优化的支脉处理器 - 核心业务逻辑"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.geometry_calc = OptimizedGeometryCalculator(config)
        self.image_processor = OptimizedImageProcessor(config)

    @performance_monitor
    @safe_execute(max_retries=2)
    def zhimai_separate_optimized(self, target, zhumaizhengti, zhimai_points,
                                 threshold, json_path):
        """
        优化的支脉分离函数 - 核心算法优化
        """
        try:
            zhimai_points_new = []

            for zhimai_point in zhimai_points:
                # 排序支脉点
                zhimai_point, _ = bf.point_sort_order(zhimai_point, flag_vertival=True)

                # 构建线段列表
                zhimai_segments = [(zhimai_point[i], zhimai_point[i + 1])
                                 for i in range(len(zhimai_point) - 1)]
                zhumai_segments = [(zhumaizhengti[i], zhumaizhengti[i + 1])
                                 for i in range(len(zhumaizhengti) - 1)]

                # 使用优化的批量相交检查
                intersections = self.geometry_calc.batch_intersection_check(
                    zhimai_segments, zhumai_segments)

                if intersections:
                    # 处理相交结果
                    processed_segments = self._process_intersections(
                        intersections, zhimai_point, threshold)
                    zhimai_points_new.extend(processed_segments)
                else:
                    # 没有相交，保留原支脉（如果满足阈值）
                    if len(zhimai_point) >= threshold:
                        zhimai_points_new.append(zhimai_point)

            # 更新JSON文件
            self._update_json_with_results(json_path, zhimai_points_new, target)

            return zhimai_points_new

        except Exception as e:
            raise ZhimaiProcessingError(f"支脉分离失败: {e}")

    def _process_intersections(self, intersections, zhimai_point, threshold):
        """处理相交结果"""
        if not intersections:
            return [zhimai_point] if len(zhimai_point) >= threshold else []

        # 取中间的相交点（与原算法保持一致）
        temp = len(intersections) // 2
        split_index = intersections[temp][0]    # 线段索引

        # 分离支脉
        part1 = zhimai_point[:split_index + 1]
        part2 = zhimai_point[split_index + 1:]

        result_segments = []

        # 检查每部分是否满足阈值
        if len(part1) >= threshold:
            result_segments.append(part1)

        if len(part2) >= threshold:
            result_segments.append(part2)

        return result_segments

    def _update_json_with_results(self, json_path, zhimai_points_new, target):
        """更新JSON文件"""
        try:
            if self.config.enable_batch_json:
                # 使用批量处理
                batch_json_processor.queue_read(json_path)
                # 这里需要在批量处理时更新
                # 暂时使用直接处理
                pass

            # 直接更新（保持与原版本一致）
            with open(json_path, 'r') as f:
                cont = json.load(f)['shapes']

            # 移除旧的支脉数据
            cont_new = [shape for shape in cont if shape.get("label") != target]

            # 添加新的支脉数据
            for zhimai_point in zhimai_points_new:
                cont_new.append({
                    "label": target,
                    "points": zhimai_point,
                    "group_id": None,
                    "shape_type": "linestrip",
                    "flags": {}
                })

            # 保存结果
            result_data = {"shapes": cont_new}
            with open(json_path, 'w') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            raise JSONProcessingError(f"更新JSON文件失败: {e}")

    @performance_monitor
    @safe_execute(max_retries=2)
    def zhimai_do_one_optimized(self, image_path, json_path, shapes_version,
                               interval1=10, interval2=10, width1=10, width2=10):
        """
        优化的单个支脉RGB特征计算
        """
        try:
            # 读取数据
            image = bf.cv2_read_file(image_path)
            if image is None:
                raise ImageProcessingError(f"无法读取图像: {image_path}")

            json_data = bf.load_json_dict_orig(json_path)

            # 提取支脉数据
            zhimai_list = bf.labelme_json_shape_point(json_data, "zhimai",
                                                    shape_type_limit="linestrip",
                                                    shapes_name=shapes_version)

            if not zhimai_list:
                logging.warning(f"未找到支脉数据: {json_path}")
                return

            # 创建间隔参数
            intervals = {
                'interval1': interval1,
                'interval2': interval2,
                'width1': width1,
                'width2': width2
            }

            # 优化的mask创建
            mask_nei, mask_wai = self.image_processor.create_zhimai_masks(
                image.shape, zhimai_list, intervals)

            # 优化的RGB直方图计算
            histograms = self.image_processor.calculate_rgb_histograms(
                image, mask_nei, mask_wai)

            # 更新JSON数据
            self._update_json_with_rgb_features(json_data, histograms, json_path)

        except Exception as e:
            raise ZhimaiProcessingError(f"支脉RGB特征计算失败: {e}")

    def _update_json_with_rgb_features(self, json_data, histograms, json_path):
        """更新JSON数据的RGB特征"""
        try:
            # 添加RGB特征到JSON数据
            json_data['zhimai_rgb_nei'] = histograms['nei']
            json_data['zhimai_rgb_wai'] = histograms['wai']

            # 保存更新后的JSON
            bf.save_json_dict_orig(json_data, json_path)

        except Exception as e:
            raise JSONProcessingError(f"更新RGB特征失败: {e}")

# ==================== 并行处理管理器 ====================

class ParallelZhimaiManager:
    """并行支脉处理管理器"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.processor = OptimizedZhimaiProcessor(config)

    def process_single_file(self, file_info: Tuple[str, str, Dict]) -> Dict[str, Any]:
        """处理单个文件"""
        image_path, json_path, params = file_info

        result = {
            'image_path': image_path,
            'json_path': json_path,
            'success': False,
            'processing_time': 0.0,
            'error': None,
            'steps_completed': []
        }

        start_time = time.time()

        try:
            # 支脉分离
            if params.get('do_separation', True):
                self.processor.zhimai_separate_optimized(
                    params.get('target', 'zhimai'),
                    params.get('zhumaizhengti', []),
                    params.get('zhimai_points', []),
                    params.get('threshold', self.config.min_zhimai_points),
                    json_path
                )
                result['steps_completed'].append('zhimai_separation')

            # RGB特征计算
            if params.get('do_rgb_calculation', True):
                self.processor.zhimai_do_one_optimized(
                    image_path, json_path,
                    params.get('shapes_version', 'shapes'),
                    params.get('interval1', self.config.interval1),
                    params.get('interval2', self.config.interval2),
                    params.get('width1', self.config.width1),
                    params.get('width2', self.config.width2)
                )
                result['steps_completed'].append('rgb_calculation')

            # 标签清理
            if params.get('do_label_cleanup', True):
                self.processor.image_processor.delete_out_labels_optimized(
                    image_path, json_path,
                    params.get('not_delete_label_names', [])
                )
                result['steps_completed'].append('label_cleanup')

            result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            logging.error(f"处理文件失败 {image_path}: {e}")

        finally:
            result['processing_time'] = time.time() - start_time

        return result

    def process_files_parallel(self, file_list: List[Tuple[str, str, Dict]]) -> List[Dict[str, Any]]:
        """并行处理文件列表"""
        if not self.config.enable_parallel or len(file_list) <= 1:
            # 串行处理
            return [self.process_single_file(file_info) for file_info in file_list]

        results = []

        try:
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self.process_single_file, file_info): file_info[0]
                    for file_info in file_list
                }

                # 收集结果
                for future in future_to_file:
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        results.append(result)
                    except Exception as e:
                        file_path = future_to_file[future]
                        error_result = {
                            'image_path': file_path,
                            'json_path': '',
                            'success': False,
                            'processing_time': 0.0,
                            'error': f"并行处理异常: {e}",
                            'steps_completed': []
                        }
                        results.append(error_result)
                        logging.error(f"并行处理文件失败 {file_path}: {e}")

        except Exception as e:
            logging.error(f"并行处理管理器异常: {e}")
            # 回退到串行处理
            results = [self.process_single_file(file_info) for file_info in file_list]

        return results

# ==================== 主要处理函数 ====================

@performance_monitor
def zhimai_split_main_pipeline_optimized(input_image_path: str,
                                       input_json_path: str,
                                       json_output_dir: str,
                                       config: ProcessingConfig) -> Dict[str, Any]:
    """
    优化的支脉分离主流程
    """
    result = {
        'input_image': input_image_path,
        'input_json': input_json_path,
        'success': False,
        'processing_time': 0.0,
        'error': None,
        'steps_completed': [],
        'output_files': []
    }

    start_time = time.time()

    try:
        # 创建输出目录
        os.makedirs(json_output_dir, exist_ok=True)

        # 生成输出文件路径 - 修复文件名处理
        image_name = bf.get_file_name(input_image_path)
        base_name = os.path.splitext(image_name)[0]  # 去除扩展名
        output_json_path = bf.pathjoin(json_output_dir, f"{base_name}.json")
        output_image_path = bf.pathjoin(json_output_dir, f"{base_name}.bmp")

        # 创建临时目录（如果需要）
        temp_dir = None
        if config.temp_dir:
            temp_dir = bf.pathjoin(json_output_dir, config.temp_dir)
            os.makedirs(temp_dir, exist_ok=True)

            # 复制文件到临时目录
            temp_json_path = bf.pathjoin(temp_dir, f"{base_name}.json")
            temp_image_path = bf.pathjoin(temp_dir, f"{base_name}.bmp")

            shutil.copy2(input_json_path, temp_json_path)
            shutil.copy2(input_image_path, temp_image_path)

            working_json_path = temp_json_path
            working_image_path = temp_image_path
        else:
            # 直接复制到输出目录
            shutil.copy2(input_json_path, output_json_path)
            shutil.copy2(input_image_path, output_image_path)

            working_json_path = output_json_path
            working_image_path = output_image_path

        result['steps_completed'].append('file_preparation')

        # 创建处理器
        processor = OptimizedZhimaiProcessor(config)

        # 读取JSON数据获取处理参数
        json_data = bf.load_json_dict_orig(working_json_path)

        # 提取主脉数据 - 修复标签名称（与原版本保持一致）
        zhumaizhengti_list = bf.labelme_json_shape_point(json_data, "zhumai_zhengti",
                                                       shape_type_limit="linestrip",
                                                       shapes_name="shapes")

        # 提取支脉数据
        zhimai_list = bf.labelme_json_shape_point(json_data, "zhimai",
                                                shape_type_limit="linestrip",
                                                shapes_name="shapes")

        if not zhumaizhengti_list:
            logging.warning(f"未找到主脉数据: {working_json_path}")

        if not zhimai_list:
            logging.warning(f"未找到支脉数据: {working_json_path}")

        # 支脉分离处理
        if zhumaizhengti_list and zhimai_list:
            zhumaizhengti = zhumaizhengti_list[0]  # 取第一个主脉

            processor.zhimai_separate_optimized(
                "zhimai", zhumaizhengti, zhimai_list,
                config.min_zhimai_points, working_json_path)

            result['steps_completed'].append('zhimai_separation')

        # RGB特征计算
        processor.zhimai_do_one_optimized(
            working_image_path, working_json_path, "shapes",
            config.interval1, config.interval2,
            config.width1, config.width2)

        result['steps_completed'].append('rgb_calculation')

        # 标签清理
        not_delete_label_names = ["canque_fill", "shang_yejian", "xia_yejian",
                                 "four_points", "beiyong6"]

        processor.image_processor.delete_out_labels_optimized(
            working_image_path, working_json_path, not_delete_label_names)

        result['steps_completed'].append('label_cleanup')

        # 移动最终结果
        if temp_dir:
            shutil.move(working_json_path, output_json_path)
            shutil.move(working_image_path, output_image_path)

            # 清理临时目录
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logging.warning(f"清理临时目录失败: {e}")

        result['output_files'] = [output_json_path, output_image_path]
        result['success'] = True

    except Exception as e:
        result['error'] = str(e)
        logging.error(f"支脉分离主流程失败: {e}")

        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except:
                pass

    finally:
        result['processing_time'] = time.time() - start_time

        # 批量处理JSON写入
        if config.enable_batch_json:
            batch_json_processor.flush_writes()

    return result

@performance_monitor
def batch_process_from_vis_optimized(vis_dir: str,
                                   json_output_dir: str,
                                   visualization_dir: str,
                                   config: ProcessingConfig,
                                   max_files: int = 10) -> List[Dict[str, Any]]:
    """
    优化的批量处理函数 - 从vis目录处理
    """
    print("🚀 烟叶支脉分离优化部署程序 - 批量处理")
    print("=" * 60)

    try:
        # 创建输出目录
        os.makedirs(json_output_dir, exist_ok=True)
        os.makedirs(visualization_dir, exist_ok=True)

        # 获取vis目录中的所有文件
        if not os.path.exists(vis_dir):
            raise FileProcessingError(f"vis目录不存在: {vis_dir}")

        # 获取所有图像文件
        image_files = [f for f in os.listdir(vis_dir)
                      if f.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png'))]

        if not image_files:
            raise FileProcessingError(f"在目录 {vis_dir} 中未找到图像文件")

        print(f"📁 找到 {len(image_files)} 个图像文件")
        print(f"⚙️  配置: 并行={config.enable_parallel}, 工作线程={config.max_workers}")
        print(f"💾 内存池限制: {config.memory_limit_mb}MB")

        # 限制处理数量
        files_to_process = image_files[:max_files]

        start_time = time.time()
        results = []

        # 准备文件列表 - 简化路径逻辑
        file_list = []

        for image_file in files_to_process:
            base_name = os.path.splitext(image_file)[0]
            image_path = os.path.join(vis_dir, image_file)

            # JSON文件在同一目录中
            json_path = os.path.join(vis_dir, f"{base_name}.json")

            if os.path.exists(json_path):
                file_list.append((image_path, json_path))
            else:
                logging.warning(f"JSON文件不存在: {json_path}")

        print(f"🔄 开始处理 {len(file_list)} 个文件...")

        # 检查是否有文件需要处理
        if len(file_list) == 0:
            print("⚠️  警告: 没有找到可处理的文件对（图像+JSON）")
            print("请检查:")
            print(f"  - vis目录是否存在: {vis_dir}")
            print(f"  - 图像文件是否有对应的JSON文件")
            return []

        # 处理文件
        for i, (image_path, json_path) in enumerate(file_list, 1):
            print(f"\n[{i}/{len(file_list)}] 处理: {os.path.basename(image_path)}")

            result = zhimai_split_main_pipeline_optimized(
                image_path, json_path, json_output_dir, config)
            results.append(result)

            status = "✅" if result['success'] else "❌"
            print(f"结果: {status} ({result['processing_time']:.2f}s)")

            if not result['success']:
                print(f"错误: {result['error']}")

        # 统计结果
        end_time = time.time()
        total_time = end_time - start_time
        success_count = sum(1 for r in results if r['success'])

        print(f"\n📊 批量处理完成!")
        print(f"总文件数: {len(file_list)}")
        print(f"成功处理: {success_count}")
        print(f"失败数量: {len(file_list) - success_count}")
        print(f"总耗时: {total_time:.2f}秒")

        # 避免除零错误
        if len(file_list) > 0:
            print(f"平均耗时: {total_time/len(file_list):.2f}秒/文件")

        # 性能统计
        if success_count > 0:
            avg_processing_time = sum(r['processing_time'] for r in results if r['success']) / success_count
            print(f"平均处理时间: {avg_processing_time:.2f}秒/文件")

        return results

    except Exception as e:
        logging.error(f"批量处理失败: {e}")
        raise ZhimaiProcessingError(f"批量处理失败: {e}")

# ==================== 后处理函数 ====================

def postprocess(image_path: str, json_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    支脉分离处理的后处理函数

    Args:
        image_path: 图像文件路径
        json_data: 输入的JSON数据

    Returns:
        包含user_feature的JSON数据
    """
    try:
        print(f"🔄 支脉分离处理: {os.path.basename(image_path)}")

        # 创建临时输出目录
        temp_output_dir = "/tmp/zhimai_split_temp"
        os.makedirs(temp_output_dir, exist_ok=True)

        # 保存临时JSON文件
        temp_json_path = os.path.join(temp_output_dir, "temp_input.json")
        with open(temp_json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        # 创建配置
        config = ProcessingConfig()

        # 调用主处理流程
        result = zhimai_split_main_pipeline_optimized(
            image_path, temp_json_path, temp_output_dir, config)

        if result['success']:
            # 读取处理结果
            output_json_path = result['output_json_path']
            with open(output_json_path, 'r', encoding='utf-8') as f:
                result_data = json.load(f)

            # 提取user_feature
            user_feature = result_data.get('user_feature', {})

            print(f"✅ 支脉分离处理完成")

            # 清理临时文件
            import shutil
            shutil.rmtree(temp_output_dir, ignore_errors=True)

            return {
                "user_feature": user_feature
            }
        else:
            print(f"❌ 支脉分离处理失败: {result.get('error', '未知错误')}")
            return {
                "user_feature": {
                    "zhimai_yerou_sub_blue": [0] * 64,
                    "zhimai_yerou_sub_green": [0] * 64,
                    "zhimai_yerou_sub_red": [0] * 64
                }
            }

    except Exception as e:
        print(f"❌ 支脉分离处理失败: {e}")
        traceback.print_exc()
        return {
            "user_feature": {
                "zhimai_yerou_sub_blue": [0] * 64,
                "zhimai_yerou_sub_green": [0] * 64,
                "zhimai_yerou_sub_red": [0] * 64
            }
        }


# ==================== 主函数 ====================

def test_postprocess():
    """测试postprocess函数"""
    print("🧪 测试支脉分离postprocess函数")
    print("=" * 60)

    # 测试路径
    test_image_dir = "/home/<USER>/xm/code/coderafactor/test_data/test_inputs/"
    test_json_dir = "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/output_optimized/"

    # 获取测试文件
    if not os.path.exists(test_image_dir):
        print(f"❌ 测试图像目录不存在: {test_image_dir}")
        return False

    if not os.path.exists(test_json_dir):
        print(f"❌ 测试JSON目录不存在: {test_json_dir}")
        return False

    # 获取图像文件
    image_files = [f for f in os.listdir(test_image_dir)
                   if f.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png'))]

    if not image_files:
        print(f"❌ 测试图像目录中没有图像文件: {test_image_dir}")
        return False

    print(f"📁 找到 {len(image_files)} 个测试图像")

    success_count = 0

    for image_file in image_files[:3]:  # 测试前3个文件
        try:
            # 构建文件路径
            image_path = os.path.join(test_image_dir, image_file)
            base_name = os.path.splitext(image_file)[0]
            json_file = f"{base_name}.json"
            json_path = os.path.join(test_json_dir, json_file)

            if not os.path.exists(json_path):
                print(f"⚠️  跳过 {image_file}: 对应JSON文件不存在")
                continue

            print(f"\n🔄 测试: {image_file}")

            # 读取输入JSON
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 调用postprocess函数
            result = postprocess(image_path, json_data)

            # 验证结果
            if 'user_feature' in result:
                user_feature = result['user_feature']
                expected_keys = ['zhimai_yerou_sub_blue', 'zhimai_yerou_sub_green', 'zhimai_yerou_sub_red']

                if all(key in user_feature for key in expected_keys):
                    print(f"✅ {image_file}: postprocess成功")
                    print(f"   特征数量: {len(user_feature)}")
                    for key in expected_keys:
                        print(f"   {key}: {len(user_feature[key])} 个值")
                    success_count += 1
                else:
                    print(f"❌ {image_file}: 缺少必要的特征键")
            else:
                print(f"❌ {image_file}: 结果中没有user_feature")

        except Exception as e:
            print(f"❌ {image_file}: 处理失败 - {e}")

    print(f"\n📊 测试结果: {success_count}/{len(image_files[:3])} 成功")
    return success_count > 0


def main():
    """
    优化版本的主入口函数
    """
    import argparse

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('zhimai_processing.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='烟叶支脉分离优化部署程序')
    parser.add_argument('--test-postprocess', action='store_true',
                       help='测试postprocess函数')
    parser.add_argument('--vis-dir', '-v', default='test_data/vis',
                       help='vis目录路径（包含合并后的JSON和图像）')
    parser.add_argument('--output-dir', '-o', default='seg_zhimai_split/test_output_optimized',
                       help='输出目录路径')
    parser.add_argument('--visualization-dir', '-z', default='seg_zhimai_split/visualization_optimized',
                       help='可视化输出目录路径')
    parser.add_argument('--max-files', '-m', type=int, default=5,
                       help='最大处理文件数量')
    parser.add_argument('--config', '-c', default='zhimai_config.json',
                       help='配置文件路径')
    parser.add_argument('--profile', '-p', default='default',
                       help='配置文件中的配置名称')

    args = parser.parse_args()

    # 如果是测试postprocess函数
    if args.test_postprocess:
        return 0 if test_postprocess() else 1

    print("🚀 烟叶支脉分离优化部署程序")
    print("=" * 60)
    print("优化特性:")
    print("  ✅ 算法复杂度优化 (O(n×m×k) → O(n log n))")
    print("  ✅ 内存池管理")
    print("  ✅ 批量JSON处理")
    print("  ✅ 并行计算支持")
    print("  ✅ 相交计算缓存")
    print("  ✅ 完善错误处理")
    print("=" * 60)

    # 创建配置
    config = ProcessingConfig(
        intersection_threshold=2.0,
        min_zhimai_points=10,
        enable_parallel=True,
        max_workers=4,
        memory_limit_mb=512,
        enable_cache=True,
        intersection_cache_size=1000,
        enable_batch_json=True,
        batch_size=10
    )

    print(f"📁 输入数据目录: {args.vis_dir}")
    print(f"📁 JSON输出目录: {args.output_dir}")
    print(f"📁 可视化输出目录: {args.visualization_dir}")
    print(f"🔧 并行处理: {'启用' if config.enable_parallel else '禁用'}")
    print(f"🧵 工作线程: {config.max_workers}")
    print(f"💾 内存限制: {config.memory_limit_mb}MB")

    try:
        # 创建输出目录
        os.makedirs(args.output_dir, exist_ok=True)
        os.makedirs(args.visualization_dir, exist_ok=True)

        # 批量处理 - 使用vis目录作为输入
        results = batch_process_from_vis_optimized(
            args.vis_dir, args.output_dir, args.visualization_dir, config, args.max_files
        )

        success_count = sum(1 for r in results if r['success'])
        print(f"\n📈 批量处理结果: {success_count}/{len(results)} 成功")

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logging.error(f"主程序执行失败: {e}")
        return 1

    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        memory_pool.clear()
        intersection_cache.clear()
        gc.collect()

    print("\n✅ 处理完成!")
    return 0

if __name__ == "__main__":
    exit_code = main()
