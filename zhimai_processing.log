2025-07-23 14:59:15,582 - WARNING - JSON文件不存在: yanye_user_feature/vis\hn-cz-2023-C2F-C21-3.json
2025-07-23 14:59:15,583 - ERROR - 批量处理失败: float division by zero
2025-07-23 14:59:15,584 - ERROR - 函数 batch_process_from_vis_optimized 执行失败: 批量处理失败: float division by zero
2025-07-23 14:59:15,584 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 0.022s, 内存变化: 0.03MB
2025-07-23 14:59:15,584 - ERROR - 主程序执行失败: 批量处理失败: float division by zero
2025-07-23 15:03:17,651 - WARNING - JSON文件不存在: yanye_user_feature/vis\hn-cz-2023-C2F-C21-3.json
2025-07-23 15:03:17,652 - ERROR - 批量处理失败: float division by zero
2025-07-23 15:03:17,652 - ERROR - 函数 batch_process_from_vis_optimized 执行失败: 批量处理失败: float division by zero
2025-07-23 15:03:17,652 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 0.014s, 内存变化: 0.02MB
2025-07-23 15:03:17,652 - ERROR - 主程序执行失败: 批量处理失败: float division by zero
2025-07-23 15:07:18,474 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output/temp/hn-cz-2023-C2F-C21-3.bmp.json
2025-07-23 15:07:18,550 - INFO - 函数 create_zhimai_masks 执行时间: 0.010s, 内存变化: 33.10MB
2025-07-23 15:07:18,627 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.076s, 内存变化: 0.12MB
2025-07-23 15:07:18,683 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.208s, 内存变化: 12.64MB
2025-07-23 15:07:18,921 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.522s, 内存变化: 48.69MB
2025-07-23 15:07:18,922 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 0.532s, 内存变化: 48.69MB
2025-07-23 15:31:42,717 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-3.json
2025-07-23 15:31:42,781 - INFO - 函数 create_zhimai_masks 执行时间: 0.007s, 内存变化: 33.12MB
2025-07-23 15:31:42,999 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.218s, 内存变化: 0.12MB
2025-07-23 15:31:43,095 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.377s, 内存变化: 12.70MB
2025-07-23 15:31:43,403 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.772s, 内存变化: 48.56MB
2025-07-23 15:31:43,405 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 0.783s, 内存变化: 48.57MB
2025-07-28 01:01:10,804 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-3.json
2025-07-28 01:01:10,863 - INFO - 函数 create_zhimai_masks 执行时间: 0.016s, 内存变化: 33.24MB
2025-07-28 01:01:10,967 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.104s, 内存变化: 11.23MB
2025-07-28 01:01:10,994 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.189s, 内存变化: 25.78MB
2025-07-28 01:01:11,233 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.459s, 内存变化: 52.12MB
2025-07-28 01:01:11,234 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 0.467s, 内存变化: 52.12MB
2025-07-28 01:28:53,466 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-6.json
2025-07-28 01:28:53,524 - INFO - 函数 create_zhimai_masks 执行时间: 0.017s, 内存变化: 33.29MB
2025-07-28 01:28:53,576 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.051s, 内存变化: 11.37MB
2025-07-28 01:28:53,595 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.128s, 内存变化: 24.86MB
2025-07-28 01:28:53,790 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.349s, 内存变化: 61.09MB
2025-07-28 01:28:53,813 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-4.json
2025-07-28 01:28:53,867 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.82MB
2025-07-28 01:28:53,914 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.047s, 内存变化: 11.08MB
2025-07-28 01:28:53,930 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.116s, 内存变化: 0.26MB
2025-07-28 01:28:54,091 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.300s, 内存变化: -2.54MB
2025-07-28 01:28:54,121 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-3.json
2025-07-28 01:28:54,177 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.83MB
2025-07-28 01:28:54,225 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.048s, 内存变化: 11.08MB
2025-07-28 01:28:54,251 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.130s, 内存变化: 0.54MB
2025-07-28 01:28:54,458 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.367s, 内存变化: -0.30MB
2025-07-28 01:28:54,485 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-5.json
2025-07-28 01:28:54,538 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:28:54,586 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.048s, 内存变化: 11.08MB
2025-07-28 01:28:54,606 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.121s, 内存变化: 0.05MB
2025-07-28 01:28:54,777 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.319s, 内存变化: -2.20MB
2025-07-28 01:28:54,803 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-7.json
2025-07-28 01:28:54,857 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:28:54,905 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.048s, 内存变化: 11.08MB
2025-07-28 01:28:54,929 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.126s, 内存变化: 0.27MB
2025-07-28 01:28:55,110 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.332s, 内存变化: -0.09MB
2025-07-28 01:28:55,110 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 1.677s, 内存变化: 55.95MB
2025-07-28 01:48:02,681 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-6.json
2025-07-28 01:48:02,741 - INFO - 函数 create_zhimai_masks 执行时间: 0.017s, 内存变化: 33.27MB
2025-07-28 01:48:02,794 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.053s, 内存变化: 11.19MB
2025-07-28 01:48:02,814 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.132s, 内存变化: 24.80MB
2025-07-28 01:48:03,015 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.361s, 内存变化: 60.95MB
2025-07-28 01:48:03,040 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-4.json
2025-07-28 01:48:03,096 - INFO - 函数 create_zhimai_masks 执行时间: 0.014s, 内存变化: 21.57MB
2025-07-28 01:48:03,145 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.049s, 内存变化: 11.08MB
2025-07-28 01:48:03,160 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.120s, 内存变化: 0.27MB
2025-07-28 01:48:03,336 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.320s, 内存变化: -2.05MB
2025-07-28 01:48:03,363 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-3.json
2025-07-28 01:48:03,419 - INFO - 函数 create_zhimai_masks 执行时间: 0.014s, 内存变化: 21.83MB
2025-07-28 01:48:03,469 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.049s, 内存变化: 11.08MB
2025-07-28 01:48:03,495 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.132s, 内存变化: 0.53MB
2025-07-28 01:48:03,714 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.377s, 内存变化: -0.79MB
2025-07-28 01:48:03,740 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-5.json
2025-07-28 01:48:03,795 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:48:03,844 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.048s, 内存变化: 11.08MB
2025-07-28 01:48:03,864 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.124s, 内存变化: 0.06MB
2025-07-28 01:48:04,051 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.337s, 内存变化: -1.28MB
2025-07-28 01:48:04,076 - WARNING - 未找到主脉数据: seg_zhimai_split/test_output_optimized/temp/hn-cz-2023-C2F-C21-7.json
2025-07-28 01:48:04,131 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:48:04,180 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.049s, 内存变化: 11.08MB
2025-07-28 01:48:04,204 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.128s, 内存变化: 0.00MB
2025-07-28 01:48:04,396 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.345s, 内存变化: -0.41MB
2025-07-28 01:48:04,396 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 1.749s, 内存变化: 56.42MB
2025-07-28 01:54:49,884 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.072s, 内存变化: 4.45MB
2025-07-28 01:54:49,942 - INFO - 函数 create_zhimai_masks 执行时间: 0.016s, 内存变化: 33.30MB
2025-07-28 01:54:49,993 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.050s, 内存变化: 10.69MB
2025-07-28 01:54:50,012 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.127s, 内存变化: 24.02MB
2025-07-28 01:54:50,215 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.431s, 内存变化: 64.77MB
2025-07-28 01:54:50,215 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 0.439s, 内存变化: 64.77MB
2025-07-28 01:56:54,150 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.072s, 内存变化: 4.43MB
2025-07-28 01:56:54,208 - INFO - 函数 create_zhimai_masks 执行时间: 0.016s, 内存变化: 33.30MB
2025-07-28 01:56:54,260 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.052s, 内存变化: 10.73MB
2025-07-28 01:56:54,279 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.130s, 内存变化: 23.92MB
2025-07-28 01:56:54,486 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.435s, 内存变化: 64.52MB
2025-07-28 01:56:54,554 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.044s, 内存变化: 0.00MB
2025-07-28 01:56:54,606 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:56:54,657 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.050s, 内存变化: 11.08MB
2025-07-28 01:56:54,672 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.118s, 内存变化: 0.00MB
2025-07-28 01:56:54,853 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.367s, 内存变化: -2.80MB
2025-07-28 01:56:54,945 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.063s, 内存变化: 0.77MB
2025-07-28 01:56:55,000 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:56:55,050 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.049s, 内存变化: 11.08MB
2025-07-28 01:56:55,075 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.130s, 内存变化: 0.02MB
2025-07-28 01:56:55,292 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.439s, 内存变化: -0.83MB
2025-07-28 01:56:55,366 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.047s, 内存变化: 0.00MB
2025-07-28 01:56:55,420 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:56:55,468 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.048s, 内存变化: 11.08MB
2025-07-28 01:56:55,488 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.122s, 内存变化: 0.00MB
2025-07-28 01:56:55,678 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.385s, 内存变化: -0.13MB
2025-07-28 01:56:55,758 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.052s, 内存变化: 0.00MB
2025-07-28 01:56:55,810 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.57MB
2025-07-28 01:56:55,861 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.051s, 内存变化: 11.10MB
2025-07-28 01:56:55,885 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.127s, 内存变化: 0.00MB
2025-07-28 01:56:56,083 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.405s, 内存变化: -0.62MB
2025-07-28 01:56:56,083 - INFO - 函数 batch_process_from_vis_optimized 执行时间: 2.041s, 内存变化: 60.14MB
2025-07-30 14:17:35,956 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.069s, 内存变化: 5.11MB
2025-07-30 14:17:36,014 - INFO - 函数 create_zhimai_masks 执行时间: 0.016s, 内存变化: 33.29MB
2025-07-30 14:17:36,068 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.053s, 内存变化: 9.95MB
2025-07-30 14:17:36,084 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.128s, 内存变化: 22.83MB
2025-07-30 14:17:36,276 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.424s, 内存变化: 64.00MB
2025-07-30 14:17:36,368 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.047s, 内存变化: 0.25MB
2025-07-30 14:17:36,424 - INFO - 函数 create_zhimai_masks 执行时间: 0.013s, 内存变化: 21.63MB
2025-07-30 14:17:36,473 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.048s, 内存变化: 11.01MB
2025-07-30 14:17:36,491 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.123s, 内存变化: 0.27MB
2025-07-30 14:17:36,677 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.378s, 内存变化: -1.80MB
2025-07-30 14:17:36,802 - INFO - 函数 zhimai_separate_optimized 执行时间: 0.062s, 内存变化: 1.29MB
2025-07-30 14:17:36,861 - INFO - 函数 create_zhimai_masks 执行时间: 0.014s, 内存变化: 21.64MB
2025-07-30 14:17:36,909 - INFO - 函数 calculate_rgb_histograms 执行时间: 0.048s, 内存变化: 11.01MB
2025-07-30 14:17:36,934 - INFO - 函数 zhimai_do_one_optimized 执行时间: 0.132s, 内存变化: 0.11MB
2025-07-30 14:17:37,136 - INFO - 函数 zhimai_split_main_pipeline_optimized 执行时间: 0.426s, 内存变化: -0.59MB
