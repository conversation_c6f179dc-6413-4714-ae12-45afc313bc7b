#把其他目录中json中推理标记量化成user_feature
import sys
# sys.path.append("/home/<USER>/jiang/Tensorflow/datadetect/test9_common")
# sys.path.append("D:\\Tensorflow\\datadetect\\test9_common")
import os
import time

# 添加必要的路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

def zhimai_proc(src_json, shapes_version=None, remain_max_length_amt=5):  # 统计支脉的长度
    # shapes_20210325152002_zhimaimerge 计算支脉的各个长度和总长度
    print('\n\n\n src_json={}'.format(src_json))
    if shapes_version is None:
        key_name_zhimai = bf.labelme_json_search_shapename("zhimai", src_json)
    else:
        key_name_zhimai = shapes_version
    point_list_list = bf.labelme_json_shape_read_point(src_json, label_name="zhimai",shape_type_limit="linestrip", shapes_name=key_name_zhimai)
    # sum_length = 0
    zhimai_length_list = []

    # g过滤掉只有一个点的
    pop_idx_list = []
    for idx, point_list in enumerate(point_list_list):
        if len(point_list) < 2:
            pop_idx_list.append(idx)
    bf.pop_list_ele_by_list(point_list_list, pop_idx_list)
    #
    for idx, point_list in enumerate(point_list_list):
        if len(point_list) > 1:
            summ = 0
            for i in range(1, len(point_list)):
                pnt1 = point_list[i - 1]
                pnt2 = point_list[i]
                summ += bf.euclidean_dist(pnt1, pnt2)
            zhimai_length_list.append(summ)
        else:
            zhimai_length_list.append(0)
    sum_length = bf.list_sum(zhimai_length_list)

    # 只保留最长的5条线
    # print('zhimai_length_list={}'.format(zhimai_length_list))
    zhimai_length_idx_list = bf.list_sort_idx_list(zhimai_length_list, ascending=False)
    # print('zhimai_length_idx_list={}'.format(zhimai_length_idx_list))
    if len(zhimai_length_idx_list) > remain_max_length_amt:
        zhimai_length_idx_list = zhimai_length_idx_list[:remain_max_length_amt + 1]

    # 支脉与主脉整体趋势的夹角
    ##过滤出足够长的支脉
    length_zhimai = 0.01
    try_amt = 10
    zhimai_zhumai_angle_list = []
    zhimai_fit_line = []
    zhimai_extend_line = []
    zhumai_cross_line = []
    for try_i in range(try_amt):
        # print('zhimai_length_list={}'.format(zhimai_length_list))
        idx_list = bf.list_where_idx(zhimai_length_list, wheretype="great", val=length_zhimai)
        # key_name = bf.labelme_json_search_shapename("zhumai", src_json)
        key_name = "shapes"
        zhengti_point_list_list = bf.labelme_json_shape_read_point(src_json, label_name="zhumai_zhengti",
                                                                   shapes_name=key_name)
        if len(zhengti_point_list_list) == 0:
            continue
        zhengti_point_list = zhengti_point_list_list[0]
        ##计算支脉斜率

        # print('idx_list={}'.format(idx_list))
        for idx in idx_list:
            # print('point_list_list={}'.format(point_list_list))
            zhimai_point_list = point_list_list[idx]

            # 拟合出支脉直线
            # print('debug001')
            # print('zhimai_point_list={}'.format(zhimai_point_list))
            k, b = bf.kx_b_fit(zhimai_point_list)
            # bf.sleep(1)
            # print('debug002')
            # zhimai起始结束点
            x_left = zhimai_point_list[0][0]
            x_right = zhimai_point_list[-1][0]
            y_fit_left = bf.get_line_point(k, b, x_left)
            y_fit_right = bf.get_line_point(k, b, x_right)

            # 计算出支脉直线衍生到屏幕左右两端的点
            x1 = 0
            x2 = 1
            y1 = bf.get_k_b_line_point(k, b, x1)
            y2 = bf.get_k_b_line_point(k, b, x2)
            # 计算支脉直线与主脉曲线之间的焦点
            pnt_cross_list, angle_cross_list, idx_cross_list = bf.find_linestriplineIntersection(zhengti_point_list, x1,
                                                                                                 y1, x2, y2)
            if len(pnt_cross_list) and idx in zhimai_length_idx_list:  # 只保留最长的五根支脉的结果
                # print('idx={}'.format(idx))
                _, min_dist_idx = bf.find_closest_point(pnt_cross_list, zhimai_point_list[0])
                zhimai_zhumai_angle = abs(angle_cross_list[min_dist_idx])  # 支脉与主脉的夹角
                zhimai_zhumai_angle_list.append(zhimai_zhumai_angle)
                zhimai_fit_line.append([[x_left, y_fit_left], [x_right, y_fit_right]])
                # 到交点的延长线
                idx_cross = idx_cross_list[min_dist_idx]
                zhimai_extend_line.append([[x_left, y_fit_left], pnt_cross_list[min_dist_idx]])
                # 交点的线段
                zhumai_cross_line.append([zhengti_point_list[idx_cross], zhengti_point_list[idx_cross + 1]])
        if len(zhimai_zhumai_angle_list):
            break
        else:
            length_zhimai *= 0.5
            print('-----------------------retry {} length_zhimai={}'.format(try_i, length_zhimai))

    if len(zhimai_zhumai_angle_list):
        zhimai_zhumai_angle_avg = bf.avg_list(zhimai_zhumai_angle_list)
    else:
        zhimai_zhumai_angle_avg = 0
    print('sum_length={}'.format(sum_length))
    print('zhimai_zhumai_angle_avg={}'.format(zhimai_zhumai_angle_avg))

    shape_list_zhimai_fit = bf.labelme_create_line_shapes_list("zhimai_fit_line", zhimai_fit_line)
    shape_list_zhimai_extend = bf.labelme_create_line_shapes_list("zhimai_extend_line", zhimai_extend_line)
    shape_list_zhumai_cross = bf.labelme_create_line_shapes_list("zhumai_cross_line", zhumai_cross_line)
    shape_list = shape_list_zhimai_fit + shape_list_zhimai_extend + shape_list_zhumai_cross

    # bf.labelme_json_add_shape_file(src_json, "zhimaiangle", shape_list)
    bf.labelme_json_append_labels_by_shapename_withlock(src_json, "zhimaiangle", shape_list, ["zhimai_fit_line", "zhimai_extend_line", "zhumai_cross_line"])
    # bf.labelme_json_append_shape_file(src_json,key_name_zhimai,shape_list)

    return {"zhimai_length": float(sum_length), "zhimai_zhumai_angle_avg": float(zhimai_zhumai_angle_avg)}


def zhumaidakai_proc(src_json, shapes_version=None):  # 统计主脉打开的位置
    # shapes_20210325152002_zhimaimerge
    if shapes_version is None:
        key_name = bf.labelme_json_search_shapename("zhumai", src_json)
    else:
        key_name = shapes_version
    # 寻找主脉打开坐标的最左右点
    point_list_list = bf.labelme_json_shape_read_point(src_json, label_name="zhumaidakai", shapes_name=key_name)
    point_list_list_zt = bf.labelme_json_shape_read_point(src_json, label_name="zhumai_zhengti", shapes_name=key_name)
    point_list_list = bf.pnt_list_list_rescale(point_list_list)
    point_list_list_zt = bf.pnt_list_list_rescale(point_list_list_zt)

    flag_found_dakai = True
    dakai_right = 0
    dakai_left = 100000
    zhumaidakai_len = 0
    zhumai_kuan_list = []
    if len(point_list_list) == 0:
        flag_found_dakai = False
    else:
        for point_list in point_list_list:
            if len(point_list) > 1:
                point_list_zt = point_list_list_zt[0] #如果有主脉打开就假设有主脉整体存在
                #处理1：找出最左侧的点和最右侧的点，然后计算打开长度
                flrp = bf.find_left_right_point()
                for i in range(len(point_list)):
                    pnt = point_list[i]
                    flrp.add_point(pnt,i)
                    if pnt[0] > dakai_right:
                        dakai_right = pnt[0]
                    if pnt[0] < dakai_left:
                        dakai_left = pnt[0]
                zhumaidakai_len += abs(flrp.right_point[0] - flrp.left_point[0])
                #处理2：为计算主脉打开区域的最大宽度，平均宽度、中位宽度做准备
                if flrp.left_point_idx<flrp.right_point_idx:
                    start_idx = flrp.left_point_idx
                    end_idx = flrp.right_point_idx
                else:
                    start_idx = flrp.right_point_idx
                    end_idx = flrp.left_point_idx

                for i in range(start_idx,end_idx+1):
                    point_on_dakai = point_list[i]
                    dist, pnt_cross = bf.point_linestrip_distance(point_on_dakai,point_list_zt)
                    #print('dist={}'.format(dist))
                    #print('point_on_dakai={}'.format(point_on_dakai))
                    #print('pnt_cross={}'.format(pnt_cross))
                    pnt_cross_list = bf.find_polygonlineIntersection(point_list,point_on_dakai[0],point_on_dakai[1],pnt_cross[0],pnt_cross[1],flag_on_line=True)
                    #print('pnt_cross_list={}'.format(pnt_cross_list))
                    dist_tmp = bf.point_list_max_dist(pnt_cross_list)
                    zhumai_kuan_list.append(dist_tmp)

    if len(zhumai_kuan_list)>0:
        print('zhumai_kuan_list={}'.format(zhumai_kuan_list))
        zhumaidakai_kuan_max = bf.get_Max(zhumai_kuan_list)
        zhumaidakai_kuan_avg = bf.avg_list(zhumai_kuan_list)
        zhumaidakai_kuan_mid = bf.mid_list(zhumai_kuan_list)
        zhumaidakai_kuan_amt = len(zhumai_kuan_list)
    else:
        zhumaidakai_kuan_max = 0
        zhumaidakai_kuan_avg = 0
        zhumaidakai_kuan_mid = 0
        zhumaidakai_kuan_amt = 0
    # 寻找主脉整体走势打开坐标的最左右点
    point_list_list = bf.labelme_json_shape_read_point(src_json, label_name="zhumai_zhengti", shapes_name=key_name)
    point_list_list = bf.pnt_list_list_rescale(point_list_list)
    zhengti_right = 0
    zhengti_left = 1000000
    for point_list in point_list_list:
        if len(point_list) > 1:
            for i in range(len(point_list)):
                pnt = point_list[i]
                if pnt[0] > zhengti_right:
                    zhengti_right = pnt[0]
                if pnt[0] < zhengti_left:
                    zhengti_left = pnt[0]

    if flag_found_dakai:
        zhumaidakai_position = (dakai_right - zhengti_left) / (zhengti_right - zhengti_left)
        zhumaidakai_rate = (dakai_right - dakai_left) / (zhengti_right - zhengti_left)
    else:
        zhumaidakai_position = 0
        zhumaidakai_rate = 0
    print('zhumaidakai_position={}'.format(zhumaidakai_position))
    print('zhumaidakai_rate={}'.format(zhumaidakai_rate))
    print('zhumaidakai_len={}'.format(zhumaidakai_len))
    print('zhumaidakai_kuan_max={}'.format(zhumaidakai_kuan_max))
    print('zhumaidakai_kuan_avg={}'.format(zhumaidakai_kuan_avg))
    print('zhumaidakai_kuan_mid={}'.format(zhumaidakai_kuan_mid))
    print('zhumaidakai_kuan_amt={}'.format(zhumaidakai_kuan_amt))

    return {"zhumaidakai_position": zhumaidakai_position,
            "zhumaidakai_rate": zhumaidakai_rate,
            "zhumaidakai_len":zhumaidakai_len,
            "zhumaidakai_kuan_max":zhumaidakai_kuan_max,
            "zhumaidakai_kuan_avg":zhumaidakai_kuan_avg,
            "zhumaidakai_kuan_mid":zhumaidakai_kuan_mid,
            "zhumaidakai_kuan_amt":zhumaidakai_kuan_amt
            }


def do_one(src_json, dst_json):
    add_dict = {}
    ret_dict1 = zhimai_proc(src_json)
    ret_dict2 = zhumaidakai_proc(src_json)
    #
    add_dict = bf.dict_merge(add_dict, ret_dict1)
    add_dict = bf.dict_merge(add_dict, ret_dict2)
    bf.labelme_json_add_userfeature_file(dst_json, add_dict)


def do_one_5(src_json, json_write_dir):
    add_dict = {}
    ret_dict1 = zhimai_proc(src_json, "shapes")
    ret_dict2 = zhumaidakai_proc(src_json, "shapes")
    #
    add_dict = bf.dict_merge(add_dict, ret_dict1)
    add_dict = bf.dict_merge(add_dict, ret_dict2)
    dst_json = bf.pathjoin(json_write_dir, bf.get_file_name(src_json))

    # 先复制源文件到目标位置
    import shutil
    if src_json != dst_json:
        shutil.copy2(src_json, dst_json)

    bf.labelme_json_add_userfeature_file(dst_json, add_dict)


def do_one_5_pre(i,src_json_name_list,dst_json_name_list,src_json_list,dst_path):
    if src_json_name_list[i] in dst_json_name_list:
        j = bf.list_index(dst_json_name_list, src_json_name_list[i])
        do_one_5(src_json_list[i], dst_path)


def do_dir(src_path, dst_path):
    """
    目录批量处理 (完全按照原始实现)
    Args:
        src_path: 源目录路径
        dst_path: 目标目录路径
    """
    try:
        # 创建输出目录
        os.makedirs(dst_path, exist_ok=True)

        # 扫描源目录中的JSON文件
        src_json_list, src_json_name_list = bf.scan_labelme_jsonfile_list(src_path)

        # 扫描目标目录中的JSON文件
        dst_json_list, dst_json_name_list = bf.scan_labelme_jsonfile_list(dst_path)

        print(f"Found {len(src_json_list)} source JSON files")
        print(f"Found {len(dst_json_list)} destination JSON files")

        # 处理每个源JSON文件
        for i in range(len(src_json_list)):
            try:
                do_one_5(src_json_list[i], dst_path)
            except Exception as e:
                print(f"Error processing file {i}: {e}")

        print(f"Batch processing completed: {len(src_json_list)} files processed")

    except Exception as e:
        print(f"Error in batch processing: {e}")


def main():
    """主入口函数"""
    print("烟叶主要特征分析模块 - 部署程序")
    print("=" * 60)

    # 配置路径
    src_path = "dealing_images_juchi/output"
    dst_path = "import_yanye_user_feature/output"

    print(f"源目录: {src_path}")
    print(f"目标目录: {dst_path}")

    # 检查源目录是否存在
    if not os.path.exists(src_path):
        print(f"错误: 源目录不存在 {src_path}")
        return

    # 批量处理
    start_time = time.time()
    do_dir(src_path, dst_path)
    end_time = time.time()

    print(f"\n处理完成! 总耗时: {end_time - start_time:.2f}秒")


if __name__ == "__main__":
    main()
