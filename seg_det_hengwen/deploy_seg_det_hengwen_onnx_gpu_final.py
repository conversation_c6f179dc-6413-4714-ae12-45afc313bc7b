#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶横纹特征检测GPU流水线并行版本
基于焦点检测的成功流水线架构，专门适配横纹检测

作者: 系统架构师
日期: 2025-01-27
版本: 2.0.0

关键特性：
1. GPU流水线并行架构
2. 高性能内存池管理
3. 异步队列系统
4. 与现有程序完全一致的检测结果
5. 横纹检测优化（100x100窗口）
"""

import os
import sys
import time
import threading
import cv2
import numpy as np
import json
import queue
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from PIL import Image

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

try:
    import base_function as bf
    import onnxruntime as ort
    print("🔥🔥🔥 GPU流水线并行版本 - 成功导入所有必要模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入必要模块 {e}")
    sys.exit(1)

# 横纹检测配置
jiaodian_judge_json_path = os.path.join(current_dir, "jiaodian_judge.json")

def judge_jiaodian(img, cx, cy, w, h, cfg_dict):
    """焦点判断函数 - 与原程序保持一致"""
    return True

def img_to_array_compatible(img, data_format='channels_last', dtype='float32'):
    """
    与原版本完全一致的PIL图像到numpy数组转换函数
    确保转换结果完全一致，避免检测目标数量差异
    """
    if data_format not in {'channels_first', 'channels_last'}:
        raise ValueError('Unknown data_format: %s' % data_format)

    # 关键：使用np.asarray而不是np.array，与原版本保持一致
    x = np.asarray(img, dtype=dtype)

    if len(x.shape) == 3:
        if data_format == 'channels_first':
            x = x.transpose(2, 0, 1)
    elif len(x.shape) == 2:
        if data_format == 'channels_first':
            x = x.reshape((1, x.shape[0], x.shape[1]))
        else:
            x = x.reshape((x.shape[0], x.shape[1], 1))
    else:
        raise ValueError('Unsupported image shape: %s' % (x.shape,))

    return x


@dataclass
class PipelineData:
    """流水线数据结构"""
    image_path: str
    image_data: Optional[np.ndarray] = None
    processed_patches: Optional[np.ndarray] = None
    coords: Optional[List] = None
    inference_result: Optional[np.ndarray] = None
    processed_result: Optional[Dict] = None
    timestamp: float = 0.0
    stage: str = "init"  # init, preprocessed, inferred, postprocessed
    width: int = 0
    height: int = 0


class GPUMemoryPool:
    """GPU内存池管理器 - 减少动态内存分配开销"""
    
    def __init__(self, pool_size: int = 16, patch_size: Tuple[int, int] = (100, 100)):
        self.pool_size = pool_size
        self.patch_height, self.patch_width = patch_size
        self.buffers = queue.Queue()
        self.used_buffers = set()
        self._lock = threading.Lock()
        
        # 预分配GPU内存缓冲区
        print(f"🔧 初始化GPU内存池 - 池大小: {pool_size}, 补丁尺寸: {patch_size}")
        
        # 预分配内存缓冲区 (batch_size, height, width, channels)
        for _ in range(pool_size):
            buffer = np.zeros((1024, self.patch_height, self.patch_width, 3), dtype=np.float32)
            self.buffers.put(buffer)
    
    def get_buffer(self, batch_size: int = 1024) -> Optional[np.ndarray]:
        """获取GPU内存缓冲区"""
        try:
            buffer = self.buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            if batch_size <= buffer.shape[0]:
                return buffer[:batch_size]
            else:
                return np.zeros((batch_size, self.patch_height, self.patch_width, 3), dtype=np.float32)
        except queue.Empty:
            print("⚠️  GPU内存池已满，创建临时缓冲区")
            return np.zeros((batch_size, self.patch_height, self.patch_width, 3), dtype=np.float32)
    
    def return_buffer(self, buffer: np.ndarray):
        """归还GPU内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.buffers.put_nowait(buffer)
                    except queue.Full:
                        print("⚠️  GPU内存池队列已满")
    
    def get_pool_stats(self) -> Dict[str, int]:
        """获取内存池统计信息"""
        with self._lock:
            return {
                'available': self.buffers.qsize(),
                'used': len(self.used_buffers),
                'total': self.pool_size
            }


class AsyncQueue:
    """异步队列 - 支持流水线各阶段的数据传递"""
    
    def __init__(self, maxsize: int = 16):
        self.queue = queue.Queue(maxsize=maxsize)
        self.maxsize = maxsize
        
    def put(self, item, timeout: float = 1.0) -> bool:
        """放入数据项"""
        try:
            self.queue.put(item, timeout=timeout)
            return True
        except queue.Full:
            return False
    
    def get(self, timeout: float = 1.0) -> Optional[Any]:
        """获取数据项"""
        try:
            return self.queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()


class ZeroIOImageSplitter:
    """零重复I/O图像分割器 - 消除重复图像读取"""

    def __init__(self):
        print("🚀 初始化零重复I/O图像分割器")

    def split_image_in_memory(self, pil_img: Image.Image, h_pixel: int = 100, w_pixel: int = 100) -> Tuple[List[Image.Image], List[Tuple[int, int]]]:
        """
        内存中直接分割图像 - 与原版本完全一致的算法
        针对横纹检测的100x100窗口大小
        """
        width, height = pil_img.size

        # 横纹检测的特殊处理：如果图像小于窗口大小，直接返回原图
        if width <= w_pixel and height <= h_pixel:
            return [pil_img], [(0, 0)]

        # 使用与焦点检测相同的内存优化分割算法
        h_amt = int(height / h_pixel)
        w_amt = int(width / w_pixel)

        small_pil_list = []
        small_cord_list = []

        # 与原版本一致的参数
        w_pixel_half = int(w_pixel / 2)
        h_pixel_half = int(h_pixel / 2)

        # 关键优化：缓存PIL→CV2转换，避免重复转换
        pil_img_cv2 = bf.pil_to_cv2(pil_img)  # 只转换一次

        for h_idx in range(h_amt):
            top = h_idx * h_pixel
            for w_idx in range(w_amt):
                left = w_idx * w_pixel

                # 与原版本完全一致的窗口位置计算
                left_top_list = [(left, top)]
                if left + w_pixel_half + w_pixel < width:
                    left_top_list.append((left + w_pixel_half, top))
                if top + h_pixel_half + h_pixel < height:
                    left_top_list.append((left, top + h_pixel_half))
                if left + w_pixel_half + w_pixel < width and top + h_pixel_half + h_pixel < height:
                    left_top_list.append((left + w_pixel_half, top + h_pixel_half))

                for left_tmp, top_tmp in left_top_list:
                    # 关键优化：直接从CV2数组切片，避免重复的PIL裁剪
                    if top_tmp + h_pixel <= height and left_tmp + w_pixel <= width:
                        # 从缓存的CV2图像中切片
                        small_cv2 = pil_img_cv2[top_tmp:top_tmp+h_pixel, left_tmp:left_tmp+w_pixel]

                        # 与原版本完全一致的灰度转换和亮度检查
                        small_cv2_gray = bf.cv2_gray(small_cv2)
                        small_mean = np.mean(small_cv2_gray)

                        if small_mean > 50:  # 与原版本一致的阈值
                            # 转换回PIL格式（保持与原版本一致）
                            small_pil = bf.cv2_to_pil(small_cv2)
                            small_pil_list.append(small_pil)
                            small_cord_list.append((left_tmp, top_tmp))

        return small_pil_list, small_cord_list


class GPUHengwenModelManager:
    """GPU横纹检测模型管理器 - 线程安全版本"""
    
    # 类级别的锁，确保线程安全的初始化
    _init_lock = threading.Lock()
    
    def __init__(self, model_path: str, thread_id: Optional[int] = None):
        """
        初始化GPU横纹检测模型管理器
        
        Args:
            model_path: 模型文件路径
            thread_id: 线程ID，用于调试
        """
        self.thread_id = thread_id or threading.current_thread().ident
        self.model_path = model_path
        
        # 模型相关属性
        self.session = None
        self.input_name = None
        self.output_name = None
        
        # 初始化状态
        self._initialized = False
        
        print(f"🟠 [线程{self.thread_id}] 创建横纹检测模型管理器")
        
        # 立即加载模型
        self._load_model()
    
    def _load_model(self) -> None:
        """
        加载GPU ONNX模型 - 线程安全
        """
        with self._init_lock:
            if self._initialized:
                return
                
            print(f"🟠 [线程{self.thread_id}] 开始加载横纹检测模型...")
            
            # 验证模型文件存在
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"ONNX模型文件不存在: {self.model_path}")
            
            # 配置GPU执行提供程序
            providers = []
            if 'CUDAExecutionProvider' in ort.get_available_providers():
                providers.append('CUDAExecutionProvider')
                print(f"🚀 使用CUDA GPU加速")
            else:
                print(f"⚠️ CUDA不可用，回退到CPU")
            
            providers.append('CPUExecutionProvider')
            
            # 创建推理会话
            sess_options = ort.SessionOptions()
            sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
            sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
            
            # GPU推理配置：单线程模式
            sess_options.inter_op_num_threads = 1
            sess_options.intra_op_num_threads = 2
            
            self.session = ort.InferenceSession(
                self.model_path,
                sess_options=sess_options,
                providers=providers
            )
            
            # 获取输入输出名称
            self.input_name = self.session.get_inputs()[0].name
            self.output_name = self.session.get_outputs()[0].name
            
            self._initialized = True
            print(f"✅ GPU ONNX模型加载成功: {self.model_path}")
            print(f"   执行提供程序: {self.session.get_providers()[0]}")
    
    def predict_batch(self, x_batch: np.ndarray, batch_size: int = 32) -> np.ndarray:
        """GPU批量推理"""
        if not self._initialized:
            raise RuntimeError("ONNX模型未正确加载")
        
        # 确保数据类型正确
        if x_batch.dtype != np.float32:
            x_batch = x_batch.astype(np.float32)
        
        total_samples = x_batch.shape[0]
        all_results = []
        
        # 分批处理
        for i in range(0, total_samples, batch_size):
            end_idx = min(i + batch_size, total_samples)
            batch = x_batch[i:end_idx]
            
            # GPU推理
            ort_inputs = {self.input_name: batch}
            batch_results = self.session.run([self.output_name], ort_inputs)[0]
            all_results.append(batch_results)
        
        # 合并所有批次结果
        return np.concatenate(all_results, axis=0)


class HighPerformanceHengwenProcessor:
    """高性能横纹后处理器 - 与现有版本完全一致"""

    def __init__(self):
        self.cfg_dicg = bf.load_json_dict_orig(jiaodian_judge_json_path)
        self.class_id_dict = bf.yanye_fengji_rect_class_id_dict_100
        self.thd = 0.99
        self.now = bf.nowtime()

        # 预计算优化
        self.background_id = self.class_id_dict["background"]
        self.hengwen_id = self.class_id_dict.get("hengwen", 1)
        self.id_to_label = {v: k for k, v in self.class_id_dict.items()}  # 反向映射

        # 图像缓存
        self.image_cache = {}

    def extract_detections_original_logic(self, classes_arr: np.ndarray, small_cord_list: List[Tuple[int, int]],
                                        width: int, height: int, h_pixel: int = 100, w_pixel: int = 100) -> List:
        """
        提取检测结果 - 与原版本完全一致的逻辑
        """
        yolo_para_list = []

        # 完全按照原版本的逻辑
        for i in range(len(classes_arr)):
            class_list = classes_arr[i]
            Max_idx = bf.get_Max_idx(list(class_list))

            for k, v in self.class_id_dict.items():
                thd_tmp = self.thd
                if bf.is_dict(self.thd):
                    if bf.dict_haskey(self.thd, k):
                        thd_tmp = self.thd[k]
                    else:
                        thd_tmp = self.thd["default"]

                if Max_idx == self.background_id and class_list[self.background_id] > thd_tmp:
                    break
                elif Max_idx == v and class_list[v] > thd_tmp:
                    small_cord = small_cord_list[i]
                    left, top = small_cord
                    cx_pixel = left + w_pixel / 2
                    cy_pixel = top + h_pixel / 2
                    yolo_para_list.append([v, cx_pixel, cy_pixel, w_pixel, h_pixel, height, width, class_list])

        return yolo_para_list

    def merge_detections_original_logic(self, yolo_para_list: List, w_pixel: int = 100, h_pixel: int = 100) -> List:
        """
        合并检测结果 - 与原版本完全一致的逻辑
        """
        if len(yolo_para_list) <= 1:
            return yolo_para_list

        skip_list = []

        for i in range(len(yolo_para_list)):
            if i in skip_list:
                continue

            yolo_para_main = yolo_para_list[i]
            id_main = yolo_para_main[0]
            cx_pixel_main, cy_pixel_main = yolo_para_main[1], yolo_para_main[2]
            cx_pixel_sum, cy_pixel_sum = cx_pixel_main, cy_pixel_main
            merge_cnt = 0

            # 与原版本完全一致的合并范围计算
            start_j = i - 0  # 原版本的逻辑
            start_j = start_j if start_j >= 0 else 0
            start_j = start_j if start_j < len(yolo_para_list) else len(yolo_para_list)
            end_j = i + 100
            end_j = end_j if end_j >= 0 else 0
            end_j = end_j if end_j < len(yolo_para_list) else len(yolo_para_list)

            for j in range(start_j, end_j):
                if j not in skip_list and j != i:
                    yolo_para_tmp = yolo_para_list[j]
                    if yolo_para_tmp[0] == id_main:
                        cx_pixel_sub, cy_pixel_sub = yolo_para_tmp[1], yolo_para_tmp[2]
                        if abs(cx_pixel_main - cx_pixel_sub) < w_pixel and abs(cy_pixel_main - cy_pixel_sub) < h_pixel:
                            skip_list.append(j)
                            cx_pixel_sum += cx_pixel_sub
                            cy_pixel_sum += cy_pixel_sub
                            merge_cnt += 1

            if merge_cnt > 0:
                cx_pixel_main_update = int(cx_pixel_sum / (merge_cnt + 1))
                cy_pixel_main_update = int(cy_pixel_sum / (merge_cnt + 1))
                yolo_para_main[1] = cx_pixel_main_update
                yolo_para_main[2] = cy_pixel_main_update

        # 移除被合并的检测
        yolo_para_list_merged = [yolo_para_list[i] for i in range(len(yolo_para_list)) if i not in skip_list]
        return yolo_para_list_merged

    def generate_shapes_optimized(self, yolo_para_list: List, pic_path: str, width: int, height: int) -> list:
        """
        优化的shapes生成 - 与原版本完全一致
        """
        shapes = []

        if not yolo_para_list:
            return shapes

        # 处理每个检测结果 - 与原版本完全一致的格式
        for yolo_para in yolo_para_list:
            # yolo_para格式: [v, cx_pixel, cy_pixel, w_pixel, h_pixel, height, width, class_list]
            id_val = yolo_para[0]
            cx_pixel = yolo_para[1]
            cy_pixel = yolo_para[2]
            w_pixel = yolo_para[3]
            h_pixel = yolo_para[4]

            # 计算边界框坐标
            x1 = max(0, cx_pixel - w_pixel / 2)
            y1 = max(0, cy_pixel - h_pixel / 2)
            x2 = min(width, cx_pixel + w_pixel / 2)
            y2 = min(height, cy_pixel + h_pixel / 2)

            # 获取类别标签
            label = self.id_to_label.get(id_val, "hengwen")

            # 转换为相对坐标 - 与原版本的格式完全一致
            shape = {
                "label": label,
                "points": [
                    [x1 / width, y1 / height],  # 左上角相对坐标
                    [x2 / width, y2 / height]   # 右下角相对坐标
                ],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {},
                "other_data": {}
            }
            shapes.append(shape)

        return shapes

    def clear_cache(self):
        """清理缓存"""
        self.image_cache.clear()


class HengwenPipelineProcessor:
    """横纹检测流水线处理器 - 基于焦点检测的成功架构"""

    def __init__(self, model_manager: 'GPUHengwenModelManager',
                 split_workers: int = 1, postprocess_workers: int = 1,
                 queue_size: int = 16):
        self.model_manager = model_manager
        self.split_workers = split_workers
        self.postprocess_workers = postprocess_workers

        # 初始化队列系统
        self.split_queue = AsyncQueue(queue_size)
        self.inference_queue = AsyncQueue(queue_size)
        self.postprocess_queue = AsyncQueue(queue_size)
        self.result_queue = AsyncQueue(queue_size)

        # 初始化GPU内存池
        self.memory_pool = GPUMemoryPool(pool_size=queue_size, patch_size=(100, 100))

        # 初始化组件
        self.splitter = ZeroIOImageSplitter()
        self.postprocessor = self._get_global_postprocessor()

        # 控制标志
        self._stop_event = threading.Event()
        self._threads = []

        # 线程安全锁
        self._postprocess_lock = threading.Lock()

        # 统计信息
        self.stats = {
            'processed_count': 0,
            'split_time': 0.0,
            'inference_time': 0.0,
            'postprocess_time': 0.0,
            'total_time': 0.0
        }

        print(f"🚀 初始化横纹检测流水线处理器 - 分割工作线程: {split_workers}, 后处理工作线程: {postprocess_workers}")

    def _get_global_postprocessor(self):
        """获取全局后处理器实例 - 确保与现有版本一致"""
        # 使用类级别的单例模式
        if not hasattr(HengwenPipelineProcessor, '_global_postprocessor'):
            HengwenPipelineProcessor._global_postprocessor = HighPerformanceHengwenProcessor()
        return HengwenPipelineProcessor._global_postprocessor

    def _split_worker(self, worker_id: int):
        """图像分割工作线程"""
        print(f"🟠 启动图像分割工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从分割队列获取任务
            item = self.split_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 读取图像 - 与现有版本完全一致的方式
                pil_img = bf.cv2_to_pil(cv2.imread(item.image_path))
                if pil_img is None:
                    print(f"⚠️  [分割{worker_id}] 无法读取图像: {item.image_path}")
                    continue

                # 获取图像尺寸
                width, height = pil_img.size
                item.width = width
                item.height = height

                # 内存中直接分割 - 零重复I/O
                small_pil_list, small_cord_list = self.splitter.split_image_in_memory(pil_img, 100, 100)

                # 批量预处理图像块
                processed_patches = []
                for small_pil in small_pil_list:
                    # 预处理
                    small_pil = bf.keras_trans_pilimg(small_pil,
                                       color_mode="rgb",
                                       target_size=(100, 100),
                                       interpolation="nearest")

                    # 使用与原版本完全一致的img_to_array转换
                    x = img_to_array_compatible(small_pil, data_format='channels_last', dtype='float32')
                    processed_patches.append(x)

                if processed_patches:
                    item.processed_patches = np.array(processed_patches)
                    item.coords = small_cord_list
                    item.stage = "split"

                    # 放入推理队列
                    if not self.inference_queue.put(item, timeout=1.0):
                        print(f"⚠️  [分割{worker_id}] 推理队列已满，丢弃任务")
                        continue

                split_time = time.time() - start_time
                self.stats['split_time'] += split_time

                print(f"🟠 [分割{worker_id}] 分割完成: {os.path.basename(item.image_path)} - {len(small_pil_list)}个块")

            except Exception as e:
                print(f"❌ [分割{worker_id}] 处理异常: {e}")
                continue

        print(f"🟠 图像分割工作线程 {worker_id} 已停止")

    def _inference_worker(self):
        """GPU推理工作线程"""
        print(f"🔶 启动GPU推理工作线程")

        while not self._stop_event.is_set():
            # 从推理队列获取任务
            item = self.inference_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # GPU批量推理 - 使用与现有版本一致的批量大小
                if item.processed_patches is not None and len(item.processed_patches) > 0:
                    classes_arr = self.model_manager.predict_batch(item.processed_patches, batch_size=32)
                    item.inference_result = classes_arr
                    item.stage = "inferred"

                    # 放入后处理队列
                    if not self.postprocess_queue.put(item, timeout=1.0):
                        print(f"⚠️  [推理] 后处理队列已满，丢弃任务")
                        continue

                inference_time = time.time() - start_time
                self.stats['inference_time'] += inference_time

            except Exception as e:
                print(f"❌ [推理] 处理异常: {e}")
                continue

        print(f"🔶 GPU推理工作线程已停止")

    def _postprocess_worker(self, worker_id: int, json_dir: str):
        """后处理工作线程"""
        print(f"🟢 启动后处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从后处理队列获取任务
            item = self.postprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 使用线程锁确保后处理的线程安全
                with self._postprocess_lock:
                    # 提取检测结果 - 与现有版本完全一致
                    yolo_para_list = self.postprocessor.extract_detections_original_logic(
                        item.inference_result, item.coords, item.width, item.height, 100, 100
                    )

                    # 合并检测结果 - 与现有版本完全一致
                    yolo_para_list = self.postprocessor.merge_detections_original_logic(yolo_para_list, 100, 100)

                    # 生成shapes - 与现有版本完全一致
                    shapes = self.postprocessor.generate_shapes_optimized(
                        yolo_para_list, item.image_path, item.width, item.height
                    )

                # 保存JSON - 与现有版本完全一致
                if shapes:
                    json_name = bf.get_file_name(bf.rename_add_post(item.image_path, post="json"))
                    json_path = os.path.join(json_dir, json_name)

                    # 创建基础JSON结构
                    json_data = {
                        "version": "4.5.6",
                        "flags": {},
                        "shapes": shapes,
                        "imagePath": bf.get_file_name(item.image_path),
                        "imageData": None,
                        "imageHeight": 1152,
                        "imageWidth": 512
                    }

                    # 使用标准JSON保存
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)

                    print(f"save json_filepath {json_path}")

                print(f"✅ [后处理{worker_id}] 处理完成: {bf.get_file_name(item.image_path)} - 检测到 {len(shapes)} 个目标")

                # 保存处理结果
                item.processed_result = {
                    'shapes': shapes,
                    'detection_count': len(shapes)
                }
                item.stage = "postprocessed"

                # 放入结果队列
                self.result_queue.put(item, timeout=1.0)

                postprocess_time = time.time() - start_time
                self.stats['postprocess_time'] += postprocess_time
                self.stats['processed_count'] += 1

            except Exception as e:
                print(f"❌ [后处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🟢 后处理工作线程 {worker_id} 已停止")

    def start_pipeline(self, json_dir: str):
        """启动流水线处理"""
        print("🚀 启动横纹检测流水线处理...")

        # 启动图像分割工作线程
        for i in range(self.split_workers):
            thread = threading.Thread(target=self._split_worker, args=(i,))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        # 启动GPU推理工作线程
        thread = threading.Thread(target=self._inference_worker)
        thread.daemon = True
        thread.start()
        self._threads.append(thread)

        # 启动后处理工作线程
        for i in range(self.postprocess_workers):
            thread = threading.Thread(target=self._postprocess_worker,
                                    args=(i, json_dir))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        print(f"✅ 横纹检测流水线已启动 - 总线程数: {len(self._threads)}")

    def stop_pipeline(self):
        """停止流水线处理"""
        print("🛑 停止横纹检测流水线处理...")
        self._stop_event.set()

        # 等待所有线程结束
        for thread in self._threads:
            thread.join(timeout=2.0)

        print("✅ 横纹检测流水线已停止")

    def process_images_pipeline(self, image_paths: List[str], json_dir: str) -> int:
        """使用流水线处理图像列表"""
        if not image_paths:
            return 0

        total_start_time = time.time()

        # 启动流水线
        self.start_pipeline(json_dir)

        try:
            # 将所有图像路径放入分割队列
            print(f"📥 将 {len(image_paths)} 张图像放入处理队列...")
            for image_path in image_paths:
                item = PipelineData(image_path=image_path, timestamp=time.time())

                # 等待队列有空间
                while not self.split_queue.put(item, timeout=0.1):
                    if self._stop_event.is_set():
                        break
                    time.sleep(0.01)

            # 等待所有任务完成
            print("⏳ 等待所有任务完成...")
            processed_count = 0
            total_detections = 0

            # 监控处理进度
            last_progress_time = time.time()
            while processed_count < len(image_paths):
                result_item = self.result_queue.get(timeout=1.0)
                if result_item is not None:
                    processed_count += 1
                    if result_item.processed_result:
                        total_detections += result_item.processed_result.get('detection_count', 0)

                    # 显示进度（每5秒或每10%显示一次）
                    current_time = time.time()
                    progress = (processed_count / len(image_paths)) * 100
                    if (current_time - last_progress_time > 5.0) or (processed_count % max(1, len(image_paths) // 10) == 0):
                        # 获取队列状态
                        split_size = self.split_queue.qsize()
                        inference_size = self.inference_queue.qsize()
                        postprocess_size = self.postprocess_queue.qsize()
                        memory_stats = self.memory_pool.get_pool_stats()

                        print(f"📊 进度: {processed_count}/{len(image_paths)} ({progress:.1f}%) | "
                              f"队列: 分割{split_size} 推理{inference_size} 后处理{postprocess_size} | "
                              f"内存池: {memory_stats['available']}/{memory_stats['total']}")
                        last_progress_time = current_time

                # 检查是否超时
                if time.time() - total_start_time > 300:  # 5分钟超时
                    print("⚠️  处理超时，强制停止")
                    break

            total_time = time.time() - total_start_time
            self.stats['total_time'] = total_time

            # 输出性能统计
            self._print_performance_stats(len(image_paths), total_detections, total_time)

            return total_detections

        finally:
            # 停止流水线
            self.stop_pipeline()

    def _print_performance_stats(self, total_images: int, total_detections: int, total_time: float):
        """打印性能统计信息"""
        print(f"\n📊 横纹检测流水线性能统计:")
        print(f"   总图像数量: {total_images}")
        print(f"   总检测数量: {total_detections}")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均每张图像: {total_time/total_images:.2f}秒")
        print(f"   处理速度: {total_images/total_time:.2f} 张/秒")

        if self.stats['processed_count'] > 0:
            avg_split = self.stats['split_time'] / self.stats['processed_count']
            avg_inference = self.stats['inference_time'] / self.stats['processed_count']
            avg_postprocess = self.stats['postprocess_time'] / self.stats['processed_count']

            print(f"   平均分割时间: {avg_split:.3f}秒")
            print(f"   平均推理时间: {avg_inference:.3f}秒")
            print(f"   平均后处理时间: {avg_postprocess:.3f}秒")

            # 计算并行效率
            sequential_time = avg_split + avg_inference + avg_postprocess
            parallel_efficiency = (sequential_time / (total_time/total_images)) * 100
            print(f"   并行效率: {parallel_efficiency:.1f}%")


def main():
    """主函数"""
    try:
        print("🔥🔥🔥 这是GPU流水线并行版本 - 全新架构 🔥🔥🔥")
        print("🔥🔥🔥 执行main()函数 - 确认执行正确版本 🔥🔥🔥")
        print("🧪 烟叶横纹特征检测GPU流水线并行版本")
        print("="*60)

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_image_dir = os.path.join(current_dir, 'test_images_onnx')
        output_json_dir = os.path.join(current_dir, 'test_output_gpu_pipeline')

        # 路径标准化
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        # 设置模型路径
        model_path = os.path.join(current_dir, "hengwen.onnx")

        # 设置输出目录
        os.makedirs(output_json_dir, exist_ok=True)

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入目录不存在: {input_image_dir}")
            return False

        # 验证模型文件
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return False

        # 获取图像文件列表
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        print(f"📊 找到 {len(image_files)} 个图像文件")

        # 初始化GPU模型管理器
        current_thread_id = threading.current_thread().ident
        print(f"🟠 [线程{current_thread_id}] 初始化横纹检测组件...")

        model_load_start = time.time()
        model_manager = GPUHengwenModelManager(model_path, thread_id=current_thread_id)
        model_load_time = time.time() - model_load_start
        print(f"✅ 模型加载完成，耗时: {model_load_time:.3f}秒")

        # 使用流水线并行处理器 - 基于焦点检测的成功经验，使用简化配置
        pipeline_processor = HengwenPipelineProcessor(
            model_manager,
            split_workers=1,  # 1个分割线程
            postprocess_workers=1  # 1个后处理线程
        )

        # 构建图像路径列表
        image_paths = [os.path.join(input_image_dir, image_file) for image_file in image_files]

        print(f"\n🚀 开始横纹检测流水线并行处理...")

        # 流水线并行处理所有图像
        total_start_time = time.time()
        total_detections = pipeline_processor.process_images_pipeline(
            image_paths, output_json_dir
        )
        total_time = time.time() - total_start_time

        print(f"\n🎉 横纹检测流水线并行处理完成！")
        print(f"📊 总处理时间: {total_time:.2f}秒")
        print(f"📊 平均每张图像: {total_time/len(image_files):.2f}秒")
        print(f"📊 处理吞吐量: {len(image_files)/total_time:.2f}张/秒")
        print(f"📊 总检测数量: {total_detections}")
        print(f"🚀 流水线并行架构 - 分割/推理/后处理并行执行")

        return True

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
