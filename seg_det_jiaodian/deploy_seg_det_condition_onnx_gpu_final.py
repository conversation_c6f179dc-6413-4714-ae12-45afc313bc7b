#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶条件检测GPU流水线并行版本
基于轮廓残缺填充的成功双模型流水线架构，集成优化后的焦点检测和浮青检测

作者: 系统架构师
日期: 2025-01-27
版本: 2.0.0

关键特性：
1. 双模型流水线并行架构
2. 高性能内存池管理
3. 异步队列系统
4. 智能结果合并
5. 与现有程序完全一致的检测结果
"""

import os
import sys
import time
import threading
import cv2
import numpy as np
import json
import queue
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

try:
    import base_function as bf
    print("🔥🔥🔥 GPU流水线并行版本 - 成功导入所有必要模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入必要模块 {e}")
    sys.exit(1)

# 导入优化后的检测模块
try:
    from seg_det_jiaodian.deploy_seg_det_jiaodian_onnx_gpu_final import (
        GPUJiaodianModelManager, 
        HighPerformancePostProcessor as JiaodianPostProcessor,
        ZeroIOImageSplitter
    )
    print("✅ 成功导入优化后的焦点检测模块")
except ImportError as e:
    print(f"❌ 无法导入焦点检测模块: {e}")
    sys.exit(1)

try:
    from seg_det_jiaodian.deploy_seg_det_fuqing_onnx_gpu_final import (
        GPUFuqingModelManager,
        HighPerformanceFuqingProcessor as FuqingPostProcessor
    )
    print("✅ 成功导入优化后的浮青检测模块")
except ImportError as e:
    print(f"❌ 无法导入浮青检测模块: {e}")
    sys.exit(1)


@dataclass
class PipelineData:
    """流水线数据结构"""
    image_path: str
    image_data: Optional[np.ndarray] = None
    
    # 焦点检测相关
    jiaodian_patches: Optional[List] = None
    jiaodian_coords: Optional[List] = None
    jiaodian_processed_patches: Optional[np.ndarray] = None
    jiaodian_result: Optional[np.ndarray] = None
    jiaodian_shapes: Optional[List] = None
    
    # 浮青检测相关
    fuqing_preprocessed: Optional[np.ndarray] = None
    fuqing_ratio: Optional[Tuple[float, float]] = None
    fuqing_pad_w: float = 0.0
    fuqing_pad_h: float = 0.0
    fuqing_result: Optional[List] = None
    fuqing_shapes: Optional[List] = None
    
    # 合并结果
    merged_shapes: Optional[List] = None
    processed_result: Optional[Dict] = None
    
    timestamp: float = 0.0
    stage: str = "init"  # init, preprocessed, inferred, postprocessed
    width: int = 0
    height: int = 0


class DualModelMemoryPool:
    """双模型内存池管理器 - 管理焦点和浮青两个模型的内存"""
    
    def __init__(self, pool_size: int = 16):
        self.pool_size = pool_size
        
        # 焦点检测内存池（图像块）
        self.jiaodian_buffers = queue.Queue()
        
        # 浮青检测内存池（完整图像）
        self.fuqing_buffers = queue.Queue()
        
        self.used_buffers = set()
        self._lock = threading.Lock()
        
        # 预分配内存缓冲区
        print(f"🔧 初始化双模型内存池 - 池大小: {pool_size}")
        
        # 焦点检测缓冲区 (图像块)
        for _ in range(pool_size):
            buffer = np.zeros((1024, 30, 30, 3), dtype=np.float32)
            self.jiaodian_buffers.put(buffer)
        
        # 浮青检测缓冲区 (完整图像)
        for _ in range(pool_size):
            buffer = np.zeros((1, 3, 640, 640), dtype=np.float32)
            self.fuqing_buffers.put(buffer)
    
    def get_jiaodian_buffer(self, batch_size: int = 1024) -> Optional[np.ndarray]:
        """获取焦点检测内存缓冲区"""
        try:
            buffer = self.jiaodian_buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            if batch_size <= buffer.shape[0]:
                return buffer[:batch_size]
            else:
                return np.zeros((batch_size, 30, 30, 3), dtype=np.float32)
        except queue.Empty:
            print("⚠️  焦点检测内存池已满，创建临时缓冲区")
            return np.zeros((batch_size, 30, 30, 3), dtype=np.float32)
    
    def get_fuqing_buffer(self) -> Optional[np.ndarray]:
        """获取浮青检测内存缓冲区"""
        try:
            buffer = self.fuqing_buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            return buffer
        except queue.Empty:
            print("⚠️  浮青检测内存池已满，创建临时缓冲区")
            return np.zeros((1, 3, 640, 640), dtype=np.float32)
    
    def return_jiaodian_buffer(self, buffer: np.ndarray):
        """归还焦点检测内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.jiaodian_buffers.put_nowait(buffer)
                    except queue.Full:
                        print("⚠️  焦点检测内存池队列已满")
    
    def return_fuqing_buffer(self, buffer: np.ndarray):
        """归还浮青检测内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.fuqing_buffers.put_nowait(buffer)
                    except queue.Full:
                        print("⚠️  浮青检测内存池队列已满")
    
    def get_pool_stats(self) -> Dict[str, int]:
        """获取内存池统计信息"""
        with self._lock:
            return {
                'jiaodian_available': self.jiaodian_buffers.qsize(),
                'fuqing_available': self.fuqing_buffers.qsize(),
                'used': len(self.used_buffers),
                'total': self.pool_size * 2
            }


class AsyncQueue:
    """异步队列 - 支持流水线各阶段的数据传递"""
    
    def __init__(self, maxsize: int = 16):
        self.queue = queue.Queue(maxsize=maxsize)
        self.maxsize = maxsize
        
    def put(self, item, timeout: float = 1.0) -> bool:
        """放入数据项"""
        try:
            self.queue.put(item, timeout=timeout)
            return True
        except queue.Full:
            return False
    
    def get(self, timeout: float = 1.0) -> Optional[Any]:
        """获取数据项"""
        try:
            return self.queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()


class ShapeMerger:
    """智能shapes合并器 - 合并焦点检测和浮青检测的结果"""
    
    def __init__(self):
        print("🔧 初始化智能shapes合并器")
    
    def merge_shapes(self, jiaodian_shapes: List, fuqing_shapes: List, 
                    image_path: str, width: int, height: int) -> List:
        """
        智能合并两种检测的shapes - 与现有版本完全一致
        
        Args:
            jiaodian_shapes: 焦点检测的shapes
            fuqing_shapes: 浮青检测的shapes
            image_path: 图像路径
            width: 图像宽度
            height: 图像高度
            
        Returns:
            合并后的shapes列表
        """
        # 直接合并两个shapes列表 - 与现有版本完全一致
        merged_shapes = []
        
        # 添加焦点检测结果
        if jiaodian_shapes:
            merged_shapes.extend(jiaodian_shapes)
        
        # 添加浮青检测结果
        if fuqing_shapes:
            merged_shapes.extend(fuqing_shapes)
        
        return merged_shapes


class ConditionPipelineProcessor:
    """条件检测流水线处理器 - 双模型流水线并行架构"""

    def __init__(self, jiaodian_manager: 'GPUJiaodianModelManager',
                 fuqing_manager: 'GPUFuqingModelManager',
                 preprocess_workers: int = 1, postprocess_workers: int = 1,
                 queue_size: int = 16, conf_threshold: float = 0.4, iou_threshold: float = 0.45):
        self.jiaodian_manager = jiaodian_manager
        self.fuqing_manager = fuqing_manager
        self.preprocess_workers = preprocess_workers
        self.postprocess_workers = postprocess_workers
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold

        # 初始化队列系统
        self.preprocess_queue = AsyncQueue(queue_size)
        self.inference_queue = AsyncQueue(queue_size)
        self.postprocess_queue = AsyncQueue(queue_size)
        self.result_queue = AsyncQueue(queue_size)

        # 初始化双模型内存池
        self.memory_pool = DualModelMemoryPool(pool_size=queue_size)

        # 初始化组件
        self.jiaodian_splitter = ZeroIOImageSplitter()
        self.jiaodian_postprocessor = self._get_global_jiaodian_postprocessor()
        self.fuqing_postprocessor = self._get_global_fuqing_postprocessor()
        self.shape_merger = ShapeMerger()

        # 控制标志
        self._stop_event = threading.Event()
        self._threads = []

        # 线程安全锁
        self._postprocess_lock = threading.Lock()

        # 统计信息
        self.stats = {
            'processed_count': 0,
            'preprocess_time': 0.0,
            'inference_time': 0.0,
            'postprocess_time': 0.0,
            'total_time': 0.0
        }

        print(f"🚀 初始化条件检测流水线处理器 - 预处理工作线程: {preprocess_workers}, 后处理工作线程: {postprocess_workers}")

    def _get_global_jiaodian_postprocessor(self):
        """获取全局焦点后处理器实例"""
        if not hasattr(ConditionPipelineProcessor, '_global_jiaodian_postprocessor'):
            ConditionPipelineProcessor._global_jiaodian_postprocessor = JiaodianPostProcessor()
        return ConditionPipelineProcessor._global_jiaodian_postprocessor

    def _get_global_fuqing_postprocessor(self):
        """获取全局浮青后处理器实例"""
        if not hasattr(ConditionPipelineProcessor, '_global_fuqing_postprocessor'):
            ConditionPipelineProcessor._global_fuqing_postprocessor = FuqingPostProcessor()
        return ConditionPipelineProcessor._global_fuqing_postprocessor

    def _preprocess_worker(self, worker_id: int):
        """预处理工作线程 - 同时处理焦点和浮青的预处理"""
        print(f"🔵 启动预处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从预处理队列获取任务
            item = self.preprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 读取图像 - 与现有版本完全一致的方式
                image = cv2.imread(item.image_path)
                if image is None:
                    print(f"⚠️  [预处理{worker_id}] 无法读取图像: {item.image_path}")
                    continue

                # 获取图像尺寸
                height, width = image.shape[:2]
                item.width = width
                item.height = height
                item.image_data = image

                # 焦点检测预处理：图像分割
                small_pil_list, small_cord_list = self.jiaodian_splitter.split_image_in_memory(
                    bf.cv2_to_pil(image), 30, 30
                )

                # 批量预处理焦点图像块
                processed_patches = []
                for small_pil in small_pil_list:
                    # 预处理
                    small_pil = bf.keras_trans_pilimg(small_pil,
                                       color_mode="rgb",
                                       target_size=(30, 30),
                                       interpolation="nearest")

                    # 使用与原版本完全一致的img_to_array转换
                    from seg_det_jiaodian.deploy_seg_det_jiaodian_onnx_gpu_final import img_to_array_compatible
                    x = img_to_array_compatible(small_pil, data_format='channels_last', dtype='float32')
                    processed_patches.append(x)

                if processed_patches:
                    item.jiaodian_processed_patches = np.array(processed_patches)
                    item.jiaodian_coords = small_cord_list

                # 浮青检测预处理：letterbox处理
                img_processed, ratio, (pad_w, pad_h) = self.fuqing_manager.preprocess(image)
                item.fuqing_preprocessed = img_processed
                item.fuqing_ratio = ratio
                item.fuqing_pad_w = pad_w
                item.fuqing_pad_h = pad_h

                item.stage = "preprocessed"

                # 放入推理队列
                if not self.inference_queue.put(item, timeout=1.0):
                    print(f"⚠️  [预处理{worker_id}] 推理队列已满，丢弃任务")
                    continue

                preprocess_time = time.time() - start_time
                self.stats['preprocess_time'] += preprocess_time

            except Exception as e:
                print(f"❌ [预处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🔵 预处理工作线程 {worker_id} 已停止")

    def _dual_inference_worker(self):
        """双模型推理工作线程 - 并行执行焦点和浮青推理"""
        print(f"🔶 启动双模型推理工作线程")

        while not self._stop_event.is_set():
            # 从推理队列获取任务
            item = self.inference_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 并行推理两个模型
                with ThreadPoolExecutor(max_workers=2, thread_name_prefix="DualModelInference") as executor:
                    # 提交焦点检测任务
                    jiaodian_future = None
                    if item.jiaodian_processed_patches is not None and len(item.jiaodian_processed_patches) > 0:
                        jiaodian_future = executor.submit(
                            self.jiaodian_manager.predict_batch,
                            item.jiaodian_processed_patches,
                            1024
                        )

                    # 提交浮青检测任务
                    fuqing_future = None
                    if item.fuqing_preprocessed is not None:
                        fuqing_future = executor.submit(
                            self.fuqing_manager.predict,
                            item.fuqing_preprocessed
                        )

                    # 等待焦点检测结果
                    if jiaodian_future:
                        item.jiaodian_result = jiaodian_future.result()

                    # 等待浮青检测结果并后处理
                    if fuqing_future:
                        fuqing_preds = fuqing_future.result()
                        item.fuqing_result = self.fuqing_manager.postprocess(
                            fuqing_preds, item.image_data, item.fuqing_ratio,
                            item.fuqing_pad_w, item.fuqing_pad_h,
                            self.conf_threshold, self.iou_threshold
                        )

                item.stage = "inferred"

                # 放入后处理队列
                if not self.postprocess_queue.put(item, timeout=1.0):
                    print(f"⚠️  [推理] 后处理队列已满，丢弃任务")
                    continue

                inference_time = time.time() - start_time
                self.stats['inference_time'] += inference_time

            except Exception as e:
                print(f"❌ [推理] 处理异常: {e}")
                continue

        print(f"🔶 双模型推理工作线程已停止")

    def _postprocess_worker(self, worker_id: int, json_dir: str):
        """后处理工作线程 - 处理双模型结果并合并"""
        print(f"🟢 启动后处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从后处理队列获取任务
            item = self.postprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 使用线程锁确保后处理的线程安全
                with self._postprocess_lock:
                    # 处理焦点检测结果
                    jiaodian_shapes = []
                    if item.jiaodian_result is not None and item.jiaodian_coords is not None:
                        # 提取焦点检测结果
                        yolo_para_list = self.jiaodian_postprocessor.extract_detections_original_logic(
                            item.jiaodian_result, item.jiaodian_coords, item.width, item.height
                        )

                        # 合并焦点检测结果
                        yolo_para_list = self.jiaodian_postprocessor.merge_detections_original_logic(yolo_para_list)

                        # 生成焦点shapes
                        jiaodian_shapes = self.jiaodian_postprocessor.generate_shapes_optimized(
                            yolo_para_list, item.image_path, item.width, item.height
                        )

                    # 处理浮青检测结果
                    fuqing_shapes = []
                    if item.fuqing_result is not None:
                        fuqing_shapes = self.fuqing_postprocessor.generate_shapes_optimized(
                            item.fuqing_result, item.image_path, item.width, item.height
                        )

                    # 合并两种检测结果
                    merged_shapes = self.shape_merger.merge_shapes(
                        jiaodian_shapes, fuqing_shapes, item.image_path, item.width, item.height
                    )

                # 保存JSON - 与现有版本完全一致
                if merged_shapes:
                    json_name = bf.get_file_name(bf.rename_add_post(item.image_path, post="json"))
                    json_path = os.path.join(json_dir, json_name)

                    # 创建基础JSON结构
                    json_data = {
                        "version": "4.5.6",
                        "flags": {},
                        "shapes": merged_shapes,
                        "imagePath": bf.get_file_name(item.image_path),
                        "imageData": None,
                        "imageHeight": 1152,
                        "imageWidth": 512
                    }

                    # 使用标准JSON保存
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)

                    print(f"save json_filepath {json_path}")

                jiaodian_count = len(jiaodian_shapes) if jiaodian_shapes else 0
                fuqing_count = len(fuqing_shapes) if fuqing_shapes else 0
                total_count = len(merged_shapes) if merged_shapes else 0

                print(f"✅ [后处理{worker_id}] 处理完成: {bf.get_file_name(item.image_path)} - 焦点:{jiaodian_count}, 浮青:{fuqing_count}, 总计:{total_count}")

                # 保存处理结果
                item.processed_result = {
                    'jiaodian_shapes': jiaodian_shapes,
                    'fuqing_shapes': fuqing_shapes,
                    'merged_shapes': merged_shapes,
                    'jiaodian_count': jiaodian_count,
                    'fuqing_count': fuqing_count,
                    'total_count': total_count
                }
                item.stage = "postprocessed"

                # 放入结果队列
                self.result_queue.put(item, timeout=1.0)

                postprocess_time = time.time() - start_time
                self.stats['postprocess_time'] += postprocess_time
                self.stats['processed_count'] += 1

            except Exception as e:
                print(f"❌ [后处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🟢 后处理工作线程 {worker_id} 已停止")

    def start_pipeline(self, json_dir: str):
        """启动流水线处理"""
        print("🚀 启动条件检测流水线处理...")

        # 启动预处理工作线程
        for i in range(self.preprocess_workers):
            thread = threading.Thread(target=self._preprocess_worker, args=(i,))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        # 启动双模型推理工作线程
        thread = threading.Thread(target=self._dual_inference_worker)
        thread.daemon = True
        thread.start()
        self._threads.append(thread)

        # 启动后处理工作线程
        for i in range(self.postprocess_workers):
            thread = threading.Thread(target=self._postprocess_worker,
                                    args=(i, json_dir))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        print(f"✅ 条件检测流水线已启动 - 总线程数: {len(self._threads)}")

    def stop_pipeline(self):
        """停止流水线处理"""
        print("🛑 停止条件检测流水线处理...")
        self._stop_event.set()

        # 等待所有线程结束
        for thread in self._threads:
            thread.join(timeout=2.0)

        print("✅ 条件检测流水线已停止")

    def process_images_pipeline(self, image_paths: List[str], json_dir: str) -> Dict[str, int]:
        """使用流水线处理图像列表"""
        if not image_paths:
            return {'jiaodian': 0, 'fuqing': 0, 'total': 0}

        total_start_time = time.time()

        # 启动流水线
        self.start_pipeline(json_dir)

        try:
            # 将所有图像路径放入预处理队列
            print(f"📥 将 {len(image_paths)} 张图像放入处理队列...")
            for image_path in image_paths:
                item = PipelineData(image_path=image_path, timestamp=time.time())

                # 等待队列有空间
                while not self.preprocess_queue.put(item, timeout=0.1):
                    if self._stop_event.is_set():
                        break
                    time.sleep(0.01)

            # 等待所有任务完成
            print("⏳ 等待所有任务完成...")
            processed_count = 0
            total_jiaodian = 0
            total_fuqing = 0
            total_detections = 0

            # 监控处理进度
            last_progress_time = time.time()
            while processed_count < len(image_paths):
                result_item = self.result_queue.get(timeout=1.0)
                if result_item is not None:
                    processed_count += 1
                    if result_item.processed_result:
                        total_jiaodian += result_item.processed_result.get('jiaodian_count', 0)
                        total_fuqing += result_item.processed_result.get('fuqing_count', 0)
                        total_detections += result_item.processed_result.get('total_count', 0)

                    # 显示进度（每5秒或每10%显示一次）
                    current_time = time.time()
                    progress = (processed_count / len(image_paths)) * 100
                    if (current_time - last_progress_time > 5.0) or (processed_count % max(1, len(image_paths) // 10) == 0):
                        # 获取队列状态
                        preprocess_size = self.preprocess_queue.qsize()
                        inference_size = self.inference_queue.qsize()
                        postprocess_size = self.postprocess_queue.qsize()
                        memory_stats = self.memory_pool.get_pool_stats()

                        print(f"📊 进度: {processed_count}/{len(image_paths)} ({progress:.1f}%) | "
                              f"队列: 预处理{preprocess_size} 推理{inference_size} 后处理{postprocess_size} | "
                              f"内存池: 焦点{memory_stats['jiaodian_available']} 浮青{memory_stats['fuqing_available']}")
                        last_progress_time = current_time

                # 检查是否超时
                if time.time() - total_start_time > 300:  # 5分钟超时
                    print("⚠️  处理超时，强制停止")
                    break

            total_time = time.time() - total_start_time
            self.stats['total_time'] = total_time

            # 输出性能统计
            self._print_performance_stats(len(image_paths), total_jiaodian, total_fuqing, total_detections, total_time)

            return {'jiaodian': total_jiaodian, 'fuqing': total_fuqing, 'total': total_detections}

        finally:
            # 停止流水线
            self.stop_pipeline()

    def _print_performance_stats(self, total_images: int, total_jiaodian: int, total_fuqing: int,
                               total_detections: int, total_time: float):
        """打印性能统计信息"""
        print(f"\n📊 条件检测流水线性能统计:")
        print(f"   总图像数量: {total_images}")
        print(f"   焦点检测数量: {total_jiaodian}")
        print(f"   浮青检测数量: {total_fuqing}")
        print(f"   总检测数量: {total_detections}")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均每张图像: {total_time/total_images:.2f}秒")
        print(f"   处理速度: {total_images/total_time:.2f} 张/秒")

        if self.stats['processed_count'] > 0:
            avg_preprocess = self.stats['preprocess_time'] / self.stats['processed_count']
            avg_inference = self.stats['inference_time'] / self.stats['processed_count']
            avg_postprocess = self.stats['postprocess_time'] / self.stats['processed_count']

            print(f"   平均预处理时间: {avg_preprocess:.3f}秒")
            print(f"   平均双模型推理时间: {avg_inference:.3f}秒")
            print(f"   平均后处理时间: {avg_postprocess:.3f}秒")

            # 计算并行效率
            sequential_time = avg_preprocess + avg_inference + avg_postprocess
            parallel_efficiency = (sequential_time / (total_time/total_images)) * 100
            print(f"   并行效率: {parallel_efficiency:.1f}%")


def main():
    """主函数"""
    try:
        print("🔥🔥🔥 这是双模型GPU流水线并行版本 - 全新架构 🔥🔥🔥")
        print("🔥🔥🔥 执行main()函数 - 确认执行正确版本 🔥🔥🔥")
        print("🧪 烟叶条件检测双模型GPU流水线并行版本")
        print("="*60)

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_image_dir = os.path.join(current_dir, 'test_images_onnx_gpu')
        output_json_dir = os.path.join(current_dir, 'test_output_condition_pipeline')

        # 路径标准化
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        # 设置模型路径
        jiaodian_model_path = os.path.join(current_dir, "jiaodian_deng.onnx")
        fuqing_model_path = os.path.join(current_dir, "fuqing.onnx")

        # 设置输出目录
        os.makedirs(output_json_dir, exist_ok=True)

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入目录不存在: {input_image_dir}")
            return False

        # 验证模型文件
        if not os.path.exists(jiaodian_model_path):
            print(f"❌ 焦点模型文件不存在: {jiaodian_model_path}")
            return False
        if not os.path.exists(fuqing_model_path):
            print(f"❌ 浮青模型文件不存在: {fuqing_model_path}")
            return False

        # 获取图像文件列表
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        print(f"📊 找到 {len(image_files)} 个图像文件")

        # 初始化双模型管理器
        current_thread_id = threading.current_thread().ident
        print(f"🔧 [线程{current_thread_id}] 初始化双模型管理器...")

        model_load_start = time.time()

        # 加载焦点检测模型
        jiaodian_manager = GPUJiaodianModelManager(jiaodian_model_path, thread_id=current_thread_id)

        # 加载浮青检测模型
        fuqing_manager = GPUFuqingModelManager(fuqing_model_path, thread_id=current_thread_id)

        model_load_time = time.time() - model_load_start
        print(f"✅ 双模型加载完成，耗时: {model_load_time:.3f}秒")

        # 使用双模型流水线并行处理器
        pipeline_processor = ConditionPipelineProcessor(
            jiaodian_manager,
            fuqing_manager,
            preprocess_workers=1,  # 1个预处理线程
            postprocess_workers=1,  # 1个后处理线程
            conf_threshold=0.4,
            iou_threshold=0.45
        )

        # 构建图像路径列表
        image_paths = [os.path.join(input_image_dir, image_file) for image_file in image_files]

        print(f"\n🚀 开始条件检测双模型流水线并行处理...")

        # 双模型流水线并行处理所有图像
        total_start_time = time.time()
        detection_counts = pipeline_processor.process_images_pipeline(
            image_paths, output_json_dir
        )
        total_time = time.time() - total_start_time

        print(f"\n🎉 条件检测双模型流水线并行处理完成！")
        print(f"📊 总处理时间: {total_time:.2f}秒")
        print(f"📊 平均每张图像: {total_time/len(image_files):.2f}秒")
        print(f"📊 处理吞吐量: {len(image_files)/total_time:.2f}张/秒")
        print(f"📊 焦点检测数量: {detection_counts['jiaodian']}")
        print(f"📊 浮青检测数量: {detection_counts['fuqing']}")
        print(f"📊 总检测数量: {detection_counts['total']}")
        print(f"🚀 双模型流水线并行架构 - 焦点/浮青并行执行")

        return True

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
