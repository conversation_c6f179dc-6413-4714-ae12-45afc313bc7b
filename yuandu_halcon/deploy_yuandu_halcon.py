#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
烟叶圆度计算独立部署程序
整合圆度计算功能：基于canque_fill轮廓计算圆度值

作者: Augment Agent
日期: 2025-06-30
版本: 1.0
基于: xu/tbc_rank/predict/py/yuandu_halcon_tmp.py
     wt/yanyefenji_data_test/yuandu_halcon.py
"""

import os
import sys
import json
import math
import cv2
import numpy as np
import time
from pathlib import Path
import copy

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

def do_one(json_path, image_path):
    image = bf.cv2_read_file(image_path)
    height, width, _ = bf.cv2_size(image)
    lunkuo = bf.labelme_json_shape_read_point_onlyjson(json_path, label_name="canque_fill", shapes_name="shapes")[0]
    lunkuo = [[int(x[0] * width), int(x[1] * height)] for x in lunkuo]
    # mask = np.zeros((height, width), dtype="uint8")
    lunkuo_counter = np.array(lunkuo)
    # cv2.drawContours(mask, [lunkuo_counter], 0, 255, 2)
    mu = cv2.moments(lunkuo_counter, False)
    mc = [int(mu['m10'] / mu['m00']), int(mu['m01'] / mu['m00'])]
    # lunkuo_pixs = []
    # for h in range(height):
    #     for w in range(width):
    #         if mask[h, w] == 255:
    #             lunkuo_pixs.append([w, h])
    dist_sum = 0
    sig_sum = 0
    for p in lunkuo:
        dist_sum += bf.euclidean_dist(p, mc)
    distance = dist_sum / len(lunkuo)
    for p in lunkuo:
        sig_sum += (bf.euclidean_dist(p, mc) - distance) ** 2
    sigma = math.sqrt(sig_sum / len(lunkuo))
    roundness = 1 - (sigma / distance)
    print("image={} roundness={} sigma={} distance={}".format(image_path, roundness, sigma, distance))
    bf.labelme_json_add_userfeature_file(json_path, {"yuandu_halcon": roundness})

def do_dir(json_root, image_root):
    image_list, _ = bf.scan_files_2(image_root, except_midfix="mid", postfix="bmp")
    for image_path in image_list:
        json_path = bf.pathjoin(json_root, bf.rename_add_post(bf.get_file_name(image_path), post="json"))
        do_one(json_path, image_path)
        

def postprocess(image_path: str, json_data: dict) -> dict:
    """
    圆度计算的后处理函数

    Args:
        image_path: 单张图像路径
        json_data: 单个JSON数据内容

    Returns:
        包含"user_feature"的JSON内容
    """
    try:
        print(f"🔄 圆度计算处理: {os.path.basename(image_path)}")

        # 创建临时目录
        temp_dir = "temp_yuandu"
        os.makedirs(temp_dir, exist_ok=True)

        # 创建临时文件
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        temp_json_path = os.path.join(temp_dir, f"{base_name}.json")

        # 保存临时JSON文件
        with open(temp_json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        # 调用圆度计算函数
        do_one(temp_json_path, image_path)

        # 读取处理后的结果
        with open(temp_json_path, 'r', encoding='utf-8') as f:
            result_data = json.load(f)

        # 提取user_feature
        user_feature = result_data.get("user_feature", {})

        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)

        print(f"✅ 圆度计算处理完成")

        return {"user_feature": user_feature}

    except Exception as e:
        print(f"❌ 圆度计算处理失败: {e}")
        import traceback
        traceback.print_exc()
        return {"user_feature": {}}


def main():
    """
    主入口函数 - 测试postprocess函数
    """
    print("🚀 烟叶圆度计算独立部署程序 - postprocess测试")
    print("=" * 60)

    # 测试路径配置
    test_image_dir = "/home/<USER>/xm/code/coderafactor/test_data/test_inputs/"
    test_json_dir = "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/output_optimized/"
    test_output_dir = "yuandu_halcon/test_output"

    print(f"📁 测试图像目录: {test_image_dir}")
    print(f"📁 测试JSON目录: {test_json_dir}")
    print(f"📁 对比输出目录: {test_output_dir}")

    # 获取测试文件
    if not os.path.exists(test_image_dir):
        print(f"❌ 测试图像目录不存在: {test_image_dir}")
        return 1

    if not os.path.exists(test_json_dir):
        print(f"❌ 测试JSON目录不存在: {test_json_dir}")
        return 1

    # 获取图像文件
    image_files = []
    for ext in ['.bmp', '.jpg', '.jpeg', '.png']:
        image_files.extend([f for f in os.listdir(test_image_dir) if f.lower().endswith(ext)])

    if not image_files:
        print(f"❌ 未找到测试图像文件")
        return 1

    print(f"📊 找到 {len(image_files)} 个测试图像")

    # 测试postprocess函数
    success_count = 0
    total_count = 0

    for image_file in image_files[:3]:  # 测试前3个文件
        try:
            total_count += 1
            print(f"\n🧪 测试文件 {total_count}: {image_file}")

            # 构建文件路径
            image_path = os.path.join(test_image_dir, image_file)
            base_name = os.path.splitext(image_file)[0]
            json_file = base_name + ".json"
            json_path = os.path.join(test_json_dir, json_file)

            # 检查JSON文件是否存在
            if not os.path.exists(json_path):
                print(f"   ⚠️  对应JSON文件不存在: {json_file}")
                continue

            # 读取JSON数据
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 调用postprocess函数
            result = postprocess(image_path, json_data)

            # 检查结果
            if "user_feature" in result:
                print(f"   ✅ postprocess成功，user_feature包含 {len(result['user_feature'])} 个特征")

                # 与test_output中的结果对比（如果存在）
                compare_path = os.path.join(test_output_dir, json_file)
                if os.path.exists(compare_path):
                    with open(compare_path, 'r', encoding='utf-8') as f:
                        compare_data = json.load(f)

                    compare_features = compare_data.get("user_feature", {})
                    result_features = result["user_feature"]

                    print(f"   📊 对比结果: 原有{len(compare_features)}个特征 vs 新生成{len(result_features)}个特征")

                    # 简单的数值对比
                    if compare_features and result_features:
                        common_keys = set(compare_features.keys()) & set(result_features.keys())
                        if common_keys:
                            print(f"   🔍 共同特征: {len(common_keys)}个")
                        else:
                            print(f"   ⚠️  没有共同特征")

                success_count += 1
            else:
                print(f"   ❌ postprocess失败，未返回user_feature")

        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n📈 测试结果: {success_count}/{total_count} 成功")
    print("✅ postprocess函数测试完成!")

    return 0 if success_count > 0 else 1


def batch_process():
    """批量处理函数"""
    print("烟叶圆度计算独立部署程序 - 批量处理")
    print("=" * 50)

    # 配置路径
    json_dir = "coderafactor/yanye_user_feature/merged_results"
    image_dir = "coderafactor/test_images"
    output_dir = "coderafactor/yuandu_halcon/output"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有图像文件
    if not os.path.exists(image_dir):
        print(f"图像目录不存在: {image_dir}")
        return

    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png'))]

    if not image_files:
        print(f"在目录 {image_dir} 中未找到图像文件")
        return

    print(f"找到 {len(image_files)} 个图像文件")

    success_count = 0
    start_time = time.time()

    for i, image_file in enumerate(image_files[:10], 1):  # 只处理前10个文件
        json_file = image_file.replace('.bmp', '.json')
        image_path = os.path.join(image_dir, image_file)
        json_path = os.path.join(json_dir, json_file)

        print(f"\n[{i}/10] 处理: {image_file}")

        if not os.path.exists(json_path):
            print(f"❌ JSON文件不存在: {json_path}")
            continue

        # 复制JSON文件到输出目录
        output_json_path = os.path.join(output_dir, json_file)
        import shutil
        shutil.copy2(json_path, output_json_path)

        try:
            # 调用圆度计算函数
            do_one(output_json_path, image_path)
            success_count += 1
            print(f"✅ 处理成功")
        except Exception as e:
            print(f"❌ 处理失败: {e}")

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n批量处理完成!")
    print(f"总文件数: 10")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {10 - success_count}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均耗时: {total_time/10:.2f}秒/文件")


if __name__ == "__main__":
    main()
