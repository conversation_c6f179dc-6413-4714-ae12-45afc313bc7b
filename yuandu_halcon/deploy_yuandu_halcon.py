#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
烟叶圆度计算独立部署程序
整合圆度计算功能：基于canque_fill轮廓计算圆度值

作者: Augment Agent
日期: 2025-06-30
版本: 1.0
基于: xu/tbc_rank/predict/py/yuandu_halcon_tmp.py
     wt/yanyefenji_data_test/yuandu_halcon.py
"""

import os
import sys
import json
import math
import cv2
import numpy as np
import time
from pathlib import Path
import copy

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

def do_one(json_path, image_path):
    image = bf.cv2_read_file(image_path)
    height, width, _ = bf.cv2_size(image)
    lunkuo = bf.labelme_json_shape_read_point_onlyjson(json_path, label_name="canque_fill", shapes_name="shapes")[0]
    lunkuo = [[int(x[0] * width), int(x[1] * height)] for x in lunkuo]
    # mask = np.zeros((height, width), dtype="uint8")
    lunkuo_counter = np.array(lunkuo)
    # cv2.drawContours(mask, [lunkuo_counter], 0, 255, 2)
    mu = cv2.moments(lunkuo_counter, False)
    mc = [int(mu['m10'] / mu['m00']), int(mu['m01'] / mu['m00'])]
    # lunkuo_pixs = []
    # for h in range(height):
    #     for w in range(width):
    #         if mask[h, w] == 255:
    #             lunkuo_pixs.append([w, h])
    dist_sum = 0
    sig_sum = 0
    for p in lunkuo:
        dist_sum += bf.euclidean_dist(p, mc)
    distance = dist_sum / len(lunkuo)
    for p in lunkuo:
        sig_sum += (bf.euclidean_dist(p, mc) - distance) ** 2
    sigma = math.sqrt(sig_sum / len(lunkuo))
    roundness = 1 - (sigma / distance)
    print("image={} roundness={} sigma={} distance={}".format(image_path, roundness, sigma, distance))
    bf.labelme_json_add_userfeature_file(json_path, {"yuandu_halcon": roundness})

def do_dir(json_root, image_root):
    image_list, _ = bf.scan_files_2(image_root, except_midfix="mid", postfix="bmp")
    for image_path in image_list:
        json_path = bf.pathjoin(json_root, bf.rename_add_post(bf.get_file_name(image_path), post="json"))
        do_one(json_path, image_path)
        

def main():
    """
    主入口函数
    """
    print("烟叶圆度计算独立部署程序")
    print("=" * 50)

    # 配置路径 (使用计划中指定的测试数据源)
    json_dir = "coderafactor/yanye_user_feature/merged_results"
    image_dir = "coderafactor/test_images"
    output_dir = "coderafactor/yuandu_halcon/output"

    print(f"JSON目录: {json_dir}")
    print(f"图像目录: {image_dir}")
    print(f"输出目录: {output_dir}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试单个文件
    test_image = "hn-cz-2023-C2F-C21-10.bmp"
    test_json = "hn-cz-2023-C2F-C21-10.json"

    image_path = os.path.join(image_dir, test_image)
    json_path = os.path.join(json_dir, test_json)

    if os.path.exists(image_path) and os.path.exists(json_path):
        print(f"\n开始处理测试文件: {test_image}")

        # 复制JSON文件到输出目录
        output_json_path = os.path.join(output_dir, test_json)
        import shutil
        shutil.copy2(json_path, output_json_path)

        try:
            # 调用圆度计算函数
            do_one(output_json_path, image_path)
            print(f"✅ 处理成功: {test_image}")
        except Exception as e:
            print(f"❌ 处理失败: {test_image}, 错误: {e}")
    else:
        print(f"测试文件不存在: {image_path} 或 {json_path}")

    print("\n处理完成!")


def batch_process():
    """批量处理函数"""
    print("烟叶圆度计算独立部署程序 - 批量处理")
    print("=" * 50)

    # 配置路径
    json_dir = "coderafactor/yanye_user_feature/merged_results"
    image_dir = "coderafactor/test_images"
    output_dir = "coderafactor/yuandu_halcon/output"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有图像文件
    if not os.path.exists(image_dir):
        print(f"图像目录不存在: {image_dir}")
        return

    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png'))]

    if not image_files:
        print(f"在目录 {image_dir} 中未找到图像文件")
        return

    print(f"找到 {len(image_files)} 个图像文件")

    success_count = 0
    start_time = time.time()

    for i, image_file in enumerate(image_files[:10], 1):  # 只处理前10个文件
        json_file = image_file.replace('.bmp', '.json')
        image_path = os.path.join(image_dir, image_file)
        json_path = os.path.join(json_dir, json_file)

        print(f"\n[{i}/10] 处理: {image_file}")

        if not os.path.exists(json_path):
            print(f"❌ JSON文件不存在: {json_path}")
            continue

        # 复制JSON文件到输出目录
        output_json_path = os.path.join(output_dir, json_file)
        import shutil
        shutil.copy2(json_path, output_json_path)

        try:
            # 调用圆度计算函数
            do_one(output_json_path, image_path)
            success_count += 1
            print(f"✅ 处理成功")
        except Exception as e:
            print(f"❌ 处理失败: {e}")

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n批量处理完成!")
    print(f"总文件数: 10")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {10 - success_count}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均耗时: {total_time/10:.2f}秒/文件")


if __name__ == "__main__":
    main()
