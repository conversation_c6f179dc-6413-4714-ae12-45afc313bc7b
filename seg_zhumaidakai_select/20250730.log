20250730084547_102
error image = test_images/hn-cz-2023-C2F-C21-3.bmp error json=seg_zhumaidakai_select/output/hn-cz-2023-C2F-C21-3.json20250730084547_103
traceback.format_exc():
Traceback (most recent call last):
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1581, in zhumai_zhimai_do_one
    mid_dict, img_cv2_gray, edge_tailor_arr = made_mid(path, midpath, midjsonpath, True,
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1343, in made_mid
    edge_tailor_arr, canques_list, pcamean_list = get_edge_tailor(path,
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1265, in get_edge_tailor
    edge, canques_list,center_point_list = process(None,image,flag_show=flag_show_edge_tailor)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 853, in process
    right_down_intercept_indexs, up_intercept_indexs, down_intercept_indexs, pcamean_list, cntr = get_intercept(edge_points, img_cv2=img_cv2)
                                                                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 748, in get_intercept
    main_orient_line_dict,pcamean_list, cntr = get_main_orient_line2(fill_points,
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 468, in get_main_orient_line2
    main_eig_index = find_max_index(np.reshape(eigenvalues, (-1)))  #
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 422, in find_max_index
    max_v = np.float('-inf')
            ^^^^^^^^
  File "/home/<USER>/anaconda3/envs/vllm/lib/python3.12/site-packages/numpy/__init__.py", line 397, in __getattr__
    raise AttributeError(__former_attrs__[attr], name=None)
AttributeError: module 'numpy' has no attribute 'float'.
`np.float` was a deprecated alias for the builtin `float`. To avoid this error in existing code, use `float` by itself. Doing this will not modify any behavior and is safe. If you specifically wanted the numpy scalar type, use `np.float64` here.
The aliases was originally deprecated in NumPy 1.20; for more details and guidance see the original release note at:
    https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations
20250730084740_122
error image = test_data/vis/hn-cz-2023-C2F-C21-3.bmp error json=seg_zhumaidakai_select/output/hn-cz-2023-C2F-C21-3.json20250730084740_123
traceback.format_exc():
Traceback (most recent call last):
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1581, in zhumai_zhimai_do_one
    mid_dict, img_cv2_gray, edge_tailor_arr = made_mid(path, midpath, midjsonpath, True,
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1343, in made_mid
    edge_tailor_arr, canques_list, pcamean_list = get_edge_tailor(path,
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1265, in get_edge_tailor
    edge, canques_list,center_point_list = process(None,image,flag_show=flag_show_edge_tailor)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 853, in process
    right_down_intercept_indexs, up_intercept_indexs, down_intercept_indexs, pcamean_list, cntr = get_intercept(edge_points, img_cv2=img_cv2)
                                                                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 748, in get_intercept
    main_orient_line_dict,pcamean_list, cntr = get_main_orient_line2(fill_points,
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 468, in get_main_orient_line2
    main_eig_index = find_max_index(np.reshape(eigenvalues, (-1)))  #
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 422, in find_max_index
    max_v = np.float('-inf')
            ^^^^^^^^
  File "/home/<USER>/anaconda3/envs/vllm/lib/python3.12/site-packages/numpy/__init__.py", line 397, in __getattr__
    raise AttributeError(__former_attrs__[attr], name=None)
AttributeError: module 'numpy' has no attribute 'float'.
`np.float` was a deprecated alias for the builtin `float`. To avoid this error in existing code, use `float` by itself. Doing this will not modify any behavior and is safe. If you specifically wanted the numpy scalar type, use `np.float64` here.
The aliases was originally deprecated in NumPy 1.20; for more details and guidance see the original release note at:
    https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations
20250730084915_212
error image = /home/<USER>/xm/code/coderafactor/test_data/vis/hn-cz-2023-C2F-C21-3.bmp error json=/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/output/hn-cz-2023-C2F-C21-3.json20250730084915_214
traceback.format_exc():
Traceback (most recent call last):
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1581, in zhumai_zhimai_do_one
    mid_dict, img_cv2_gray, edge_tailor_arr = made_mid(path, midpath, midjsonpath, True,
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1343, in made_mid
    edge_tailor_arr, canques_list, pcamean_list = get_edge_tailor(path,
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 1265, in get_edge_tailor
    edge, canques_list,center_point_list = process(None,image,flag_show=flag_show_edge_tailor)
                                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 853, in process
    right_down_intercept_indexs, up_intercept_indexs, down_intercept_indexs, pcamean_list, cntr = get_intercept(edge_points, img_cv2=img_cv2)
                                                                                                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 748, in get_intercept
    main_orient_line_dict,pcamean_list, cntr = get_main_orient_line2(fill_points,
                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 468, in get_main_orient_line2
    main_eig_index = find_max_index(np.reshape(eigenvalues, (-1)))  #
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/deploy_seg_zhumaidakai_select.py", line 422, in find_max_index
    max_v = np.float('-inf')
            ^^^^^^^^
  File "/home/<USER>/anaconda3/envs/vllm/lib/python3.12/site-packages/numpy/__init__.py", line 397, in __getattr__
    raise AttributeError(__former_attrs__[attr], name=None)
AttributeError: module 'numpy' has no attribute 'float'.
`np.float` was a deprecated alias for the builtin `float`. To avoid this error in existing code, use `float` by itself. Doing this will not modify any behavior and is safe. If you specifically wanted the numpy scalar type, use `np.float64` here.
The aliases was originally deprecated in NumPy 1.20; for more details and guidance see the original release note at:
    https://numpy.org/devdocs/release/1.20.0-notes.html#deprecations
