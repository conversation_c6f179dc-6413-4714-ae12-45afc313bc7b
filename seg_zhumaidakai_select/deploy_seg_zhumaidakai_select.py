#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
烟叶主脉打开选择独立部署程序
整合三个核心功能：主脉选择、标签清理、特征计算

作者: Augment Agent
日期: 2025-06-23
版本: 1.0
基于: xu/UNet/ResUNet/Utils/zhumai_select.py
     wt/yanyefenji_data_test/delete_out_labels.py
     jiang/Tensorflow/datadetect/test9_common/test_zhumai_zhimai_json.py
"""

import os
import sys
import json
import math
import cv2
import numpy as np
from itertools import combinations
import traceback
import time
from pathlib import Path

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

def json_writer(points, src_file, template):

    # with open(src_file, 'r') as f:
    #     template = json.load(f)
    temp = []
    for e in points:
        temp.append({
            "label": "zhumaidakai",
            "points": e,
            "group_id": None,
            "shape_type": "polygon",
            "flags": {}})
    template[f'shapes'].extend(temp)
    bf.save_json_dict_with_lock(template, src_file)
    # with open(src_file, 'w') as f:
    #     json.dump(template, f, indent=4)


def jsonloader(src_file):

    dakai_point = []
    zoushi_point = []

    with open(src_file, 'r') as f:
        content = json.load(f)
        # if len(content['shapes'])>0:
        for n in range(len(content['shapes'])-1, -1, -1):
            if content['shapes'][n]["label"] == "zhumaizoushi":
                zoushi_point.append(content['shapes'][n]['points'])

            if content['shapes'][n]["label"] == "zhumaidakai":
                dakai_point.append(content['shapes'][n]['points'])
                content['shapes'].remove(content['shapes'][n])

    return dakai_point, zoushi_point, content

def ray_tracing_method(point,poly):
    x, y = point[0], point[1]

    n = len(poly)
    inside = False

    p1x, p1y = poly[0]
    for i in range(n+1):
        p2x, p2y = poly[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x < max(p1x, p2x):
                    if p1y != p2y:
                        xints = (y-p1y)*(p2x-p1x)/(p2y-p1y)+p1x
                    if p1x == p2x or x <= xints:
                        inside = not inside
        p1x ,p1y = p2x, p2y
    return inside

def L2_dist(m, n):
    '''
    Args:
        m:列表，x点坐标
        n:列表，y点坐标

    Returns: L2距离
    '''
    return ((m[0] + 1 - n[0]) ** 2 + (m[1] + 1 - n[1]) ** 2) ** 0.5

def list_select(arr, threshold, axis=-1):
    size = len(arr)
    index = list(range(size))
    position_pre = [p[1] for p in arr]
    for n in range(size-1, -1 ,-1):
        if arr[n][axis] <= threshold:
            arr.pop(n)
            index.pop(n)
    position = []
    temp = []
    global one_dakai
    one_dakai = []
    for n in range(len(index)-1):
        if index[n+1] - index[n] == 1:
            temp.append(position_pre[index[n]])
            if n == len(index)-2:
                temp.append(position_pre[index[n]])
                one_dakai = [[i[0][0]/5028, i[0][1]/2280] for i in temp]
                one_dakai.extend([temp[n][1][0]/5028, temp[n][1][1]/2280] for n in range(len(temp)-1,-1,-1))
                position.append(one_dakai)
                # temp = []
                # one_dakai = []
        elif index[n+1] - index[n] > 1:
            temp.append(position_pre[index[n]])
            one_dakai = [[i[0][0] / 5028, i[0][1] / 2280] for i in temp]
            one_dakai.extend([temp[n][1][0]/5028, temp[n][1][1]/2280] for n in range(len(temp)-1,-1,-1))
            position.append(one_dakai)
            temp = []
            one_dakai = []
    return position

def width_cal(points, polygon):
    global width, temp
    width = []
    temp = []
    points.insert(0, [points[0][0]-1,points[0][1]])
    points.insert(-1, [points[-1][0]+1,points[-1][1]])
    for m in range(1,len(points)-1):
        x1 ,y1 = points[m]
        k = (points[m+1][1] - points[m-1][1])/(points[m+1][0] - points[m-1][0])
        if k != 0:
            k = -1/k
            b = y1 - k*x1
            x2, y2 = x1+1, k*(x1+1)+b
        elif k == 0:
            x2, y2 = x1, y1+1
        pnt_cross_list = bf.find_polygonlineIntersection(polygon, x1, y1, x2, y2)
        max = 0
        from itertools import combinations
        iter_list = list(combinations(pnt_cross_list, 2))
        iter_point = None
        for iter_item in iter_list:
            dist = L2_dist(iter_item[0], iter_item[1])
            if dist > max:
                max = dist
                iter_point = [iter_item[0], iter_item[1]]
                iter_point, _ = bf.point_sort_order(iter_point, flag_vertival=True)
        if iter_point is not None:
            temp.append([points[m], iter_point, int(max)])
    x = [point[1][0][0] for point in temp]
    order = np.argsort(x, axis=0).tolist()

    for i in order:
        width.append(temp[i])
    return width

def zhumai_one(src_file, threshold):
    dakai_points, zoushi_points, content = jsonloader(src_file)
    file = src_file.split('/')[-1]
    print(file)
    # global contour
    contour = []
    if len(dakai_points) != 0:
        temp = zoushi_points.copy()
        zoushi_point = []
        for pnt in temp:
            zoushi_point.extend(pnt)
        for dakai_pnt in dakai_points:
            zoushi_in_dakai = []
            for zoushi_pnt in zoushi_point:
                if ray_tracing_method(zoushi_pnt, dakai_pnt):
                    zoushi_in_dakai.append(zoushi_pnt)
            zoushi_in_dakai = [[p[0] * 5028, p[1] * 2280] for p in zoushi_in_dakai]
            dakai_pnt = [[p[0] * 5028, p[1] * 2280] for p in dakai_pnt]
            if len(zoushi_in_dakai) != 0:
                width = width_cal(zoushi_in_dakai, dakai_pnt)
                point_set = list_select(width, threshold)
                for point in point_set:
                    if len(point) > 4:
                        contour.append(point)
    json_writer(contour, src_file, content)


def delete_out_labels(img_path, json_path, not_delete_label_names, lunkuo_shapes_name="shapes", lunkuo_name="canque_fill", shapes_name="shapes"):
    img = cv2.imread(img_path)
    height, width, _ = bf.cv2_size(img)
    img2 = np.zeros([height, width, 3], np.uint8)
    img2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    img2 = np.where(img2 < 100, 0, 255).astype(np.uint8)
    json_data = bf.load_json_dict_orig(json_path, encoding="utf-8-sig")
    shapes = json_data[lunkuo_shapes_name]
    lunkuo = None
    for shape in shapes:
        if shape["label"] == lunkuo_name:
            lunkuo = [[x[0] * width, x[1] * height] for x in shape["points"]]
    if lunkuo is None:
        print("没找到轮廓 {}".format(img_path))
    else:
        cv2.polylines(img2, np.array([lunkuo], np.int32), True, (255, 255, 255), 1)
        img2 = np.where(img2 < 100, 0, 255).astype(np.uint8)
        conts, _ = cv2.findContours(img2, mode=cv2.RETR_EXTERNAL, method=cv2.CHAIN_APPROX_SIMPLE)
        json_data_tmp = json_data.copy()
        for k, v in json_data.items():
            if shapes_name is None:
                if not k.__contains__("shapes"):
                    continue
            else:
                if not k == shapes_name:
                    continue
            shapes = []
            for shape in v:
                if not shape["label"] in not_delete_label_names:
                    # print('del ={}'.format(shape["label"]))
                    if shape["shape_type"] == "rectangle":
                        points = shape["points"]
                        p0 = points[0]
                        p1 = points[1]
                        p0 = [p0[0] * width, p0[1] * height]
                        p1 = [p1[0] * width, p1[1] * height]
                        if cv2.pointPolygonTest(conts[0], tuple(p0), False) == 1 and cv2.pointPolygonTest(conts[0], tuple(p1), False) == 1:  # 若点在轮廓内
                            shapes.append(shape)
                        # center = bf.get_rect_center_point_labelme(shape["points"])
                        # center = [center[0] * width, center[1] * height]
                        # if cv2.pointPolygonTest(conts[0], tuple(center), False) == 1:  # 若点在轮廓内
                        #     shapes.append(shape)
                    elif shape["shape_type"] == "circle":
                        center = shape["points"][0]
                        center = [center[0] * width, center[1] * height]
                        if cv2.pointPolygonTest(conts[0], tuple(center), False) == 1:  # 若点在轮廓内
                            shapes.append(shape)
                    elif shape["shape_type"] == "polygon" or shape["shape_type"] == "linestrip":
                        points = []
                        for point in shape["points"]:
                            if cv2.pointPolygonTest(conts[0], (point[0] * width, point[1] * height), False) >= 0:
                                points.append(point)
                        if len(points) > 3:
                            shape["points"] = points
                            shapes.append(shape)
                    else:
                        shapes.append(shape)
                else:
                    shapes.append(shape)
            json_data_tmp[k] = shapes
        bf.save_json_dict_orig(json_data_tmp, json_path)

def delete_do_one(i,bmp_files,json_root,not_delete_label_names):
    json_path = bf.pathjoin(json_root, bf.rename_add_post(bf.get_file_name(bmp_files[i]), post="json"))
    delete_out_labels(bmp_files[i], json_path, not_delete_label_names, lunkuo_shapes_name="shapes",
                      lunkuo_name="canque_fill",
                      shapes_name="shapes")

def proc_file(filepath,proc_type="grad"):
    # 形态学：边缘检测
    import cv2
    print('filepath={}'.format(filepath))
    original_img = bf.cv2_read_file(filepath)
    if proc_type=="grad":
        original_img = bf.cv2_GaussianBlur(original_img,gau_core=(7, 7))
        gray_img = bf.cv2_gray(original_img)
        _ ,Thr_img = bf.cv2_thd(gray_img ,240 ,255 ,cv2.THRESH_TOZERO_INV  )  # 设定红色通道阈值210（阈值影响梯度运算效果）
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT ,(5 ,5))  # 定义矩形结构元素
        ret = cv2.morphologyEx(Thr_img, cv2.MORPH_GRADIENT, kernel)  # 梯度
        ret = bf.cv2_GaussianBlur(ret, gau_core=(7, 7))
    else:
        original_img = bf.cv2_GaussianBlur(original_img,gau_core=(17, 17))
        gray_img = bf.cv2_gray(original_img)
        _ ,ret = bf.cv2_thd(gray_img ,60 ,255 ,cv2.THRESH_TOZERO_INV )  # 设定红色通道阈值210（阈值影响梯度运算效果）
        # bf.show_cv2_img(ret,name="Thr_img",waitms=0,flag_resize_adapt=True)
    return ret

def cal_binary_image2(image, red_filter=False):
    """
    :param image: 图片
    :param red_filter: 图片中红色部分为干扰当作背景
    :return: 二值图片 0-背景 255-目标
    黑色部分 h=0~180 s=0~255 v=0~46
    """
    v_black = [52,255]#[70,255]#[0,46]#改成52是因为264.png在50的时候由于补光太亮会导致边缘提取的轮廓太大
    # bf.show_cv2_img(image,waitms=0)
    image = cv2.GaussianBlur(image, (11, 11), 0)  # 高斯模糊去噪
    img_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)  # 转入HSV空间
    H, S, V = cv2.split(img_hsv)  # 拆分
    # ret, thresh1 = cv.threshold(V, v_black[0], v_black[1], cv.THRESH_BINARY | cv.THRESH_OTSU)  # 最大律法2值化
    ret, thresh1 = bf.cv2_thd(V, v_black[0], v_black[1])
    return thresh1

def get_border_number(contours):
    """
    :param contours: 边界信息
    :return: 各个边界的点数
    """
    border_n = []
    if contours is not None:
        for border in contours:
            border_n.append(len(border))
    return border_n

def extract_edge_canque(numbers, limit=10):
    """
    :param numbers: 边界点数列表
    :return: 边缘位置和残缺位置
    """
    max_n = float('-inf')
    max_index = -1
    canque_indexs = []
    for i in range(len(numbers)):
        n = numbers[i]
        if n > limit:
            if n > max_n:
                if max_index >= 0:
                    canque_indexs.append(max_index)
                max_n = n
                max_index = i
            else:
                canque_indexs.append(i)
    return max_index, canque_indexs

def get_edge_canques(binary,flag_show=True):
    """
    :param binary: 0-背景  255-目标  的二值图片
    :return: 边缘和残缺
    """
    contours, hierarchy = cv2.findContours(binary, cv2.RETR_CCOMP, cv2.CHAIN_APPROX_SIMPLE)  # 获取边界信息
    if flag_show:
        img = bf.cv2_gray_to_color(binary)
        cv2.drawContours(img, contours, -1, (0, 0, 255), 3)
        cv2.imshow("contours_img", img)
        cv2.waitKey(0)
    # print('debug0002 ------contours++++={}'.format(np.sum(contours)))
    numbers = get_border_number(contours)
    edge_index, canque_indexs = extract_edge_canque(numbers)
    edge = contours[edge_index]
    canques_list = [contours[i] for i in canque_indexs]
    return edge, canques_list

def find_jixie_damege(values, max_index):
    avg_step = 20
    avg_values = bf.moving_avg(values,step_amt=avg_step)
    # print('left len(avg_values)={}'.format(len(avg_values)))
    diff_values = bf.diff_list(avg_values,step_amt=2)
    for idx in range(max_index,len(avg_values)):
        print('leftd={}={}'.format(values[idx],diff_values[idx]))

def cal_point_distance(p1, p2):
    """
    :param p1: 一点
    :param p2: 另一点
    :return: p1p2的距离
    """
    x1, y1 = p1
    x2, y2 = p2
    return ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5

def get_distance_point2(start_point, end_point, vert_dis, line_dis):
    """
    :param start_point: 起点
    :param end_point: 终点
    :param vert_dis: 距离
    :param line_dis: 线距
    :return: 起止线段上距离起点为目标距离的点
    """
    x1, y1 = start_point
    x2, y2 = end_point
    x = x1 + (x2 - x1) * vert_dis / line_dis
    y = y1 + (y2 - y1) * vert_dis / line_dis
    return x, y

def get_split_points(line, gap=1):
    """
    :param line: 直线
    :param gap: 间隔
    :return: 从起点到终点按间隔取断点
    """
    split_points = []
    start_point, end_point = line
    line_d = cal_point_distance(start_point, end_point)
    n = line_d // gap
    for i in range(int(n)):
        inter_d = gap * i
        p = get_distance_point2(start_point, end_point, inter_d, line_d)
        split_points.append(p)
    split_points.append(end_point)
    return split_points

def get_line_points(points, interval):
    """
    :param points: 连续点集
    :param interval: 补点的间隔
    :return: 两点距离太远中间补点后的点集(否则求主方向出错)
    """
    fill_points = []
    for i in range(len(points)):
        cur_p = points[i]
        if i == len(points) - 1:
            next_p = points[0]
        else:
            next_p = points[i + 1]
        split_points = get_split_points([cur_p, next_p], gap=interval)
        split_points.pop()
        fill_points.extend(split_points)
    return fill_points


def find_max_index(eigenvalues):
    """
    :param eigenvalues: 特征值列表
    :return: 最大特征值索引
    """
    max_index = -1
    max_v = float('-inf')
    for i in range(len(eigenvalues)):
        v = eigenvalues[i]
        if v > max_v:
            max_v = v
            max_index = i
    return max_index



def get_main_orient_line2(points,img_cv2=None):#计算主方向线时不能假设烟叶是直线，且方向只有一个，烟叶也有可能是弯的，对于弯的情况就分成几段逐段的计算方向
    """
    :param points: 连续点集
    :return: 主方向线
    """
    #计算点在x周方向上的最左和最右宽度，把宽度方向切成4段，然后逐渐计算0+1段、1+2段、2+3段的主方向
    main_orient_line_dict = {"left":[],"right":[],"center":[]}
    points_arr = np.array(points)
    x_arr = points_arr[:,0]
    x_max = np.max(x_arr)
    x_min = np.min(x_arr)
    # print('x_min={}'.format(x_min))
    # print('x_max={}'.format(x_max))
    step_amt = 10
    interval_amt = 5
    x_step = (x_max-x_min)/step_amt
    x_split_list = []
    for i in range(step_amt):
        x_split_list.append(x_min+x_step*i)
    x_split_list.append(x_max)
    # print('x_split_list={}'.format(x_split_list))
    direction_line_list = []#表示方向的线段的列表[[cntr, p1]]
    pcamean_list = []
    cntr = []
    center_point_list=[]
    for i in range(step_amt-interval_amt+1):
        x_lower = x_split_list[i]
        x_upper = x_split_list[i+interval_amt]
        # print('x_lower={}'.format(x_lower))
        # print('x_upper={}'.format(x_upper))
        points_arr_step = bf.ndarray_where(points_arr,type="interval",val1=x_lower,val2=x_upper,dim_id=0)
        # idx = np.where((x_arr >= x_lower) & (x_arr <= x_upper))
        # print('idx={}'.format(idx))
        # points_arr_step = points_arr[idx]
        mean, eigenvectors, eigenvalues = cv2.PCACompute2(points_arr_step.astype(float), np.array([]))
        pcamean_list.append(bf.get_center_point_list_pca(mean))
        main_eig_index = find_max_index(np.reshape(eigenvalues, (-1)))  #
        main_eig = eigenvectors[main_eig_index]
        center_point = bf.get_center_point2_pca(mean)
        main_eig_point = (center_point[0] + main_eig[0], center_point[1] + main_eig[1])

        pca_ret_list = [mean, eigenvectors, eigenvalues]
        center_point_list.append(center_point)
        # if i==0:
        #     main_orient_line_dict["left"]=[center_point, main_eig_point]
        # elif i==step_amt-interval_amt:
        #     main_orient_line_dict["right"] = [center_point, main_eig_point]
        # drawAxis(image, cntr, p1, (0, 255, 0), 1)  # 绿色，较长轴
        bf.show_main_orient(pca_ret_list, img_cv2)
        mean = pca_ret_list[0]
        c = (int(mean[0, 0]), int(mean[0, 1]))
        cntr.append(c)

    main_orient_line_dict["left"] = [center_point_list[0],center_point_list[1]]
    main_orient_line_dict["right"] = [center_point_list[-2],center_point_list[-1]]
    # print(cntr)
    # 获取points的协方差矩阵特征值与特征向量
    mean, eigenvectors, eigenvalues = cv2.PCACompute2(points_arr.astype(float), np.array([]))
    pca_ret_list = [mean, eigenvectors, eigenvalues]
    bf.show_main_orient(pca_ret_list, img_cv2)
    main_eig_index = find_max_index(np.reshape(eigenvalues, (-1)))  #
    main_eig = eigenvectors[main_eig_index]
    center_point = bf.get_center_point2_pca(mean)
    main_eig_point = (center_point[0] + main_eig[0], center_point[1] + main_eig[1])
    main_orient_line_dict["center"] = [center_point, main_eig_point]

    return main_orient_line_dict, pcamean_list, cntr


def get_line_para(p1, p2):
    """
    :param p1: 直线上一点
    :param p2: 直线上另一点
    :return: 直线的参数ax+by+c=0
    """
    x1, y1 = p1
    x2, y2 = p2
    a = y1 - y2
    b = x2 - x1
    c = x1 * y2 - x2 * y1
    return a, b, c

def get_vertical_cross_point(line, orient_point):
    """
    :param line: 直线
    :param orient_point: 方向点
    :return: 方向点向直线作垂线与直线的交点
    """
    p1, p2 = line
    A, B, C = get_line_para(p1, p2)
    x0, y0 = orient_point
    vert_point_x = (B * B * x0 - A * B * y0 - A * C) / (A * A + B * B)
    vert_point_y = (-A * B * x0 + A * A * y0 - B * C) / (A * A + B * B)
    return vert_point_x, vert_point_y

def cal_point_orient_distance(start_point, end_point, point):
    """
    :param start_point: 起点
    :param end_point: 终点
    :param point: 目标点
    :return: 目标点到起点的距离 s > e方向为正 否则负
    """
    x1 = start_point[0]
    x2 = end_point[0]
    x = point[0]
    d = cal_point_distance(start_point, point)
    if (x - x1) * (x2 - x1) < 0:
        d = -d
    return d

def get_main_orient_line_range(main_orient_line, points):
    """
    :param main_orient_line: 主方向线
    :param points: 连续点集
    :return: 主方向线（连续点集与主方向线交点在该两点之间）
    """
    min_v = float("inf")
    max_v = float("-inf")
    min_p = None
    max_p = None
    for i in range(len(points)):
        point = points[i]
        cross_point = get_vertical_cross_point(main_orient_line, point)
        d = cal_point_orient_distance(main_orient_line[0], main_orient_line[1], cross_point)
        if d > max_v:
            max_v = d
            max_p = cross_point
        if d < min_v:
            min_v = d
            min_p = cross_point
    return [min_p, max_p]

def get_main_orient_line_orient(main_orient_line):
    """
    :param main_orient_line: 主方向线
    :return: 主方向线 x小的为起点
    """
    start_point, end_point = main_orient_line
    if start_point[0] > end_point[0]:
        return [end_point, start_point]
    return main_orient_line

def get_vertical_line(line, select_point):
    """
    :param line: 直线
    :param select_point: 线上一点
    :return: 过选择点的垂线
    """
    p1, p2 = line
    x1, y1 = p1
    x2, y2 = p2
    x, y = select_point
    if x1 == x2:
        next_point = (x + 10, y)
    elif y1 == y2:
        next_point = (x, y + 10)
    else:
        x_result = x + 10
        #  (x2 - x1) * 10 + (y2 - y1) * (y_result - y) = 0
        y_result = (x2 - x1) * 10 / (y1 - y2) + y
        next_point = (x_result, y_result)
    return [select_point, next_point]

def get_cross_point(line1, line2):
    """
    :param line1: 线段1
    :param line2: 线段2
    :return: 线段的交点
    """
    p1, p2 = line1
    p3, p4 = line2
    a1, b1, c1 = get_line_para(p1, p2)
    a2, b2, c2 = get_line_para(p3, p4)
    d = a1 * b2 - a2 * b1
    if d == 0:
        return None
    x = (b1 * c2 - b2 * c1) / d
    y = (c1 * a2 - c2 * a1) / d
    return x, y

def if_inline(point1, point2, point):
    """
    :param point1: 直线上一点
    :param point2 直线上另一点
    :param point: 直线上待测点
    :return: false-待测点不在两点间 true-待测点在两点间
    """
    d = cal_point_distance(point1, point2)
    d1 = cal_point_distance(point1, point)
    if d1 > d:
        return False
    d2 = cal_point_distance(point2, point)
    if d2 > d:
        return False
    return True

def get_intercept_distance2(main_orient_points, points):
    """
    :param main_orient_points: 主方向点集
    :param points: 边缘点集
    :return: [截距,上半区截断位置索引，下半区截断位置索引，]
    """
    intercept_distance = []
    up_intercept_indexs = []
    down_intercept_indexs = []
    main_orient_start_p = main_orient_points[0]
    main_orient_end_p = main_orient_points[-1]
    for main_orient_point in main_orient_points:
        # print('main_orient_point={}'.format(main_orient_point))
        max_intercept_index = -1
        min_intercept_index = -1
        max_intercept_distance = float("-inf")
        min_intercept_distance = float("inf")
        point_list = []#计数器要等于2才可以计算截线长度
        point_id_list = []
        for i in range(len(points)):
            cur_point = points[i]
            if i == len(points) - 1:
                next_point = points[0]
            else:
                next_point = points[i + 1]
            vertical_line = get_vertical_line([main_orient_start_p, main_orient_end_p], main_orient_point)#求出当前main_orient_point在主方向上的垂线，用两个点表示
            cross_point = get_cross_point([cur_point, next_point], vertical_line)#求出理论交点
            if cross_point is not None and if_inline(cur_point, next_point, cross_point):#判断理论交点是否在两点之间
                # 存在交点
                point_list.append([cur_point, next_point])
                point_id_list.append(i)
                # if len(point_list)==2:
                #     break


        if len(point_list)>=2:
            if len(point_list)>2:#如果交点个数大于2个则取最远的距离
                max_i = 0
                max_j = 0

                pp0_arr = 0
                pp1_arr = 0
                d = 0
                for i in range(len(point_list)):
                    for j in range(i+1,len(point_list)):
                        pp0_arr_tmp = bf.listToArray(point_list[i])
                        p0_arr = (pp0_arr_tmp[0] + pp0_arr_tmp[1]) * 0.5
                        pp1_arr_tmp = bf.listToArray(point_list[j])
                        p1_arr = (pp1_arr_tmp[0] + pp1_arr_tmp[1]) * 0.5
                        d_tmp = cal_point_distance(p0_arr.tolist(), p1_arr.tolist())
                        if d_tmp>d:
                            d = d_tmp
                            pp0_arr = pp0_arr_tmp
                            pp1_arr = pp1_arr_tmp
                            max_i = i
                            max_j = j

                if p0_arr[1] > p1_arr[1]:
                    up_intercept_indexs.append(point_id_list[max_i])
                    down_intercept_indexs.append(point_id_list[max_j])
                else:
                    up_intercept_indexs.append(point_id_list[max_j])
                    down_intercept_indexs.append(point_id_list[max_i])
            else:
                #计算两组点的中间点的距离
                pp0_arr = bf.listToArray(point_list[0])
                p0_arr = (pp0_arr[0]+pp0_arr[1])*0.5
                pp1_arr = bf.listToArray(point_list[1])
                p1_arr = (pp1_arr[0]+pp1_arr[1])*0.5
                d = cal_point_distance(p0_arr.tolist(), p1_arr.tolist())
                if p0_arr[1]>p1_arr[1]:
                    up_intercept_indexs.append(point_id_list[0])
                    down_intercept_indexs.append(point_id_list[1])
                else:
                    up_intercept_indexs.append(point_id_list[1])
                    down_intercept_indexs.append(point_id_list[0])

            # print('d={}'.format(d))
            intercept_distance.append(d)
            # if p0_arr[1]>p1_arr[1]:
            #     up_intercept_indexs.append(point_id_list[0])
            #     down_intercept_indexs.append(point_id_list[1])
            # else:
            #     up_intercept_indexs.append(point_id_list[1])
            #     down_intercept_indexs.append(point_id_list[0])



            # #
            # orient = geometry.get_point_orient_line([main_orient_start_p, main_orient_end_p], cross_point)
            # d *= orient
        #     if d > max_intercept_distance:
        #         max_intercept_distance = d
        #         max_intercept_index = i
        #     if d < min_intercept_distance:
        #         min_intercept_distance = d
        #         min_intercept_index = i
        #
        # if max_intercept_index > 0 and min_intercept_index > 0 and max_intercept_index != min_intercept_index:
        #     cur_intercept_distance = abs(min_intercept_distance) + abs(max_intercept_distance)
        #     intercept_distance.append(cur_intercept_distance)
        #     up_intercept_indexs.append(max_intercept_index)
        #     down_intercept_indexs.append(min_intercept_index)

            # point1 = points[max_intercept_index]
            # point2 = points[min_intercept_index]
            # import matplotlib.pyplot as plt
            # plt.figure()
            # ax = plt.gca()
            # ax.set_aspect(1)
            # plt.plot([main_orient_start_p[0], main_orient_end_p[0]], [main_orient_start_p[1], main_orient_end_p[1]])
            # plt.plot([one[0] for one in points], [one[1] for one in points], c='black')
            # plt.plot([point1[0], point2[0]], [point1[1], point2[1]], c='red')
            # plt.show()
            # print()
    return intercept_distance, up_intercept_indexs, down_intercept_indexs

def get_intercept(points, interval=2,img_cv2=None):
    fill_points = get_line_points(points, 1)
    # print('fill_points={}'.format(fill_points))
    main_orient_line_dict,pcamean_list, cntr = get_main_orient_line2(fill_points,
                                                               img_cv2,#显示pca结果时使用
                                                               )  # 主方向线
    #获取最宽截面
    main_orient_line = main_orient_line_dict["center"]
    main_orient_line = get_main_orient_line_range(main_orient_line, points)  # 主方向线长度缩放
    main_orient_line = get_main_orient_line_orient(main_orient_line)  # 主方向线方向调整
    main_orient_points = get_split_points(main_orient_line, interval)  # 主方向线打断点

    # 获取截断距离及对应的上下点索引
    intercept_distance, up_intercept_indexs, down_intercept_indexs = get_intercept_distance2(main_orient_points, points)
    max_intercept_distance = max(intercept_distance)  # 烟叶高度
    # toal_intercept_distance = max_intercept_distance * k
    max_index = intercept_distance.index(max_intercept_distance)



    #获取左边结束截面
    left_orient_line = main_orient_line_dict["left"]
    left_orient_line = get_main_orient_line_range(left_orient_line, points)  # 主方向线长度缩放
    left_orient_line = get_main_orient_line_orient(left_orient_line)  # 主方向线方向调整
    left_orient_points = get_split_points(left_orient_line, interval)  # 主方向线打断点
    left_intercept_distance, left_up_intercept_indexs, left_down_intercept_indexs = get_intercept_distance2(left_orient_points, points)
    if img_cv2 is not None:#画所有的截线
        for i in range(0,max_index):
            pnt_up = points[left_up_intercept_indexs[i]]
            pnt_down = points[left_down_intercept_indexs[i]]
            bf.cv2_line(img_cv2,pnt_up,pnt_down)

    #获取右边边结束截面
    right_orient_line = main_orient_line_dict["right"]
    right_orient_line = get_main_orient_line_range(right_orient_line, points)  # 主方向线长度缩放
    right_orient_line = get_main_orient_line_orient(right_orient_line)  # 主方向线方向调整
    right_orient_points = get_split_points(right_orient_line, interval)  # 主方向线打断点
    right_intercept_distance, right_up_intercept_indexs, right_down_intercept_indexs = get_intercept_distance2(right_orient_points, points)
    if img_cv2 is not None:#画所有的截线
        for i in range(max_index,len(right_intercept_distance)):
            pnt_up = points[right_up_intercept_indexs[i]]
            pnt_down = points[right_down_intercept_indexs[i]]
            bf.cv2_line(img_cv2,pnt_up,pnt_down)
    return max_index,max_intercept_distance,right_intercept_distance,left_intercept_distance, left_up_intercept_indexs, \
        left_down_intercept_indexs,right_intercept_distance, right_up_intercept_indexs, \
        right_down_intercept_indexs,up_intercept_indexs,down_intercept_indexs,pcamean_list, cntr

def process(path,img_cv2=None, red_filter=False,flag_show=True,split_amt=10,two_side_pixel_amt=10,active_area_rate_lower=0.003):
    """
    :param path: 图片路径
    :param red_filter: 图片中红色部分为干扰当作背景
    :return: 图片边缘和中间的残缺
    """
    if path is None:
        image = img_cv2
    else:
        image = cv2.imread(path)  # 读取图片
    binary = cal_binary_image2(image, red_filter)  # 获取背景、目标二值图像
    binary = bf.cv2_erode(binary,iterations=0)
    # cv.Canny(binary)
    edge, canques_list = get_edge_canques(binary,flag_show)  # 提取边缘和残缺
    # cv.moments()
    height, width, color_size = bf.cv2_size(binary)
    interval_width = int(width / split_amt)
    background_width = interval_width+2*two_side_pixel_amt
    img_background = bf.cv2_create_mask(height,background_width)
    #


    #把图片从右到左切成几块，计算区域中心点
    # bf.show_cv2_img(binary_crop, "binary_crop", flag_resize_adapt=False)
    # bf.cv2_size(binary_crop,print_flag=True,addname="binary_crop")
    center_point_list = []
    split_area = interval_width*height
    for i in range(split_amt):

        left = i*interval_width
        top = 0
        binary_crop = bf.cv2_crop(binary, left, top, interval_width, height)
        active_area = bf.arr_sum(binary_crop)/255
        active_area_rate = active_area/split_area
        if active_area_rate>active_area_rate_lower:
            # print('active_area_rate={}'.format(active_area_rate))
            img_background_tmp = img_background.copy()
            # print('={}'.format())
            # bf.cv2_size(img_background_tmp, print_flag=True, addname="img_background_tmp")
            # bf.cv2_size(img_background_tmp[0:height,10:interval_width+10], print_flag=True, addname="binary_crop_dst")
            bf.cv2_paste_img_left_top(binary_crop,img_background_tmp,two_side_pixel_amt,top)
            # img_background_tmp[0:height,10:interval_width+10] = binary_crop
            # bf.show_cv2_img(img_background_tmp, "crop_pic", flag_resize_adapt=False)
            cx_tmp, cy_tmp = bf.cv2_img_center_moments(img_background_tmp)
            cx, cy = cx_tmp+left-two_side_pixel_amt, cy_tmp+top
            center_point_list.append([cx,cy])
            # print('cx={}'.format(cx))
            # print('cy={}'.format(cy))
            if img_cv2 is not None:
                bf.cv2_circle(image,cx,cy)

    # bf.show_cv2_img(image, "center_point", flag_resize_adapt=False)
    center_point_list.pop(0)


    #检测轮廓外的机械损伤
    ##获得截线
    edge_points = np.reshape(edge, (-1, 2))
    edge_points = edge_points.tolist()
    max_index, max_intercept_distance, right_intercept_distance, left_intercept_distance, left_up_intercept_indexs, \
        left_down_intercept_indexs, right_intercept_distance, right_up_intercept_indexs, \
        right_down_intercept_indexs, up_intercept_indexs, down_intercept_indexs, pcamean_list, cntr = get_intercept(edge_points, img_cv2=img_cv2)
    ##
    find_jixie_damege(left_intercept_distance, max_index)

    return edge, canques_list, center_point_list

def find_left_index2(values, toal_value, max_index, k=0.2):
    """
    :param values: 特征值列表
    :param toal_value: 目标特征值
    :param max_index: 最大特征值索引
    :param k: 找不到满足添加的索引则取默认索引
    :return: 左侧目标特征值离最大值最近出现的索引
    """
    avg_step = 20
    avg_values = bf.moving_avg(values,step_amt=avg_step)
    print('left len(avg_values)={}'.format(len(avg_values)))
    for d in avg_values[:max_index]:
        print('leftd={}'.format(d))
    diff_values = bf.diff_list(avg_values,step_amt=2)
    for d in diff_values[:max_index]:
        print('leftdiff={}'.format(d))
    #规则1:从由向左探查avg_values，如果发现斜率斜率为0的邻近两个点，低左边有高，且低与高的差距大于30个像素点以上且低的值小于80个像素点则认为低点为左侧结束点
    print('规则1:从由向左探查avg_values，如果发现斜率斜率为0的邻近两个点，低左边有高，且低与高的差距大于30个像素点以上且低的值小于80个像素点则认为低点为左侧结束点')
    idx_list = bf.list_where_idx(diff_values[:int(max_index*0.5)],wheretype="crosszero")
    idx_arr = bf.listToArray(idx_list)
    if len(idx_arr)>=2:
        print('idx_arr={}'.format(idx_arr))
        idx_arr += int(avg_step*0.7)
        for i in range(0,len(idx_arr)-1):
            if values[idx_arr[i]]-values[idx_arr[i+1]]>30 and values[idx_arr[i+1]]<80:
                print('返回规则1')
                return idx_arr[i+1]
    #规则2:从右往左在叶基部如果发现，30个斜率中有20个小于±0.5，且宽度小于40则认为
    print('规则2:从右往左在叶基部如果发现，30个斜率中有20个小于±0.5，且宽度小于40则认为')
    scan_interval = 30
    for i in range(len(diff_values[:max_index]),scan_interval-1,-1):
        # print('i-30={} i={}'.format(i-30,i))
        #规则3：查看有没有小于toal_value
        idx_list3 = bf.list_where_idx(values[i - scan_interval:i], wheretype="less", val=toal_value)
        if len(bf.list_where_idx(avg_values[i-scan_interval:i],wheretype="less",val=40))==scan_interval:
            if len(bf.list_where_idx(diff_values[i-scan_interval:i],wheretype="less",val=0.5,flag_abs=True))>int(scan_interval*2/3):
                print('返回规则2')
                return i
        elif len(idx_list3):
            print('返回规则3')
            return i - scan_interval +idx_list3[-1]
        # avg_values[i-30,i]

    #规则4:探查10个值中最小的
    print('规则4:探查10个值中最小的')
    max_index = max_index if len(values)>max_index else len(values) - 1

    for i in range(9,max_index,1):
        all_small = True
        for j in range(i , i+10):
            v = values[j]
            # print('v={}'.format(v))
            # print('toal_value={}'.format(toal_value))
            if v > toal_value:
                all_small = False
                break
        if all_small is False:
            # print('i={}'.format(i))
            print('返回规则4')
            return i

    # 规则5:没找到连续10个小于目标值的，在头10个中找小于目标值的
    for i in range(min(10, max_index), -1, -1):
        v = values[i]
        if v < toal_value:
            print('返回规则5')
            return i

    return 0

def find_right_index2(values, toal_value, max_index_orig, k=0.2):
    """
    :param values: 特征值列表
    :param toal_value: 目标特征值
    :param max_index: 最大特征值索引
    :param k: 找不到满足添加的索引则取默认索引
    :return: 右侧目标特征值离最大值最近出现的索引
    """
    max_index =max_index_orig + int((len(values)-max_index_orig)*0.5)
    # print('len(values)={}'.format(len(values)))
    avg_values = bf.moving_avg(values,step_amt=10)
    # print('len(avg_values)={}'.format(len(avg_values)))
    diff_values = bf.diff_list(avg_values,step_amt=2)
    #规则1:如果发现宽度突变有小于-30的，则认为叶尖有残损，以突变处作为叶尖，截取
    print('规则1:如果发现宽度突变有小于-30的，则认为叶尖有残损，以突变处作为叶尖，截取')
    diff_values_part = diff_values[max_index:]
    print('len(diff_values_part)={}'.format(len(diff_values_part)))
    idx_list = bf.list_where_idx(diff_values_part,wheretype="less",val=-30)
    # print('idx_list={}'.format(idx_list))
    if len(idx_list)==1:
        print('返回规则1-1')
        return idx_list[0]+max_index+1
    elif len(idx_list)>1:
        min_val = diff_values[0]
        min_idx = idx_list[0]
        print('min_val={} min_idx={}'.format(min_val,min_idx))
        for idx in idx_list:
            print('diff_values_part[idx]={} idx={}'.format(diff_values_part[idx], idx))
            if diff_values_part[idx]<min_val:
                min_val = diff_values_part[idx]
                min_idx = idx
        print('返回规则1-2')
        return min_idx + max_index + 1

    #规则2:宽度小于50且变化率接近0的截面最小点
    # print('宽度小于50且变化率接近0的截面最小点')
    # idx_list = bf.list_where_idx(diff_values_part, wheretype="interval", val=-2,val2=2)
    idx_list = []
    for i in range(0,len(diff_values_part)-1):
        if diff_values_part[i]*diff_values_part[i+1]<0:#斜率0点附近
            idx_list.append(i)

    print('diff_values_part={}'.format(diff_values_part))
    print('规则2:宽度小于50且变化率接近0的截面最小点idx_list={}'.format(idx_list))
    values_part = values[max_index:]
    if len(idx_list) > 0:
        min_val = values_part[0]
        min_idx = idx_list[0]
        print('min_val={} min_idx={}'.format(min_val,min_idx))
        for idx in idx_list[:-2]:
            print('values_part[idx]={} idx={}'.format(values_part[idx], idx))
            if values_part[idx]<min_val and values_part[idx]<60:
                min_val = values_part[idx]
                min_idx = idx
                print('min_val={} min_idx={}'.format(min_val,min_idx))
        if min_val<60:
            print('返回规则2')
            return min_idx + max_index + 1


    #规则3:在叶尖部寻找有没有小于阈值的截面
    print('规则3:在叶尖部寻找有没有小于阈值的截面')
    values_part = avg_values[max_index:]
    idx_list = bf.list_where_idx(values_part,wheretype="less",val=toal_value)
    if len(idx_list)>3:
        max_val = values_part[0]
        max_idx = idx_list[0]
        # print('min_val={} min_idx={}'.format(min_val,min_idx))
        for idx in idx_list:
            print('values_part[idx]={} idx={}'.format(values_part[idx], idx))
            if values_part[idx]>max_val:
                max_val = values_part[idx]
                max_idx = idx
        print('返回规则3')
        return max_idx + max_index + 1


    #规则4:从右往左扫描，发现有大于toal_value时，从这个位置想左20个宽度内找出最小的宽度位置
    print('规则4:从右往左扫描，发现有大于toal_value时，从这个位置想左20个宽度内找出最小的宽度位置')
    for i in range(len(values) - 9,max_index,-1):
        # print('i={}'.format(i))
        all_small = True
        for j in range(i, i - 10,-1):
            v = values[j]
            if v > toal_value:
                all_small = False
                break
        if all_small is False:
            lst = values[i-20:i]
            min_idx = bf.get_Min_idx(lst)
            daoshu = len(lst)-min_idx#倒数第几个
            print('返回规则4')
            return i-daoshu+1
    # 规则5:没找到连续10个小于目标值的，在后10个中找小于目标值的
    print('规则5:没找到连续10个小于目标值的，在后10个中找小于目标值的')
    for i in range(max(len(values) - 10, max_index), len(values)):
        v = values[i]
        if v < toal_value:
            print('返回规则5')
            return i
    print('返回规则6')
    return len(values) - 1

def get_foot_index(points, interval=2, k=0.2,right_foot_rate=0.5,img_cv2=None):
    """
    :param points: 连续点集（轮廓）
    :param interval: 主方向线打断时的间隔
    :param k: 叶脚截线长占最长截线段比例
    :param right_foot_rate: 右边叶尖部的foot_k是左边的几分之一
    :return:叶脚上打断点索引(左上)，下打断点索引（左下）, 最大截面上索引，最大截面下索引, 叶头上打断点索引（右上），下打断点索引（右下）, 烟叶长, 宽
    """


    max_index,max_intercept_distance,right_intercept_distance,left_intercept_distance, left_up_intercept_indexs, \
        left_down_intercept_indexs,right_intercept_distance, right_up_intercept_indexs, \
        right_down_intercept_indexs,up_intercept_indexs,down_intercept_indexs,pcamean_list, cntr = get_intercept(points,interval=interval,img_cv2=img_cv2)
    toal_intercept_distance = max_intercept_distance * k

    #获取左边结束截面
    # print('left_intercept_distance={}'.format(len(left_intercept_distance)))
    # print('left toal_intercept_distance={}'.format(toal_intercept_distance))
    # print('right toal_intercept_distance={}'.format(toal_intercept_distance*right_foot_rate))
    # print('max_index={}'.format(max_index))
    left_toal_index = find_left_index2(left_intercept_distance, toal_intercept_distance, max_index)

    # print('left_toal_index={}'.format(left_toal_index))
    left_index1, left_index2 = left_down_intercept_indexs[left_toal_index],left_up_intercept_indexs[left_toal_index]
    if img_cv2 is not None:
        pnt_up = points[left_index1]
        pnt_down = points[left_index2]
        bf.cv2_line(img_cv2, pnt_up, pnt_down, color=(255, 0, 0))



    #获取右边边结束截面
    right_toal_index = find_right_index2(right_intercept_distance, toal_intercept_distance*right_foot_rate, max_index)
    right_index1, right_index2 = right_down_intercept_indexs[right_toal_index],right_up_intercept_indexs[right_toal_index]

    if img_cv2 is not None:
        pnt_up = points[right_index1]
        pnt_down = points[right_index2]
        bf.cv2_line(img_cv2, pnt_up, pnt_down, color=(0, 0, 255))
        bf.show_cv2_img(img_cv2, name="intercept", waitms=0, flag_resize_adapt=True)

    # 计算烟叶长度
    # right_toal_index = find_right_index(intercept_distance, toal_intercept_distance*right_foot_rate, max_index)
    tobacco_length = (right_toal_index - left_toal_index) * interval

    #让left_index1的y<left_index2，right_index1的y<right_index2，以此形成left_index1->left_index2->right_index2->right_index1的逆时针顺序
    pnt_1 = points[left_index1]
    pnt_2 = points[left_index2]
    if pnt_1[1]>pnt_2[1]:
        left_index1, left_index2 = bf.idx_exchange(left_index1,left_index2)
    pnt_1 = points[right_index1]
    pnt_2 = points[right_index2]
    if pnt_1[1]>pnt_2[1]:
        right_index1, right_index2 = bf.idx_exchange(right_index1,right_index2)

    return left_index1, left_index2, \
           down_intercept_indexs[max_index], up_intercept_indexs[max_index], \
           right_index1, right_index2,  \
           tobacco_length, max_intercept_distance,pcamean_list, cntr

def process2(edge_points,center_point_list=None, foot_k=0.125,right_foot_rate=0.5,img_cv2=None):
    """
    基于最大截面截取轮廓
    :param edge_points: 边缘点[宽度，高度]
    :param foot_k: 截取参数(最大截面0.125倍处)
    :return: 截取烟叶左右0.125最大截面轮廓后剩余的轮廓点集
    """
    import numpy as np
    # print('len(edge_points)={}'.format(len(edge_points)))
    left_index1, left_index2, _, _, right_index1, right_index2, _, _,pcamean_list, cntr = get_foot_index(edge_points, k=foot_k,right_foot_rate=right_foot_rate,img_cv2=img_cv2)
    # print('left_index1={}, left_index2={},right_index1={}, right_index2={}'.format(left_index1, left_index2,right_index1, right_index2))
    n = len(edge_points)
    edge_points = edge_points[left_index1:] + edge_points[: left_index1]  # 左上角的点放第一位置
    # print('edge_points={}'.format(edge_points))
    # edge_points_orig = edge_points.copy()
    # print('len(edge_points_orig)={}'.format(len(edge_points_orig)))
    # edge_points_orig_list = edge_points_orig.tolist()

    #一般left_index1<left_index2时 right_index2应该>right_index1，如果不是则特殊处理
    # if left_index1<left_index2 and right_index1>right_index2:
    #     tmp = right_index2
    #     right_index2 = right_index1
    #     right_index1 = tmp
    # print('---left_index1={}'.format(left_index1))
    # print('left_index2={}'.format(left_index2))
    # print('right_index2={}'.format(right_index2))
    # print('right_index1={}'.format(right_index1))

    #
    left_index2 -= left_index1
    if left_index2 < 0:
        left_index2 = n + left_index2
    right_index1 -= left_index1
    if right_index1 < 0:
        right_index1 = n + right_index1
    right_index2 -= left_index1
    if right_index2 < 0:
        right_index2 = n + right_index2
    left_index1 = 0

    if left_index1 < right_index1 < right_index2 < left_index2:
        print("顺时针")

    elif left_index1 < left_index2 < right_index2 < right_index1:
        print("逆时针转顺时针")
        edge_points = bf.arr_reverse(edge_points)
        len_edge_points = len(edge_points)
        left_index2 = len_edge_points-left_index2
        right_index1 = len_edge_points-right_index1
        right_index2 = len_edge_points-right_index2
    else:
        print('left_index1={}'.format(left_index1))
        print('right_index1={}'.format(right_index1))
        print('right_index2={}'.format(right_index2))
        print('left_index2={}'.format(left_index2))
        print("tailor wrong!")

    # edge_points = edge_points[: right_index1 + 1] + edge_points[right_index2: left_index2 + 1]
    right_drop_edge_points_list = edge_points[right_index1 + 1:right_index2]
    # print('len(right_drop_edge_points_list)={}'.format(len(right_drop_edge_points_list)))
    left_drop_edge_points_list = edge_points[left_index2 + 1:]
    # print('len(left_drop_edge_points_list)={}'.format(len(left_drop_edge_points_list)))

    left1_point = edge_points[left_index1]
    left2_point = edge_points[left_index2]
    right1_point = edge_points[right_index1]
    right2_point = edge_points[right_index2]
    # print('left1_point={}'.format(left1_point))
    # print('left2_point={}'.format(left2_point))
    # print('right1_point={}'.format(right1_point))
    # print('right2_point={}'.format(right2_point))
    left_mid_point = list(( ( np.array(left1_point)+np.array(left2_point) )/2).astype(int))
    right_mid_point = list(( ( np.array(right1_point)+np.array(right2_point) )/2).astype(int))

    right_add_points_list = []
    for point in right_drop_edge_points_list:#把烟叶轮廓多边形的两边界面，也就是烟尖和烟基的边界处凹进的部分也添加进烟叶轮廓中去
        _, x_cross, _ = bf.point_line_distance(point[0],point[1],right1_point[0],right1_point[1],right2_point[0],right2_point[1])
        if point[0]<x_cross:
            right_add_points_list.append(point)
    left_add_points_list = []
    for point in left_drop_edge_points_list:
        _, x_cross, _ = bf.point_line_distance(point[0],point[1],left1_point[0],left1_point[1],left2_point[0],left2_point[1])
        if point[0]>x_cross:
            left_add_points_list.append(point)
    edge_points = edge_points[: right_index1 + 1] + right_add_points_list + edge_points[right_index2: left_index2 + 1] + left_add_points_list
    # edge_points = edge_points[: right_index1 + 1] + edge_points[right_index2: left_index2 + 1]
    # edge_points = edge_points[: right_index1 + 1] + right_add_points_list

    #找最左和最右的主脉起始和结束点
    if center_point_list is not None:
        pcamean_list = center_point_list
    ##先找出轮廓的最左侧和最右侧
    edge_points_sorted = bf.point_sort(edge_points.copy())
    edge_points_left = edge_points_sorted[0]
    left_x = edge_points_left[0]
    edge_points_right = edge_points_sorted[-1]
    right_x = edge_points_right[0]
    #找出最靠近轮廓的最左侧两个点
    pcamean_list = bf.point_sort(pcamean_list)
    pcamean_arr = bf.listToArray(pcamean_list)
    left_diff_arr = np.abs(pcamean_arr.copy()[:, 0] - left_x)
    left_diff_idx_arr = bf.list_sort_idx_arr(left_diff_arr)
    left_idx_min = left_diff_idx_arr[0]#最接近轮廓左侧的点的序号
    left_idx_second_min = left_diff_idx_arr[1]#第二接近轮廓左侧的点的序号

    right_diff_arr = np.abs(pcamean_arr.copy()[:, 0] - right_x)
    right_diff_idx_arr = bf.list_sort_idx_arr(right_diff_arr)
    right_idx_min = right_diff_idx_arr[0]#最接近轮廓右侧的点的序号
    right_idx_second_min = right_diff_idx_arr[1]#第二接近轮廓右侧的点的序号



    pnt_left1 = pcamean_list[left_idx_min]
    pnt_left2 = pcamean_list[left_idx_second_min]
    pnt_left_list = bf.find_polygonlineIntersection(edge_points,pnt_left1[0],pnt_left1[1],pnt_left2[0],pnt_left2[1])
    left_pnt = pnt_left_list[0]
    # if left_pnt[0]>pnt_left1[0]:
    #     pcamean_list.pop(0)
 
    pnt_right1 = pcamean_list[right_idx_min]
    pnt_right2 = pcamean_list[right_idx_second_min]
    pnt_right_list = bf.find_polygonlineIntersection(edge_points,pnt_right1[0],pnt_right1[1],pnt_right2[0],pnt_right2[1])
    right_pnt = pnt_right_list[-1]
    # if right_pnt[0]<pnt_right1[0]:
    #     pcamean_list.pop(-1)
    # print('len(pcamean_list)={}'.format(len(pcamean_list)))
    # print('right_idx_min={}'.format(right_idx_min))
    # print('left_idx_min={}'.format(left_idx_min))
    for i in range(len(pcamean_list)-right_idx_min):
        pcamean_list.pop(-1)
    for i in range(left_idx_min):
        pcamean_list.pop(0)

    # pcamean_list.insert(0,[int(left_mid_point[0]),int(left_mid_point[1])])
    # pcamean_list.append([int(right_mid_point[0]),int(right_mid_point[1])])
    pcamean_list.insert(0,[int(left_pnt[0]),int(left_pnt[1])])
    pcamean_list.append([int(right_pnt[0]),int(right_pnt[1])])
    # print('pcamean_list={}'.format(pcamean_list))
    return edge_points, pcamean_list, cntr, left1_point, left2_point, right1_point, right2_point

def get_polygon_area(series_points):
    """
    思路：起点为基础计算三角形面积相加/相减
    :param series_points: 多边形的连续点集合
    :return: 多边形面积
    """
    n = len(series_points)
    square = 0.0  # 默认面积为0
    if n >= 3:
        for i in range(n - 1):
            x1, y1 = series_points[i]
            x2, y2 = series_points[i + 1]
            square += x1 * y2 - x2 * y1
        x1, y1 = series_points[0]
        x2, y2 = series_points[-1]
        try:
            square += x2 * y1 - x1 * y2
        except Warning as e:
            print('\n\n warning{} square={} x2={} y1={} x1={} y2={}'.format(e,square,x2,y1,x1,y2))
        square /= 2
        square = abs(square)
    return square

def get_polygon_area_arr(edge_tailor_arr):#面积
    edge_points = np.reshape(edge_tailor_arr, (-1, 2))
    edge_points_list = edge_points.tolist()  # 边缘点集
    area = get_polygon_area(edge_points_list)
    return area

def get_edge_tailor(path,flag_show_orig_edge=True,flag_show_canque=True,flag_show_edge_tailor=True,resize_rate=1):
    
    image = cv2.imread(path)
    image = bf.cv2_resize_rate(image, rate=resize_rate)
    edge, canques_list,center_point_list = process(None,image,flag_show=flag_show_edge_tailor)
    edge_points = np.reshape(edge, (-1, 2))
    edge_points = edge_points.tolist()
    if flag_show_orig_edge:
        edge_tailor_points,pcamean_list, cntr,_,_,_,_ = process2(edge_points,center_point_list=center_point_list, foot_k=0.25,right_foot_rate=0.25,img_cv2=image)
    else:
        edge_tailor_points, pcamean_list, cntr,_,_,_,_ = process2(edge_points, center_point_list=center_point_list,
                                                                foot_k=0.25,right_foot_rate=0.25)

    # print('pcamean_list={}'.format(pcamean_list))
    # for pnt in pcamean_list:
    #     print('pnt={}'.format(pnt))
    #     bf.cv2_circle(image, pnt[0], pnt[1])
    #     bf.show_cv2_img(image,waitms=0)
    edge_tailor_arr = np.array(edge_tailor_points)
    edge_tailor_arr = np.reshape(edge_tailor_arr, (-1, 1, 2))

    # if flag_show_edge_tailor:
    #     cv.namedWindow("edge_tailor1", cv.WINDOW_NORMAL)
    # cv.drawContours(image, [edge], 0, (0, 255, 255), 2)


    if flag_show_orig_edge:
        edge_orig_arr = np.array(edge_points)
        edge_orig_arr = np.reshape(edge_orig_arr, (-1, 1, 2))
        cv2.drawContours(image, [edge_orig_arr], 0, (0, 0, 255), 2)

    canque_area = 0
    if len(canques_list)>0:
        #过滤残缺
        pop_jdx_list = []
        for jdx,canques in enumerate(canques_list):
            pop_idx_list = []
            for idx,point in enumerate(canques):
                ret = bf.cv2_pointPolygonTest(tuple(point[0]),edge_tailor_arr)
                if ret==-1:
                    pop_idx_list.append(idx)
            if len(pop_idx_list)==len(canques):
                pop_jdx_list.append(jdx)
            elif len(pop_idx_list)==0:
                pass
            else:
                bf.pop_list_ele_by_list(canques,pop_idx_list)
        if len(pop_jdx_list)>0:
            bf.pop_list_ele_by_list(canques_list, pop_jdx_list)

        #画残缺
        if flag_show_canque:
            for canques in canques_list:
                canques_arr = np.array(canques)
                canques_arr = np.reshape(canques_arr, (-1, 1, 2))
                cv2.drawContours(image, [canques_arr], 0, (255, 0, 0), 2)
                canque_area += get_polygon_area_arr(canques_arr)
    else:
        canques_list=[]
    if flag_show_edge_tailor:
        cv2.drawContours(image, [edge_tailor_arr], 0, (0, 255, 0), 2)
        # cv.imshow("edge_tailor1", image)

        bf.show_cv2_img(image,"edge_tailor1",flag_resize_adapt=True)


    edge_tailor_area = get_polygon_area_arr(edge_tailor_arr)
    print('canque_area={}'.format(canque_area))
    print('edge_tailor_area={} 破损率={:.2f}'.format(edge_tailor_area,canque_area/edge_tailor_area))
    if flag_show_edge_tailor:
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    return edge_tailor_arr,canques_list,pcamean_list

def made_mid(path, midpath, midjsonpath, is_save, resize_rate, flag_show):
    img_cv2_gray = proc_file(path)
    img_cv2_gray = bf.cv2_resize_rate(img_cv2_gray, rate=resize_rate)
    # bf.cv2_size(img_cv2_gray,print_flag=True,addname="debug1")
    if flag_show:
        bf.show_cv2_img(img_cv2_gray, "img_cv2_gray 1", flag_resize_adapt=True)
    # 提取边缘和叶尖和叶基又称叶脚

    edge_tailor_arr, canques_list, pcamean_list = get_edge_tailor(path,
                                                                                   flag_show_orig_edge=flag_show,
                                                                                   flag_show_canque=flag_show,
                                                                                   flag_show_edge_tailor=flag_show,
                                                                                   resize_rate=resize_rate
                                                                                   )
    width, height = bf.get_cv2_size(img_cv2_gray)
    mask = bf.make_mask(width, height, edge_tailor_arr)
    # bf.show_cv2_img(mask,"mask",waitms=10)
    mask = bf.cv2_erode(mask)
    if False:  # 如果提取主脉的效果不好，就换个方法
        img_cv2_gray = proc_file(path, proc_type="")
        img_cv2_gray = bf.cv2_resize_rate(img_cv2_gray, rate=resize_rate)
    # 去除表框的纹路
    img_cv2_gray = cv2.bitwise_and(img_cv2_gray, img_cv2_gray, mask=mask)
    # bf.show_cv2_img(img_cv2_gray, "img_cv2_gray", waitms=0)

    # 去除残缺的纹路
    for canques in canques_list:
        mask = bf.make_mask(width, height, canques)
        mask = bf.cv2_dilate(mask)
        mask = cv2.bitwise_not(mask)
        img_cv2_gray = cv2.bitwise_and(img_cv2_gray, img_cv2_gray, mask=mask)
    # img_cv2_gray = bf.cv2_dilate(img_cv2_gray)
    # _, img_cv2_gray = bf.cv2_thd(img_cv2_gray, 40, 255, cv2.THRESH_BINARY)
    img_cv2_gray = cv2.convertScaleAbs(img_cv2_gray, alpha=1.3, beta=0)
    if flag_show:
        bf.show_cv2_img(img_cv2_gray, "img_cv2_graychuqu除去边框残缺等{}".format(path), flag_resize_adapt=True)
    if is_save:
        bf.cv2_save(midpath, img_cv2_gray)
    # print('pcamean_list={}'.format(pcamean_list))
    # for pcamean in pcamean_list:
    #     print('type=pcamean[1] '+str(type(pcamean[1])))
    mid_dict = {"left_mid_point": [int(pcamean_list[0][0]), int(pcamean_list[0][1])],
                "right_mid_point": [int(pcamean_list[-1][0]), int(pcamean_list[-1][1])],
                "pcamean_list": pcamean_list
                }
    if is_save:
        bf.save_json_dict_orig(mid_dict, midjsonpath)
    return mid_dict, img_cv2_gray, edge_tailor_arr




def do_pcamean(mid_dict,path, img_cv2_gray,zhumaidakai_path, pnt_insert_max_amt, zhumai_zhengti_step, flag_show):
    pcamean_list = mid_dict["pcamean_list"]
    height, width, color_size = bf.cv2_size(img_cv2_gray, print_flag=False, addname="HoughLinesP需要根据图片尺寸调节")
    # img_cv2_gray = bf.cv2_GaussianBlur(img_cv2_gray, gau_core=(3, 3))
    if flag_show:
        bf.show_cv2_img(img_cv2_gray, "midpath", flag_resize_adapt=True)
    # sum_img_cv2_gray = np.sum(img_cv2_gray)
    ret, img_cv2_gray = bf.cv2_thd(img_cv2_gray, 40, 100)

    if flag_show:
        bf.show_cv2_img(img_cv2_gray, "cv2_thd", flag_resize_adapt=True)
    if width == 5028 and height == 2280:
        #
        img_cv2_gray = bf.cv2_erode(img_cv2_gray, iterations=1)
        if flag_show:
            bf.show_cv2_img(img_cv2_gray, "img_cv2_gray", flag_resize_adapt=True)
        # lines = cv2.HoughLinesP(img_cv2_gray, 10, np.pi / 180, 10, minLineLength=100,
        #                         maxLineGap=10)  #这套参数对提取主脉有用 函数将通过步长为1的半径和步长为π/180的角来搜索所有可能的直线

        print('{}*{} HoughLinesP 直线检测'.format(width, height))
        lines = cv2.HoughLinesP(img_cv2_gray, 10, np.pi / 1000, 100,
                                minLineLength=50,  # 值越大线越少
                                maxLineGap=10  # 值越小线越少
                                )

    else:
        lines = cv2.HoughLinesP(img_cv2_gray, 10, np.pi / 180, 100,
                                minLineLength=20,  # 20 值越大线越少
                                maxLineGap=20  # 20 值越小线越少
                                )
        # lines = cv2.HoughLines(img_cv2_gray, 150, np.pi / 180, 500)
        # image_1 = img_cv2_gray.copy()
        # image_1 = cv2.cvtColor(img_cv2_gray, cv2.COLOR_GRAY2BGR)
        sum_img_cv2_gray = np.sum(img_cv2_gray)
        sum_lines = np.sum(lines)

    image_1 = bf.cv2_read_file(path)
    if flag_show:
        for pcamean in pcamean_list:
            bf.cv2_circle(image_1, pcamean[0], pcamean[1], radius=2, color=(255, 255, 0))

    pcamean_list = bf.point_sort(pcamean_list)
    pcamean_arr = np.array(pcamean_list)
    my_poly0 = bf.np_polyfit(pcamean_arr[:, 0], pcamean_arr[:, 1],polyamt_max=4)

    # print('A-----------------len(pcamean_list)={}'.format(len(pcamean_list)))
    # 把主脉打开的检测结果点也加入到主脉中
    file_prefix_name = bf.get_file_prefix_name(bf.get_file_name(path))
    json_list, json_name_list = bf.scan_labelme_jsonfile_list(zhumaidakai_path, flag_prefix=True)
    # print('json_name_list={}'.format(json_name_list))
    if file_prefix_name in json_name_list:
        file_idx = bf.list_index(json_name_list, file_prefix_name)
        zhumaidakai_jsonpath = json_list[file_idx]
        # print('发现json={}'.format(zhumaidakai_jsonpath))
        # key_name = bf.labelme_json_search_shapename("zhumai", zhumaidakai_jsonpath)
        key_name = "shapes"
        if key_name is not None:
            # print('发现key={}'.format(key_name))
            # point_list_list = bf.labelme_json_shape_read_point(zhumaidakai_jsonpath, label_name="zhumaidakai", shapes_name=key_name)
            point_list_list = bf.labelme_json_shape_read_point(zhumaidakai_jsonpath, label_name="zhumaizoushi",
                                                               shapes_name=key_name)
            if len(point_list_list) > 0:
                # print('发现len(point_list_list)={}'.format(type(point_list_list)))
                # print('point_list_list={}'.format(point_list_list))
                flat_point_list = bf.ndlistTolist(point_list_list)
                flat_point_arr = bf.listToArray(flat_point_list)
                # for point_list in point_list_list:
                #     point_arr_arr_tmp = bf.listToArray(point_list_list[0])
                # point_arr_arr =np.ndarray(point_list_list)
                # print('flat_point_arr={}'.format(flat_point_arr))
                # bf.arr_shape(point_arr_arr)
                # print('bf.arr_shape(flat_point_arr)={}'.format(bf.arr_shape(flat_point_arr)))
                point_arr_arr = flat_point_arr.reshape((-1, 2))
                # print('point_arr_arr={}'.format(point_arr_arr))

                pnt_insert_max_step = int(len(point_arr_arr) / pnt_insert_max_amt) if len(point_arr_arr) > pnt_insert_max_amt else 1
                cnt = 0
                drop_cnt=0
                append_cnt=0
                append_dist_thd_orig =30
                for try_i in range(10):
                    append_dist_thd = append_dist_thd_orig*try_i
                    for point_list in point_list_list:
                        for point in point_list:
                            cnt += 1
                            if cnt % pnt_insert_max_step == 0:
                                x_zoushi = int(point[0] * width)
                                y_zoushi = int(point[1] * height)
                                y_tmp = my_poly0.cal_y_arr(x_zoushi, flag_int=True)
                                if abs(y_zoushi-y_tmp)<append_dist_thd:
                                # print('y_zoushi={} y_tmp={} diff={}'.format(y_zoushi,y_tmp,y_zoushi-y_tmp))
                                    pcamean_list.append([x_zoushi, y_zoushi])
                                    append_cnt+=1
                                else:
                                    drop_cnt+=1
                    rate = append_cnt / (append_cnt + drop_cnt)
                    if rate>0.7:
                        print('append_cnt={} drop_cnt={} rate={}'.format(append_cnt,drop_cnt,rate))
                        break
    # print('B-----------------len(pcamean_list)={}'.format(len(pcamean_list)))
    # 画多段pca的中心点
    # for pcamean in pcamean_list:
    #     bf.cv2_circle(image_1,pcamean[0],pcamean[1],radius=2,color=(255,255,0))
    pcamean_list = bf.point_sort(pcamean_list)
    pcamean_arr = np.array(pcamean_list)
    my_poly1 = bf.np_polyfit(pcamean_arr[:, 0], pcamean_arr[:, 1],polyamt_max=4)
    x_list = [x for x in range(pcamean_arr[0, 0], pcamean_arr[-1, 0], zhumai_zhengti_step)]
    x_list.append(pcamean_arr[-1, 0])
    x_arr = bf.listToArray(x_list)
    y_arr = my_poly1.cal_y_arr(x_arr, flag_int=True)
    return pcamean_list, lines, image_1, my_poly1, x_arr, y_arr


def do_lines(lines, image_1, my_poly1, pcamean_list, zhumai_zhengti_step):
    # 统计直线所在的y 高度的位置
    height_list = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        height_list.append(y1)
        height_list.append(y2)
    # height_arr = np.array(height_list)
    # height_avg = np.average(height_arr)
    # height_std = np.std(height_arr)
    # height_upper = height_avg + height_std * 0.5
    # height_lower = height_avg - height_std * 0.5
    # print('height_lower={}'.format(height_lower))
    # print('height_upper={}'.format(height_upper))
    # print('height_avg={}'.format(height_avg))
    # 计算线段的斜率，如果斜率在一定范围内就认为是主脉
    bf.cv2_drawtext(image_1, "红色为支脉线段，蓝色为主脉线段，白色为主脉，黄色为主脉结束后烟叶长,浅蓝的点为多段pca的中心点", (0, 0), fontScale=10)

    flrp = bf.find_left_right_point()
    cnt_b_zhezhou = 0
    for line in lines:
        # print('lines={}'.format(lines))
        x1, y1, x2, y2 = line[0]
        if x2 == x1:  # 线段是垂直的
            k1 = np.inf
            k_pcamean = my_poly1.cal_k((x1 + x2) / 2)
            dist = my_poly1.cal_dist((x1 + x2) / 2, (y1 + y2) / 2)
            a = bf.cal_diff_k(k1, k_pcamean, outtype="deg")  # 计算夹角的角度
        else:
            k1 = (y2 - y1) / (x2 - x1)  # 计算直线斜率
            k_pcamean = my_poly1.cal_k((x1 + x2) / 2)
            dist = my_poly1.cal_dist((x1 + x2) / 2, (y1 + y2) / 2)
            a = bf.cal_diff_k(k1, k_pcamean, outtype="deg")  # 计算夹角的角度
        # print('a={}'.format(a))
        # if abs(a) < 20 and (height_lower < y1 < height_upper) and (height_lower < y2 < height_upper):  # 属于主脉
        if abs(a) < 10 and abs(dist) < 20:  # 属于主脉
            cv2.line(image_1, (x1, y1), (x2, y2), (0, 255, 0), 2)
            for i in range(5):
                pcamean_list.append([x1, y1])
                pcamean_list.append([x2, y2])

            flrp.add_point([x1, y1])
            flrp.add_point([x2, y2])
        elif abs(a) < 30 and abs(dist) > 100:#上部叶的褶皱
            cv2.line(image_1, (x1, y1), (x2, y2), (255, 0, 0), 2)
            cnt_b_zhezhou+=1
        else:  # 其余为支脉
            # print('height_lower={}'.format(height_lower))
            # print('y1={}'.format(y1))
            # print('height_upper={}'.format(height_upper))
            cv2.line(image_1, (x1, y1), (x2, y2), (0, 0, 255), 2)

    #
    print('cnt_b_zhezhou={}'.format(cnt_b_zhezhou))
    pcamean_list = bf.point_sort(pcamean_list)
    pcamean_arr = np.array(pcamean_list)
    # print('len(pcamean_arr)={}'.format(len(pcamean_arr)))
    my_poly = bf.np_polyfit(pcamean_arr[:, 0], pcamean_arr[:, 1], polyamt_max=5,flag_print=False)
    x_list = [x for x in range(pcamean_arr[0, 0], pcamean_arr[-1, 0], zhumai_zhengti_step)]
    x_list.append(pcamean_arr[-1, 0])
    x_arr = bf.listToArray(x_list)
    y_arr = my_poly.cal_y_arr(x_arr, flag_int=True)
    return x_arr, y_arr, flrp

def zhumai_zhimai_do_one(path="C:/Users/<USER>/Desktop/yanye/20190515104622.bmp",
           zhumaidakai_path="Z:/wt/yanyefenji_data_test/json_files_merge",
           resize_rate=1,
           json_write_dir="",  # json保存路径
           zhumai_zhengti_step=10,
           pnt_insert_max_amt=100,
           use_write_path_source=False,  # 是否用json保存路径中的文件当做源，为False则使用图片的同目录json
           write_user_feature=True,  # 是否写user_feature
           rewrite_label=True,  # 是否重写label（主脉整体、主脉有效）
            flag_show=False
           ):
    json_path = bf.rename_add_post(path, post="json")
    if use_write_path_source:
        json_path = bf.pathjoin(json_write_dir, bf.rename_add_post(bf.get_file_name(path), post="json"))
    try:
        midpath = bf.rename_add_post(path, add_name="mid")
        midjsonpath = bf.rename_add_post(midpath, post="json")
        mid_dict, img_cv2_gray, edge_tailor_arr = made_mid(path, midpath, midjsonpath, True,
                                                                              resize_rate, flag_show)
        width, height = bf.get_cv2_size(img_cv2_gray)

        pcamean_list, lines, image_1, my_poly1, x_arr, y_arr = do_pcamean(mid_dict,path,img_cv2_gray,
                                                                                             zhumaidakai_path,
                                                                                             pnt_insert_max_amt,
                                                                                             zhumai_zhengti_step,
                                                                                             flag_show)

        import math
        if lines is not None:
            x_arr, y_arr, flrp = do_lines(lines, image_1, my_poly1, pcamean_list, zhumai_zhengti_step)
            if flrp.left_point is None:
                zhumai_point = [(x_arr[0],y_arr[0]),(x_arr[0],y_arr[0])]
            else:
                zhumai_point = [(flrp.left_point[0], flrp.left_point[1]), (flrp.right_point[0], flrp.right_point[1])]
        else:
            zhumai_point = [(x_arr[0], y_arr[0]), (x_arr[0], y_arr[0])]
        len_zhumai = 0
        len_zhumai_other = 0
        zhumai_arr_total = []
        for x, y in zip(x_arr, y_arr):
            zhumai_arr_total.append([x, y])
        zhumai_arr_total = bf.cut_zhumai(height, width, edge_tailor_arr, zhumai_arr_total)
        zhumai_youxiao = []
        zhumai_zhengti = []
        for i in range(len(zhumai_arr_total) - 1):
            if zhumai_arr_total[i][0] > zhumai_point[1][0]:
                len_zhumai_other += bf.euclidean_dist(tuple(zhumai_arr_total[i]), tuple(zhumai_arr_total[i + 1]))
                if len(zhumai_zhengti) == 0:
                    zhumai_zhengti.append(zhumai_arr_total[i])
                zhumai_zhengti.append(zhumai_arr_total[i + 1])
            else:
                len_zhumai += bf.euclidean_dist(tuple(zhumai_arr_total[i]), tuple(zhumai_arr_total[i + 1]))
                if len(zhumai_zhengti) == 0:
                    zhumai_zhengti.append(zhumai_arr_total[i])
                if len(zhumai_youxiao) == 0:
                    zhumai_youxiao.append(zhumai_arr_total[i])
                zhumai_zhengti.append(zhumai_arr_total[i + 1])
                zhumai_youxiao.append(zhumai_arr_total[i + 1])
        sorted(zhumai_youxiao, key=lambda x: x[0], reverse=True)
        sorted(zhumai_zhengti, key=lambda x: x[0], reverse=True)
        if len(zhumai_youxiao) == 0:
            zhumai_youxiao.extend(zhumai_zhengti[0:2])

        if bf.fileexist(json_path):
            json_data = bf.load_json_dict_orig(json_path)
        else:
            json_data = bf.make_labelme_json_dict(bf.get_file_name(path))
        youxiao = {
            "label": 'zhumai_youxiao',
            "points": [[x[0] / width, x[1] / height] for x in zhumai_youxiao],
            "group_id": None,
            "shape_type": "linestrip",
            "flags": {}}
        zhengti = {
            "label": 'zhumai_zhengti',
            "points": [[x[0] / width, x[1] / height] for x in zhumai_zhengti],
            "group_id": None,
            "shape_type": "linestrip",
            "flags": {}}
        for k, v in json_data.items():
            if k == "shapes" or (k.__contains__("shapes") and k.__contains__("zhumai")):
                json_data[k] = [x for x in v if
                                not (x["label"] == "zhumai_zhengti" or x["label"] == "zhumai_youxiao")]
                json_data[k].append(youxiao)
                json_data[k].append(zhengti)
        if rewrite_label:
            # print(zhumai_zhengti)
            bf.save_json_dict_orig(json_data, bf.pathjoin(json_write_dir,
                                                          bf.rename_add_post(bf.get_file_name(path), post="json")))
        # print('len_zhumai={}'.format(len_zhumai))
        # print('len_zhumai_other={}'.format(len_zhumai_other))
        # jsonpath = bf.rename_add_post(path, post="json")
        jsonpath = bf.pathjoin(json_write_dir, bf.rename_add_post(bf.get_file_name(path), post="json"))
        json_dict = bf.load_json_dict_orig(jsonpath)
        user_feature_dict = json_dict["user_feature"] if "user_feature" in json_dict.keys() else dict()
        print('len_zhumai={}'.format(len_zhumai))
        print('width={}'.format(width))
        user_feature_dict["len_zhumai"] = len_zhumai / width
        print('len_zhumai={}'.format(user_feature_dict["len_zhumai"]))
        user_feature_dict["len_zhumai_other"] = len_zhumai_other / width
        user_feature_dict["len_zhumai_rate"] = len_zhumai / (len_zhumai + len_zhumai_other)
        if write_user_feature:
            bf.labelme_json_add_userfeature_file(jsonpath, user_feature_dict)
        # else:
        #     len_zhumai_other = 0
        #     for i in range(len(x_arr) - 1):
        #         color = (0, 255, 255)  # 画粗主脉后的推测的主脉
        #         bf.cv2_line(image_1, (x_arr[i], y_arr[i]), (x_arr[i + 1], y_arr[i + 1]), color=color)
        #         len_zhumai_other += bf.euclidean_dist((x_arr[i], y_arr[i]), (x_arr[i + 1], y_arr[i + 1]))
        #
        #     jsonpath = bf.pathjoin(json_write_dir, bf.rename_add_post(bf.get_file_name(path), post="json"))
        #     json_dict = bf.load_json_dict_orig(jsonpath)
        #     user_feature_dict = json_dict["user_feature"]
        #     user_feature_dict["len_zhumai"] = 0
        #     user_feature_dict["len_zhumai_other"] = len_zhumai_other / width
        #     user_feature_dict["len_zhumai_rate"] = 0
        #     if write_user_feature:
        #         bf.save_json_dict_orig(json_dict, jsonpath)
        #     print('没发现直线')
    except Exception as e:
        bf.my_log(f"error image = {path} error json={json_path}", True,
                  log_path='seg_zhumaidakai_select')
        bf.my_log('traceback.format_exc():\n%s' % traceback.format_exc(), True,
                  log_path='seg_zhumaidakai_select')




def process_single_image(image_name, zoushi_dir, dakai_dir, image_dir, output_dir, threshold=12):
    """
    处理单张图像的完整流程

    Args:
        image_name: 图像文件名
        zoushi_dir: 主脉走势JSON目录
        dakai_dir: 主脉打开JSON目录
        image_dir: 图像目录
        output_dir: 输出目录
        threshold: 主脉选择阈值

    Returns:
        dict: 处理结果
    """
    result = {
        'image_name': image_name,
        'success': False,
        'processing_time': 0.0,
        'error': None,
        'steps_completed': []
    }

    try:
        start_time = time.time()
        base_name = os.path.splitext(image_name)[0]

        print(f"\n处理图像: {image_name}")

        # 构建文件路径
        image_path = os.path.join(image_dir, image_name)
        zoushi_json = os.path.join(zoushi_dir, f"{base_name}.json")
        dakai_json = os.path.join(dakai_dir, f"{base_name}.json")

        # 检查输入文件
        if not os.path.exists(image_path):
            result['error'] = f"图像文件不存在: {image_path}"
            return result

        if not os.path.exists(zoushi_json):
            result['error'] = f"主脉走势JSON不存在: {zoushi_json}"
            return result

        if not os.path.exists(dakai_json):
            result['error'] = f"主脉打开JSON不存在: {dakai_json}"
            return result

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 合并JSON文件（将dakai的shapes添加到zoushi中）
        merged_json = os.path.join(output_dir, f"{base_name}_merged.json")
        merge_json_files(zoushi_json, dakai_json, merged_json)
        result['steps_completed'].append('merge_json')

        # 步骤1: 主脉选择
        print(f"  步骤1: 主脉选择 (阈值={threshold})")
        zhumai_one(merged_json, threshold)
        result['steps_completed'].append('zhumai_select')

        # 步骤2: 删除轮廓外标签
        print(f"  步骤2: 删除轮廓外标签")
        not_delete_labels = ["canque_fill", "shang_yejian", "xia_yejian", "four_points", "beiyong6"]
        delete_do_one_single(image_path, merged_json, not_delete_labels)
        result['steps_completed'].append('delete_out_labels')

        # 步骤3: 主脉特征计算 (优化参数)
        print(f"  步骤3: 主脉特征计算")
        zhumai_zhimai_do_one(
            path=image_path,
            zhumaidakai_path=output_dir,
            resize_rate=0.5,  # 降低分辨率提高速度
            json_write_dir=output_dir,
            zhumai_zhengti_step=20,  # 增加步长减少计算量
            pnt_insert_max_amt=50,  # 大幅降低点插入数量
            use_write_path_source=True,
            write_user_feature=True,
            rewrite_label=True,
            flag_show=False
        )
        result['steps_completed'].append('zhumai_zhimai_calc')

        result['processing_time'] = time.time() - start_time
        result['success'] = True
        print(f"✅ 处理成功，耗时: {result['processing_time']:.2f}秒")

    except Exception as e:
        result['error'] = str(e)
        result['processing_time'] = time.time() - start_time
        print(f"❌ 处理失败: {e}")

    return result


def merge_json_files(zoushi_json, dakai_json, output_json):
    """
    合并主脉走势和主脉打开的JSON文件

    Args:
        zoushi_json: 主脉走势JSON文件路径
        dakai_json: 主脉打开JSON文件路径
        output_json: 输出JSON文件路径
    """
    # 读取主脉走势JSON作为基础
    with open(zoushi_json, 'r', encoding='utf-8-sig') as f:
        zoushi_data = json.load(f)

    # 读取主脉打开JSON
    with open(dakai_json, 'r', encoding='utf-8-sig') as f:
        dakai_data = json.load(f)

    # 将主脉打开的shapes添加到主脉走势中
    if 'shapes' in dakai_data:
        if 'shapes' not in zoushi_data:
            zoushi_data['shapes'] = []
        zoushi_data['shapes'].extend(dakai_data['shapes'])

    # 保存合并后的JSON
    with open(output_json, 'w', encoding='utf-8') as f:
        json.dump(zoushi_data, f, ensure_ascii=False, indent=2)


def delete_do_one_single(image_path, json_path, not_delete_labels):
    """
    删除单个文件的轮廓外标签

    Args:
        image_path: 图像文件路径
        json_path: JSON文件路径
        not_delete_labels: 不删除的标签列表
    """
    delete_out_labels(image_path, json_path, not_delete_labels,
                     lunkuo_shapes_name="shapes",
                     lunkuo_name="canque_fill",
                     shapes_name="shapes")


def main():
    """
    主入口函数
    """
    print("烟叶主脉打开选择独立部署程序")
    print("=" * 50)

    # 配置路径
    zoushi_dir = "/home/<USER>/xm/code/coderafactor/seg_det_zhumaizoushi/test_output/"
    dakai_dir = "/home/<USER>/xm/code/coderafactor/seg_det_zhumaidakai/test_output"
    image_dir = "/home/<USER>/xm/code/coderafactor/test_data/vis/"
    output_dir = "/home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/output"

    print(f"主脉走势目录: {zoushi_dir}")
    print(f"主脉打开目录: {dakai_dir}")
    print(f"图像目录: {image_dir}")
    print(f"输出目录: {output_dir}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试单个文件
    test_image = "hn-cz-2023-C2F-C21-3.bmp"

    if os.path.exists(os.path.join(image_dir, test_image)):
        print(f"\n开始处理测试文件: {test_image}")

        result = process_single_image(test_image, zoushi_dir, dakai_dir, image_dir, output_dir)

        if result['success']:
            print(f"✅ 测试成功完成!")
            print(f"完成步骤: {', '.join(result['steps_completed'])}")
        else:
            print(f"❌ 测试失败: {result['error']}")
    else:
        print(f"测试文件不存在: {test_image}")

    print("\n处理完成!")


def batch_process(zoushi_dir, dakai_dir, image_dir, output_dir, max_images=10, threshold=12):
    """批量处理函数"""
    print("烟叶主脉打开选择独立部署程序 - 批量处理")
    print("=" * 50)

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有图像文件
    if not os.path.exists(image_dir):
        print(f"图像目录不存在: {image_dir}")
        return

    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png'))]

    if not image_files:
        print(f"在目录 {image_dir} 中未找到图像文件")
        return

    print(f"找到 {len(image_files)} 个图像文件")

    success_count = 0
    start_time = time.time()
    results = []

    for i, image_file in enumerate(image_files[:max_images], 1):
        print(f"\n[{i}/{min(max_images, len(image_files))}] 处理: {image_file}")

        result = process_single_image(image_file, zoushi_dir, dakai_dir, image_dir, output_dir, threshold)
        results.append(result)

        if result['success']:
            success_count += 1
        else:
            print(f"❌ 处理失败: {result['error']}")

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n批量处理完成!")
    print(f"总文件数: {min(max_images, len(image_files))}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {min(max_images, len(image_files)) - success_count}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均耗时: {total_time/min(max_images, len(image_files)):.2f}秒/文件")

    return results


if __name__ == "__main__":
    main()
