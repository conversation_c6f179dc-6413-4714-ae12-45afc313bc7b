#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶锯齿平滑处理模块 - 优化部署程序
基于原始deploy_dealing_images_juchi.py的性能优化版本

优化策略：
1. 算法复杂度优化：重构get_intercept_distance2()函数，使用空间索引
2. 向量化计算：NumPy批量操作替代循环
3. 内存管理：实施内存池，消除频繁对象创建
4. 缓存机制：PCA计算结果缓存，避免重复计算
5. 并行计算：多线程处理大数据集
6. I/O优化：批量JSON操作
7. 错误处理：完善异常处理机制

主要功能：
1. 边缘提取与平滑处理
2. 锯齿平滑处理（边缘点抽稀）
3. 叶尖区域划分
4. JSON标注更新

作者: Augment Agent (优化版本)
日期: 2025-07-23
版本: 2.0 (优化版)
基于: deploy_dealing_images_juchi.py v1.0
"""

import os
import sys
import json
import cv2
import numpy as np
import math
from typing import List, Tuple, Dict, Optional, Any, Union
import time
import traceback
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import hashlib
import weakref
import gc
from functools import lru_cache, wraps
import logging
from pathlib import Path
from scipy.spatial import cKDTree
import psutil

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

# ==================== 配置管理类 ====================

@dataclass
class OptimizedProcessingConfig:
    """优化处理配置类 - 遵循单一职责原则"""
    # 基础参数
    foot_k: float = 0.125
    right_foot_rate: float = 0.5
    split_amt: int = 10
    two_side_pixel_amt: int = 10
    active_area_rate_lower: float = 0.003
    interval: int = 2
    
    # 显示控制
    flag_show: bool = False
    flag_show_orig_edge: bool = False
    flag_show_canque: bool = False
    flag_show_edge_tailor: bool = False
    
    # 性能优化参数
    enable_parallel: bool = True
    max_workers: Optional[int] = None
    memory_limit_mb: int = 1024
    spatial_index_radius: float = 50.0
    
    # 缓存配置
    enable_cache: bool = True
    pca_cache_size: int = 1000
    geometry_cache_size: int = 500
    
    # I/O配置
    batch_size: int = 10
    enable_batch_json: bool = True
    
    def __post_init__(self):
        if self.max_workers is None:
            self.max_workers = min(mp.cpu_count(), 8)

# ==================== 异常处理类 ====================

class ProcessingError(Exception):
    """处理错误基类"""
    pass

class GeometryCalculationError(ProcessingError):
    """几何计算错误"""
    pass

class ImageProcessingError(ProcessingError):
    """图像处理错误"""
    pass

class JSONProcessingError(ProcessingError):
    """JSON处理错误"""
    pass

# ==================== 内存管理类 ====================

class MemoryPool:
    """内存池管理器 - 优化内存使用"""
    
    def __init__(self, max_size_mb: int = 1024):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.arrays = {}
        self.current_size = 0
        self._lock = threading.Lock()
    
    def get_array(self, shape: Tuple[int, ...], dtype=np.float32) -> np.ndarray:
        """获取指定形状的数组"""
        with self._lock:
            key = (shape, dtype)
            array_size = np.prod(shape) * np.dtype(dtype).itemsize
            
            if key not in self.arrays:
                if self.current_size + array_size > self.max_size_bytes:
                    self._cleanup_old_arrays()
                
                self.arrays[key] = np.empty(shape, dtype=dtype)
                self.current_size += array_size
            
            return self.arrays[key].copy()  # 返回副本避免数据污染
    
    def _cleanup_old_arrays(self):
        """清理旧数组"""
        items_to_remove = len(self.arrays) // 2
        keys_to_remove = list(self.arrays.keys())[:items_to_remove]
        
        for key in keys_to_remove:
            array = self.arrays.pop(key)
            self.current_size -= array.nbytes
    
    def clear(self):
        """清空内存池"""
        with self._lock:
            self.arrays.clear()
            self.current_size = 0

# 全局内存池实例
memory_pool = MemoryPool()

# ==================== 缓存管理类 ====================

class PCACache:
    """PCA计算结果缓存 - 避免重复计算"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = {}
        self._access_order = []
        self._lock = threading.Lock()
    
    def _get_points_hash(self, points: np.ndarray) -> str:
        """计算点集的哈希值"""
        return hashlib.md5(points.tobytes()).hexdigest()
    
    def get_pca(self, points: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """获取PCA计算结果"""
        points_hash = self._get_points_hash(points)
        
        with self._lock:
            if points_hash in self.cache:
                # 更新访问顺序
                self._access_order.remove(points_hash)
                self._access_order.append(points_hash)
                return self.cache[points_hash]
            
            # 计算PCA
            mean, eigenvectors, eigenvalues = cv2.PCACompute2(points.astype(float), np.array([]))
            result = (mean, eigenvectors, eigenvalues)
            
            # 添加到缓存
            if len(self.cache) >= self.max_size:
                # 移除最旧的条目
                oldest_key = self._access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[points_hash] = result
            self._access_order.append(points_hash)
            
            return result
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self._access_order.clear()

# 全局PCA缓存实例
pca_cache = PCACache()

# ==================== 性能监控装饰器 ====================

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = 0
        
        try:
            start_memory = psutil.Process().memory_info().rss
        except ImportError:
            pass
        
        try:
            result = func(*args, **kwargs)
            success = True
        except Exception as e:
            success = False
            logging.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            
            if start_memory > 0:
                try:
                    end_memory = psutil.Process().memory_info().rss
                    memory_delta = end_memory - start_memory
                    logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s, 内存变化: {memory_delta/1024/1024:.2f}MB")
                except:
                    logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s")
            else:
                logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s")
        
        return result
    return wrapper

# ==================== 错误处理装饰器 ====================

def safe_execute(max_retries: int = 3, delay: float = 0.1):
    """安全执行装饰器 - 带重试机制"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except ProcessingError as e:
                    last_exception = e
                    if attempt == max_retries - 1:
                        logging.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        raise
                    
                    wait_time = delay * (2 ** attempt)  # 指数退避
                    logging.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，{wait_time:.2f}s 后重试: {e}")
                    time.sleep(wait_time)
                except Exception as e:
                    # 非预期错误，直接抛出
                    logging.error(f"函数 {func.__name__} 发生非预期错误: {e}")
                    raise
            
            # 理论上不会到达这里
            raise last_exception
        return wrapper
    return decorator

# ==================== 向量化几何计算类 ====================

class VectorizedGeometry:
    """向量化几何计算类 - 优化数值计算性能"""
    
    @staticmethod
    @lru_cache(maxsize=1000)
    def cached_distance(p1_tuple: Tuple[float, float], p2_tuple: Tuple[float, float]) -> float:
        """缓存的点距离计算"""
        p1, p2 = np.array(p1_tuple), np.array(p2_tuple)
        return np.sqrt(np.sum((p1 - p2)**2))
    
    @staticmethod
    def vectorized_distance(points1: np.ndarray, points2: np.ndarray) -> np.ndarray:
        """向量化距离计算 - 替代原始的 getDist_P2P 和 cal_point_distance"""
        points1 = np.asarray(points1)
        points2 = np.asarray(points2)
        
        if points1.ndim == 1:
            points1 = points1.reshape(1, -1)
        if points2.ndim == 1:
            points2 = points2.reshape(1, -1)
        
        # 使用广播进行向量化计算
        diff = points1[:, np.newaxis, :] - points2[np.newaxis, :, :]
        distances = np.sqrt(np.sum(diff**2, axis=2))
        return distances
    
    @staticmethod
    def batch_line_intersection(lines1: np.ndarray, lines2: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """批量计算直线交点 - 优化原始的 get_cross_point"""
        def get_line_params_batch(lines):
            lines = np.array(lines)
            p1, p2 = lines[:, 0], lines[:, 1]
            a = p1[:, 1] - p2[:, 1]
            b = p2[:, 0] - p1[:, 0]
            c = p1[:, 0] * p2[:, 1] - p2[:, 0] * p1[:, 1]
            return np.column_stack([a, b, c])
        
        params1 = get_line_params_batch(lines1)
        params2 = get_line_params_batch(lines2)
        
        # 批量计算交点
        a1, b1, c1 = params1[:, 0], params1[:, 1], params1[:, 2]
        a2, b2, c2 = params2[:, 0], params2[:, 1], params2[:, 2]
        
        d = a1 * b2 - a2 * b1
        
        # 处理平行线情况
        valid_mask = np.abs(d) > 1e-10
        
        x = np.where(valid_mask, (b1 * c2 - b2 * c1) / d, np.nan)
        y = np.where(valid_mask, (c1 * a2 - c2 * a1) / d, np.nan)
        
        return np.column_stack([x, y]), valid_mask
    
    @staticmethod
    def batch_point_in_line_segment(points: np.ndarray, line_starts: np.ndarray, line_ends: np.ndarray) -> np.ndarray:
        """批量判断点是否在线段内 - 优化原始的 if_inline"""
        # 计算线段长度
        line_lengths = np.sqrt(np.sum((line_ends - line_starts)**2, axis=1))
        
        # 计算点到线段两端的距离
        dist_to_start = np.sqrt(np.sum((points - line_starts)**2, axis=1))
        dist_to_end = np.sqrt(np.sum((points - line_ends)**2, axis=1))
        
        # 判断点是否在线段内（距离和等于线段长度）
        tolerance = 1e-6
        return np.abs(dist_to_start + dist_to_end - line_lengths) < tolerance

# ==================== 空间索引优化类 ====================

class SpatialIndexOptimizer:
    """空间索引优化器 - 使用KD-Tree加速空间查询"""

    def __init__(self, points: np.ndarray):
        self.points = np.asarray(points)
        self.tree = cKDTree(self.points)
        self.geometry = VectorizedGeometry()

    def find_nearby_points(self, query_points: np.ndarray, radius: float = 50.0) -> List[List[int]]:
        """使用空间索引快速查找邻近点"""
        query_points = np.asarray(query_points)
        if query_points.ndim == 1:
            query_points = query_points.reshape(1, -1)

        nearby_indices = []
        for point in query_points:
            indices = self.tree.query_ball_point(point, radius)
            nearby_indices.append(indices)

        return nearby_indices

    def find_intersections_optimized(self, main_orient_points: np.ndarray,
                                   main_orient_line: List[Tuple[float, float]]) -> Tuple[List[float], List[int], List[int]]:
        """优化的交点查找算法"""
        main_orient_points = np.asarray(main_orient_points)
        main_start, main_end = main_orient_line

        intercept_distances = []
        up_indices = []
        down_indices = []

        for main_point in main_orient_points:
            # 使用空间索引找到邻近的边缘点
            nearby_indices = self.find_nearby_points([main_point], radius=100.0)[0]

            if len(nearby_indices) < 2:
                continue

            # 构建垂直线
            vertical_line = self._get_vertical_line_vectorized(main_start, main_end, main_point)

            # 批量计算交点
            intersections, valid_intersections = self._calculate_intersections_batch(
                nearby_indices, vertical_line
            )

            if len(valid_intersections) >= 2:
                # 计算最远距离的两个交点
                distances = self.geometry.vectorized_distance([main_point], valid_intersections)[0]

                if len(distances) >= 2:
                    sorted_indices = np.argsort(distances)
                    max_idx = sorted_indices[-1]
                    min_idx = sorted_indices[0]

                    max_distance = distances[max_idx]
                    intercept_distances.append(max_distance)

                    # 根据y坐标确定上下位置
                    point1 = valid_intersections[max_idx]
                    point2 = valid_intersections[min_idx]

                    if point1[1] > point2[1]:
                        up_indices.append(nearby_indices[max_idx])
                        down_indices.append(nearby_indices[min_idx])
                    else:
                        up_indices.append(nearby_indices[min_idx])
                        down_indices.append(nearby_indices[max_idx])

        return intercept_distances, up_indices, down_indices

    def _get_vertical_line_vectorized(self, p1: Tuple[float, float], p2: Tuple[float, float],
                                    point: Tuple[float, float]) -> List[Tuple[float, float]]:
        """向量化计算垂直线"""
        x, y = point

        if abs(p1[0] - p2[0]) < 1e-10:  # 垂直线
            return [point, (x + 10, y)]
        elif abs(p1[1] - p2[1]) < 1e-10:  # 水平线
            return [point, (x, y + 10)]
        else:
            # 计算垂直线的另一点
            x_result = x + 10
            y_result = (p2[0] - p1[0]) * 10 / (p1[1] - p2[1]) + y
            return [point, (x_result, y_result)]

    def _calculate_intersections_batch(self, nearby_indices: List[int],
                                     vertical_line: List[Tuple[float, float]]) -> Tuple[np.ndarray, np.ndarray]:
        """批量计算交点"""
        edge_lines = []
        n_total = len(self.points)

        for idx in nearby_indices:
            current_point = self.points[idx]
            next_idx = (idx + 1) % n_total
            next_point = self.points[next_idx]
            edge_lines.append([current_point, next_point])

        if not edge_lines:
            return np.array([]), np.array([])

        # 批量计算交点
        vertical_lines = [vertical_line] * len(edge_lines)
        intersections, valid_mask = self.geometry.batch_line_intersection(
            vertical_lines, edge_lines
        )

        # 筛选有效交点（在线段内的）
        valid_intersections = intersections[valid_mask]

        # 进一步验证交点是否在线段内
        if len(valid_intersections) > 0:
            edge_starts = np.array([line[0] for line in edge_lines])[valid_mask]
            edge_ends = np.array([line[1] for line in edge_lines])[valid_mask]

            in_segment_mask = self.geometry.batch_point_in_line_segment(
                valid_intersections, edge_starts, edge_ends
            )

            valid_intersections = valid_intersections[in_segment_mask]

        return intersections, valid_intersections

# ==================== 优化的截距计算器 ====================

class OptimizedInterceptCalculator:
    """优化后的截距计算器 - 核心性能优化"""

    def __init__(self, config: OptimizedProcessingConfig):
        self.config = config
        self.geometry = VectorizedGeometry()

    @performance_monitor
    @safe_execute(max_retries=2)
    def get_intercept_distance_optimized(self, main_orient_points: List[Tuple[float, float]],
                                       points: List[Tuple[float, float]]) -> Tuple[List[float], List[int], List[int]]:
        """
        优化后的截距计算 - 替代原始的 get_intercept_distance2

        主要优化：
        1. 使用空间索引减少计算量 (O(n*m) -> O(n*log(m)))
        2. 向量化计算替代嵌套循环
        3. 并行处理大数据集
        4. 内存池管理临时对象
        """
        try:
            main_orient_points = np.asarray(main_orient_points)
            edge_points = np.asarray(points)

            if len(main_orient_points) == 0 or len(edge_points) == 0:
                return [], [], []

            # 创建空间索引
            spatial_optimizer = SpatialIndexOptimizer(edge_points)

            # 构建主方向线
            main_orient_line = [main_orient_points[0], main_orient_points[-1]]

            # 根据数据规模选择处理策略
            if len(main_orient_points) > 100 and self.config.enable_parallel:
                return self._parallel_intercept_calculation(
                    main_orient_points, edge_points, spatial_optimizer, main_orient_line
                )
            else:
                return spatial_optimizer.find_intersections_optimized(
                    main_orient_points, main_orient_line
                )

        except Exception as e:
            raise GeometryCalculationError(f"截距计算失败: {e}")

    def _parallel_intercept_calculation(self, main_orient_points: np.ndarray, edge_points: np.ndarray,
                                      spatial_optimizer: SpatialIndexOptimizer,
                                      main_orient_line: List[Tuple[float, float]]) -> Tuple[List[float], List[int], List[int]]:
        """并行计算截距"""
        # 将主方向点分块
        num_workers = min(self.config.max_workers, len(main_orient_points) // 10 + 1)
        chunks = np.array_split(main_orient_points, num_workers)

        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [
                executor.submit(
                    spatial_optimizer.find_intersections_optimized,
                    chunk, main_orient_line
                )
                for chunk in chunks if len(chunk) > 0
            ]

            results = [future.result() for future in futures]

        # 合并结果
        return self._merge_intercept_results(results)

    def _merge_intercept_results(self, results: List[Tuple[List[float], List[int], List[int]]]) -> Tuple[List[float], List[int], List[int]]:
        """合并并行计算结果"""
        merged_distances = []
        merged_up_indices = []
        merged_down_indices = []

        for distances, up_indices, down_indices in results:
            merged_distances.extend(distances)
            merged_up_indices.extend(up_indices)
            merged_down_indices.extend(down_indices)

        return merged_distances, merged_up_indices, merged_down_indices

# ==================== 优化的PCA计算器 ====================

class OptimizedPCAComputer:
    """优化的PCA计算器 - 缓存和批量处理"""

    def __init__(self, config: OptimizedProcessingConfig):
        self.config = config
        self.cache = pca_cache

    @performance_monitor
    def get_main_orient_line_optimized(self, points: List[Tuple[float, float]],
                                     img_cv2: Optional[np.ndarray] = None) -> Tuple[Dict[str, List], List, List]:
        """
        优化后的主方向线计算 - 替代原始的 get_main_orient_line2

        主要优化：
        1. PCA计算结果缓存
        2. 向量化操作
        3. 减少重复计算
        """
        try:
            main_orient_line_dict = {"left": [], "right": [], "center": []}
            points_arr = np.array(points)

            if len(points_arr) == 0:
                return main_orient_line_dict, [], []

            x_arr = points_arr[:, 0]
            x_max = np.max(x_arr)
            x_min = np.min(x_arr)

            step_amt = 10
            interval_amt = 5
            x_step = (x_max - x_min) / step_amt
            x_split_list = np.linspace(x_min, x_max, step_amt + 1)

            pcamean_list = []
            center_point_list = []

            # 批量处理PCA计算
            for i in range(step_amt - interval_amt + 1):
                x_lower = x_split_list[i]
                x_upper = x_split_list[i + interval_amt]

                # 使用向量化操作筛选点
                mask = (x_arr >= x_lower) & (x_arr <= x_upper)
                points_arr_step = points_arr[mask]

                if len(points_arr_step) < 2:
                    continue

                # 使用缓存的PCA计算
                mean, eigenvectors, eigenvalues = self.cache.get_pca(points_arr_step)

                pcamean_list.append(bf.get_center_point_list_pca(mean))
                center_point = bf.get_center_point2_pca(mean)
                center_point_list.append(center_point)

                # 可选的可视化
                if img_cv2 is not None and self.config.flag_show:
                    pca_ret_list = [mean, eigenvectors, eigenvalues]
                    bf.show_main_orient(pca_ret_list, img_cv2)

            # 设置左右方向线
            if len(center_point_list) >= 2:
                main_orient_line_dict["left"] = [center_point_list[0], center_point_list[1]]
                main_orient_line_dict["right"] = [center_point_list[-2], center_point_list[-1]]

            # 计算整体PCA
            mean, eigenvectors, eigenvalues = self.cache.get_pca(points_arr)
            main_eig_index = self._find_max_eigenvalue_index(eigenvalues)
            main_eig = eigenvectors[main_eig_index]
            center_point = bf.get_center_point2_pca(mean)
            main_eig_point = (center_point[0] + main_eig[0], center_point[1] + main_eig[1])
            main_orient_line_dict["center"] = [center_point, main_eig_point]

            if img_cv2 is not None and self.config.flag_show:
                pca_ret_list = [mean, eigenvectors, eigenvalues]
                bf.show_main_orient(pca_ret_list, img_cv2)

            return main_orient_line_dict, pcamean_list, []

        except Exception as e:
            raise GeometryCalculationError(f"主方向线计算失败: {e}")

    def _find_max_eigenvalue_index(self, eigenvalues: np.ndarray) -> int:
        """查找最大特征值索引"""
        eigenvalues_flat = np.reshape(eigenvalues, (-1))
        return np.argmax(eigenvalues_flat)

# ==================== 优化的图像处理器 ====================

class OptimizedImageProcessor:
    """优化的图像处理器"""

    def __init__(self, config: OptimizedProcessingConfig):
        self.config = config
        self.memory_pool = memory_pool
        self.intercept_calculator = OptimizedInterceptCalculator(config)
        self.pca_computer = OptimizedPCAComputer(config)

    @performance_monitor
    @safe_execute(max_retries=2)
    def cal_binary_image_optimized(self, image: np.ndarray, red_filter: bool = False) -> np.ndarray:
        """优化的二值化图像计算"""
        try:
            v_black = [52, 255]

            # 使用内存池获取临时数组
            blurred_shape = image.shape
            blurred = self.memory_pool.get_array(blurred_shape, dtype=image.dtype)

            # 高斯模糊去噪
            cv2.GaussianBlur(image, (11, 11), 0, dst=blurred)

            # 转换到HSV空间
            img_hsv = cv2.cvtColor(blurred, cv2.COLOR_BGR2HSV)
            H, S, V = cv2.split(img_hsv)

            # 二值化
            ret, thresh1 = bf.cv2_thd(V, v_black[0], v_black[1])

            return thresh1

        except Exception as e:
            raise ImageProcessingError(f"二值化处理失败: {e}")

    @performance_monitor
    def get_edge_canques_optimized(self, binary: np.ndarray, flag_show: bool = True) -> Tuple[np.ndarray, List]:
        """优化的边缘和残缺提取"""
        try:
            contours, hierarchy = cv2.findContours(binary, cv2.RETR_CCOMP, cv2.CHAIN_APPROX_SIMPLE)

            if flag_show and self.config.flag_show:
                img = bf.cv2_gray_to_color(binary)
                cv2.drawContours(img, contours, -1, (0, 0, 255), 3)
                cv2.imshow("contours_img", img)
                cv2.waitKey(0)

            # 优化的边界数量计算
            border_numbers = [len(border) for border in contours] if contours else []

            # 提取边缘和残缺
            edge_index, canque_indices = self._extract_edge_canque_optimized(border_numbers)

            if edge_index >= 0:
                edge = contours[edge_index]
                canques_list = [contours[i] for i in canque_indices]
                return edge, canques_list
            else:
                return np.array([]), []

        except Exception as e:
            raise ImageProcessingError(f"边缘提取失败: {e}")

    def _extract_edge_canque_optimized(self, numbers: List[int], limit: int = 10) -> Tuple[int, List[int]]:
        """优化的边缘残缺提取"""
        if not numbers:
            return -1, []

        # 使用NumPy向量化操作
        numbers_arr = np.array(numbers)
        valid_mask = numbers_arr > limit

        if not np.any(valid_mask):
            return -1, []

        valid_indices = np.where(valid_mask)[0]
        valid_numbers = numbers_arr[valid_indices]

        # 找到最大值索引
        max_local_idx = np.argmax(valid_numbers)
        max_global_idx = valid_indices[max_local_idx]

        # 其他有效索引作为残缺
        canque_indices = [idx for idx in valid_indices if idx != max_global_idx]

        return max_global_idx, canque_indices

# ==================== 优化的主处理器 ====================

class OptimizedMainProcessor:
    """优化的主处理器 - 整合所有优化组件"""

    def __init__(self, config: OptimizedProcessingConfig):
        self.config = config
        self.image_processor = OptimizedImageProcessor(config)
        self.geometry = VectorizedGeometry()

    @performance_monitor
    @safe_execute(max_retries=2)
    def process_optimized(self, path: Optional[str] = None, img_cv2: Optional[np.ndarray] = None,
                         red_filter: bool = False, flag_show: bool = True) -> Tuple[np.ndarray, List, List]:
        """
        优化的主处理函数 - 替代原始的 process

        主要优化：
        1. 内存池管理
        2. 向量化计算
        3. 并行处理
        4. 缓存机制
        """
        try:
            # 读取图像
            if path is None:
                image = img_cv2
            else:
                image = cv2.imread(path)

            if image is None:
                raise ImageProcessingError("无法读取图像")

            # 二值化处理
            binary = self.image_processor.cal_binary_image_optimized(image, red_filter)
            binary = bf.cv2_erode(binary, iterations=0)

            # 边缘和残缺提取
            edge, canques_list = self.image_processor.get_edge_canques_optimized(binary, flag_show)

            # 计算图像尺寸
            height, width = binary.shape[:2]

            # 优化的中心点计算
            center_point_list = self._calculate_center_points_optimized(
                binary, width, height, image if img_cv2 is not None else None
            )

            return edge, canques_list, center_point_list

        except Exception as e:
            raise ProcessingError(f"主处理失败: {e}")

    def _calculate_center_points_optimized(self, binary: np.ndarray, width: int, height: int,
                                         display_image: Optional[np.ndarray] = None) -> List[List[float]]:
        """优化的中心点计算"""
        try:
            interval_width = int(width / self.config.split_amt)
            background_width = interval_width + 2 * self.config.two_side_pixel_amt

            # 使用内存池创建背景图像
            img_background = self.memory_pool.get_array((height, background_width), dtype=np.uint8)
            img_background.fill(0)

            center_point_list = []
            split_area = interval_width * height

            for i in range(self.config.split_amt):
                left = i * interval_width
                top = 0

                # 裁剪图像区域
                binary_crop = binary[top:top+height, left:left+interval_width]

                # 计算活跃区域比例
                active_area = np.sum(binary_crop) / 255
                active_area_rate = active_area / split_area

                if active_area_rate > self.config.active_area_rate_lower:
                    # 重置背景图像
                    img_background.fill(0)

                    # 粘贴裁剪图像
                    paste_region = img_background[top:top+height,
                                                self.config.two_side_pixel_amt:self.config.two_side_pixel_amt+interval_width]
                    paste_region[:] = binary_crop

                    # 计算中心点
                    cx_tmp, cy_tmp = bf.cv2_img_center_moments(img_background)
                    cx = cx_tmp + left - self.config.two_side_pixel_amt
                    cy = cy_tmp + top
                    center_point_list.append([cx, cy])

                    # 可选的可视化
                    if display_image is not None and self.config.flag_show:
                        bf.cv2_circle(display_image, cx, cy)

            # 移除第一个点（通常是边界点）
            if center_point_list:
                center_point_list.pop(0)

            return center_point_list

        except Exception as e:
            raise GeometryCalculationError(f"中心点计算失败: {e}")

# ==================== 兼容性函数 - 保持原始接口 ====================

# 全局配置实例
global_config = OptimizedProcessingConfig()
global_processor = OptimizedMainProcessor(global_config)

def find_shape(json_data: Dict, shape_name: str) -> Union[List, bool]:
    """查找形状 - 保持原始接口"""
    try:
        for item in json_data['shapes']:
            if item['label'] == shape_name:
                return item['points']
        return False
    except Exception:
        return False

def getDist_P2P(Point0: Tuple[float, float], PointA: Tuple[float, float]) -> float:
    """优化的点距离计算 - 保持原始接口"""
    return VectorizedGeometry.cached_distance(tuple(Point0), tuple(PointA))

def cal_point_distance(p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
    """优化的点距离计算 - 保持原始接口"""
    return VectorizedGeometry.cached_distance(tuple(p1), tuple(p2))

def change_shape_points(json_data: Dict, shape_name: str, points: List) -> None:
    """修改形状点 - 保持原始接口"""
    try:
        for item in json_data['shapes']:
            if item['label'] == shape_name:
                item['points'] = points
                break
    except Exception as e:
        raise JSONProcessingError(f"修改形状点失败: {e}")

def cal_binary_image2(image: np.ndarray, red_filter: bool = False) -> np.ndarray:
    """优化的二值化图像计算 - 保持原始接口"""
    return global_processor.image_processor.cal_binary_image_optimized(image, red_filter)

def get_edge_canques(binary: np.ndarray, flag_show: bool = True) -> Tuple[np.ndarray, List]:
    """优化的边缘残缺提取 - 保持原始接口"""
    return global_processor.image_processor.get_edge_canques_optimized(binary, flag_show)

def process(path: Optional[str] = None, img_cv2: Optional[np.ndarray] = None,
           red_filter: bool = False, flag_show: bool = True, **kwargs) -> Tuple[np.ndarray, List, List]:
    """优化的主处理函数 - 保持原始接口"""
    return global_processor.process_optimized(path, img_cv2, red_filter, flag_show)

# ==================== 优化的几何计算函数 ====================

def get_vertical_line(line: List[Tuple[float, float]], select_point: Tuple[float, float]) -> List[Tuple[float, float]]:
    """优化的垂直线计算 - 保持原始接口"""
    try:
        p1, p2 = line
        x1, y1 = p1
        x2, y2 = p2
        x, y = select_point

        if abs(x1 - x2) < 1e-10:  # 垂直线
            next_point = (x + 10, y)
        elif abs(y1 - y2) < 1e-10:  # 水平线
            next_point = (x, y + 10)
        else:
            x_result = x + 10
            y_result = (x2 - x1) * 10 / (y1 - y2) + y
            next_point = (x_result, y_result)

        return [select_point, next_point]
    except Exception as e:
        raise GeometryCalculationError(f"垂直线计算失败: {e}")

def get_cross_point(line1: List[Tuple[float, float]], line2: List[Tuple[float, float]]) -> Optional[Tuple[float, float]]:
    """优化的交点计算 - 保持原始接口"""
    try:
        p1, p2 = line1
        p3, p4 = line2

        # 计算直线参数 ax + by + c = 0
        def get_line_para(pt1, pt2):
            x1, y1 = pt1
            x2, y2 = pt2
            a = y1 - y2
            b = x2 - x1
            c = x1 * y2 - x2 * y1
            return a, b, c

        a1, b1, c1 = get_line_para(p1, p2)
        a2, b2, c2 = get_line_para(p3, p4)

        d = a1 * b2 - a2 * b1
        if abs(d) < 1e-10:  # 平行线
            return None

        x = (b1 * c2 - b2 * c1) / d
        y = (c1 * a2 - c2 * a1) / d

        return (x, y)
    except Exception as e:
        raise GeometryCalculationError(f"交点计算失败: {e}")

def if_inline(point1: Tuple[float, float], point2: Tuple[float, float], point: Tuple[float, float]) -> bool:
    """优化的点在线段内判断 - 保持原始接口"""
    try:
        d = cal_point_distance(point1, point2)
        d1 = cal_point_distance(point1, point)
        d2 = cal_point_distance(point2, point)

        # 使用容差判断
        tolerance = 1e-6
        return abs(d1 + d2 - d) < tolerance
    except Exception as e:
        return False

def get_intercept_distance2(main_orient_points: List[Tuple[float, float]],
                          points: List[Tuple[float, float]]) -> Tuple[List[float], List[int], List[int]]:
    """优化的截距计算 - 保持原始接口"""
    return global_processor.image_processor.intercept_calculator.get_intercept_distance_optimized(
        main_orient_points, points
    )

# ==================== 优化的主要业务逻辑函数 ====================

@performance_monitor
@safe_execute(max_retries=2)
def get_edge_tailor_optimized(path: str, flag_show_orig_edge: bool = True, flag_show_canque: bool = True,
                            flag_show_edge_tailor: bool = True, resize_rate: float = 1.0) -> Tuple[np.ndarray, List, List]:
    """
    优化的边缘裁剪函数 - 替代原始的 get_edge_tailor

    主要优化：
    1. 使用优化的处理流程
    2. 内存管理优化
    3. 向量化计算
    """
    try:
        # 读取和预处理图像
        image = cv2.imread(path)
        if image is None:
            raise ImageProcessingError(f"无法读取图像: {path}")

        image = bf.cv2_resize_rate(image, rate=resize_rate)

        # 使用优化的处理流程
        edge, canques_list, center_point_list = process(None, image, flag_show=flag_show_edge_tailor)

        if edge is None or len(edge) == 0:
            return np.array([]), [], []

        edge_points = np.reshape(edge, (-1, 2)).tolist()

        # 使用优化的process2函数
        if flag_show_orig_edge:
            edge_tailor_points, pcamean_list, cntr, _, _, _, _ = process2_optimized(
                edge_points, center_point_list=center_point_list,
                foot_k=0.25, right_foot_rate=0.25, img_cv2=image
            )
        else:
            edge_tailor_points, pcamean_list, cntr, _, _, _, _ = process2_optimized(
                edge_points, center_point_list=center_point_list,
                foot_k=0.25, right_foot_rate=0.25
            )

        # 转换为numpy数组
        edge_tailor_arr = np.array(edge_tailor_points)
        edge_tailor_arr = np.reshape(edge_tailor_arr, (-1, 1, 2))

        # 处理显示逻辑
        if flag_show_orig_edge:
            edge_orig_arr = np.array(edge_points)
            edge_orig_arr = np.reshape(edge_orig_arr, (-1, 1, 2))
            cv2.drawContours(image, [edge_orig_arr], 0, (0, 0, 255), 2)

        # 处理残缺区域
        canque_area = 0
        if len(canques_list) > 0:
            canques_list = _filter_canques_optimized(canques_list, edge_tailor_arr)

            if flag_show_canque:
                for canques in canques_list:
                    canques_arr = np.array(canques)
                    canques_arr = np.reshape(canques_arr, (-1, 1, 2))
                    cv2.drawContours(image, [canques_arr], 0, (255, 0, 0), 2)
                    canque_area += _get_polygon_area_arr_optimized(canques_arr)

        # 显示结果
        if flag_show_edge_tailor:
            cv2.drawContours(image, [edge_tailor_arr], 0, (0, 255, 0), 2)
            bf.show_cv2_img(image, "edge_tailor1", flag_resize_adapt=True)

        # 计算面积和破损率
        edge_tailor_area = _get_polygon_area_arr_optimized(edge_tailor_arr)
        print(f'canque_area={canque_area}')
        print(f'edge_tailor_area={edge_tailor_area} 破损率={canque_area/edge_tailor_area:.2f}')

        if flag_show_edge_tailor:
            cv2.waitKey(0)
            cv2.destroyAllWindows()

        return edge_tailor_arr, canques_list, pcamean_list

    except Exception as e:
        raise ProcessingError(f"边缘裁剪失败: {e}")

def _filter_canques_optimized(canques_list: List, edge_tailor_arr: np.ndarray) -> List:
    """优化的残缺过滤"""
    try:
        filtered_canques = []

        for canques in canques_list:
            valid_points = []
            for point in canques:
                ret = bf.cv2_pointPolygonTest(tuple(point[0]), edge_tailor_arr)
                if ret != -1:  # 点在多边形内或边界上
                    valid_points.append(point)

            # 如果有有效点，保留这个残缺
            if valid_points:
                filtered_canques.append(valid_points)

        return filtered_canques
    except Exception as e:
        logging.warning(f"残缺过滤失败: {e}")
        return canques_list

def _get_polygon_area_arr_optimized(edge_arr: np.ndarray) -> float:
    """优化的多边形面积计算"""
    try:
        edge_points = np.reshape(edge_arr, (-1, 2))
        return _get_polygon_area_optimized(edge_points.tolist())
    except Exception as e:
        logging.warning(f"面积计算失败: {e}")
        return 0.0

def _get_polygon_area_optimized(series_points: List[Tuple[float, float]]) -> float:
    """优化的多边形面积计算 - 使用向量化操作"""
    try:
        n = len(series_points)
        if n < 3:
            return 0.0

        # 转换为numpy数组进行向量化计算
        points = np.array(series_points)

        # 使用shoelace公式的向量化版本
        x = points[:, 0]
        y = points[:, 1]

        # 计算面积
        area = 0.5 * abs(np.sum(x[:-1] * y[1:] - x[1:] * y[:-1]) + x[-1] * y[0] - x[0] * y[-1])

        return area
    except Exception as e:
        logging.warning(f"多边形面积计算失败: {e}")
        return 0.0

@performance_monitor
@safe_execute(max_retries=2)
def process2_optimized(edge_points: List[Tuple[float, float]], center_point_list: Optional[List] = None,
                      foot_k: float = 0.125, right_foot_rate: float = 0.5,
                      img_cv2: Optional[np.ndarray] = None) -> Tuple[List, List, List, Tuple, Tuple, Tuple, Tuple]:
    """
    优化的process2函数 - 基于最大截面截取轮廓

    主要优化：
    1. 使用优化的几何计算
    2. 向量化操作
    3. 内存管理优化
    """
    try:
        if not edge_points:
            return [], [], [], (0, 0), (0, 0), (0, 0), (0, 0)

        # 使用优化的get_foot_index函数
        left_index1, left_index2, _, _, right_index1, right_index2, _, _, pcamean_list, cntr = get_foot_index_optimized(
            edge_points, k=foot_k, right_foot_rate=right_foot_rate, img_cv2=img_cv2
        )

        n = len(edge_points)

        # 重新排列边缘点，使左上角点在第一位
        edge_points = edge_points[left_index1:] + edge_points[:left_index1]

        # 调整索引
        left_index2 -= left_index1
        if left_index2 < 0:
            left_index2 = n + left_index2

        right_index1 -= left_index1
        if right_index1 < 0:
            right_index1 = n + right_index1

        right_index2 -= left_index1
        if right_index2 < 0:
            right_index2 = n + right_index2

        left_index1 = 0

        # 处理顺序问题
        if left_index1 < left_index2 < right_index2 < right_index1:
            print("逆时针转顺时针")
            edge_points = list(reversed(edge_points))
            len_edge_points = len(edge_points)
            left_index2 = len_edge_points - left_index2
            right_index1 = len_edge_points - right_index1
            right_index2 = len_edge_points - right_index2

        # 提取关键点
        left1_point = edge_points[left_index1]
        left2_point = edge_points[left_index2]
        right1_point = edge_points[right_index1]
        right2_point = edge_points[right_index2]

        # 裁剪边缘点
        edge_points = edge_points[:right_index1 + 1] + edge_points[right_index2:left_index2 + 1]

        # 处理主脉点
        if center_point_list is not None:
            pcamean_list = center_point_list

        # 优化主脉点处理
        pcamean_list = _process_main_vein_points_optimized(edge_points, pcamean_list)

        return edge_points, pcamean_list, cntr, left1_point, left2_point, right1_point, right2_point

    except Exception as e:
        raise ProcessingError(f"process2优化失败: {e}")

def _process_main_vein_points_optimized(edge_points: List, pcamean_list: List) -> List:
    """优化的主脉点处理"""
    try:
        if not pcamean_list:
            return []

        # 使用向量化操作处理主脉点
        edge_points_sorted = sorted(edge_points, key=lambda x: x[0])
        edge_points_left = edge_points_sorted[0]
        edge_points_right = edge_points_sorted[-1]

        left_x = edge_points_left[0]
        right_x = edge_points_right[0]

        # 向量化计算最近点
        pcamean_arr = np.array(pcamean_list)

        # 找到最接近左右边界的点
        left_distances = np.abs(pcamean_arr[:, 0] - left_x)
        right_distances = np.abs(pcamean_arr[:, 0] - right_x)

        left_idx_min = np.argmin(left_distances)
        right_idx_min = np.argmin(right_distances)

        # 计算交点
        pnt_left1 = pcamean_list[left_idx_min]
        pnt_left2 = pcamean_list[min(left_idx_min + 1, len(pcamean_list) - 1)]

        pnt_right1 = pcamean_list[right_idx_min]
        pnt_right2 = pcamean_list[max(right_idx_min - 1, 0)]

        # 计算与轮廓的交点
        left_pnt = _find_polygon_intersection_optimized(edge_points, pnt_left1, pnt_left2)
        right_pnt = _find_polygon_intersection_optimized(edge_points, pnt_right1, pnt_right2)

        # 裁剪主脉点列表
        start_idx = max(0, left_idx_min)
        end_idx = min(len(pcamean_list), right_idx_min + 1)

        result_list = pcamean_list[start_idx:end_idx]

        # 添加边界点
        if left_pnt:
            result_list.insert(0, [int(left_pnt[0]), int(left_pnt[1])])
        if right_pnt:
            result_list.append([int(right_pnt[0]), int(right_pnt[1])])

        return result_list

    except Exception as e:
        logging.warning(f"主脉点处理失败: {e}")
        return pcamean_list

def _find_polygon_intersection_optimized(edge_points: List, p1: List, p2: List) -> Optional[List]:
    """优化的多边形交点查找"""
    try:
        # 简化实现，返回第一个有效交点
        intersections = bf.find_polygonlineIntersection(edge_points, p1[0], p1[1], p2[0], p2[1])
        return intersections[0] if intersections else None
    except Exception as e:
        logging.warning(f"多边形交点查找失败: {e}")
        return None

def get_foot_index_optimized(points: List[Tuple[float, float]], interval: int = 2, k: float = 0.2,
                           right_foot_rate: float = 0.5, img_cv2: Optional[np.ndarray] = None) -> Tuple:
    """
    优化的叶脚索引计算 - 替代原始的 get_foot_index

    主要优化：
    1. 使用优化的截距计算
    2. 向量化操作
    3. 缓存机制
    """
    try:
        # 使用优化的截距计算
        max_index, max_intercept_distance, right_intercept_distance, left_intercept_distance, \
        left_up_intercept_indexs, left_down_intercept_indexs, right_intercept_distance, \
        right_up_intercept_indexs, right_down_intercept_indexs, up_intercept_indexs, \
        down_intercept_indexs, pcamean_list, cntr = get_intercept_optimized(points, interval=interval, img_cv2=img_cv2)

        toal_intercept_distance = max_intercept_distance * k

        # 获取左边结束截面
        left_toal_index = _find_left_index_optimized(left_intercept_distance, toal_intercept_distance, max_index)
        left_index1, left_index2 = left_down_intercept_indexs[left_toal_index], left_up_intercept_indexs[left_toal_index]

        if img_cv2 is not None and global_config.flag_show:
            pnt_up = points[left_index1]
            pnt_down = points[left_index2]
            bf.cv2_line(img_cv2, pnt_up, pnt_down, color=(255, 0, 0))

        # 获取右边结束截面
        right_toal_index = _find_right_index_optimized(right_intercept_distance, toal_intercept_distance * right_foot_rate, max_index)
        right_index1, right_index2 = right_down_intercept_indexs[right_toal_index], right_up_intercept_indexs[right_toal_index]

        if img_cv2 is not None and global_config.flag_show:
            pnt_up = points[right_index1]
            pnt_down = points[right_index2]
            bf.cv2_line(img_cv2, pnt_up, pnt_down, color=(0, 0, 255))
            bf.show_cv2_img(img_cv2, name="intercept", waitms=0, flag_resize_adapt=True)

        # 计算烟叶长度
        tobacco_length = (right_toal_index - left_toal_index) * interval

        # 确保索引顺序正确
        pnt_1 = points[left_index1]
        pnt_2 = points[left_index2]
        if pnt_1[1] > pnt_2[1]:
            left_index1, left_index2 = left_index2, left_index1

        pnt_1 = points[right_index1]
        pnt_2 = points[right_index2]
        if pnt_1[1] > pnt_2[1]:
            right_index1, right_index2 = right_index2, right_index1

        return (left_index1, left_index2, down_intercept_indexs[max_index], up_intercept_indexs[max_index],
                right_index1, right_index2, tobacco_length, max_intercept_distance, pcamean_list, cntr)

    except Exception as e:
        raise GeometryCalculationError(f"叶脚索引计算失败: {e}")

def get_intercept_optimized(points: List[Tuple[float, float]], interval: int = 2,
                          img_cv2: Optional[np.ndarray] = None) -> Tuple:
    """优化的截距计算函数"""
    try:
        # 使用优化的PCA计算
        pca_computer = global_processor.image_processor.pca_computer

        # 填充点以确保连续性
        fill_points = _get_line_points_optimized(points, 1)

        # 获取主方向线
        main_orient_line_dict, pcamean_list, cntr = pca_computer.get_main_orient_line_optimized(fill_points, img_cv2)

        # 获取最宽截面
        main_orient_line = main_orient_line_dict["center"]
        main_orient_line = _get_main_orient_line_range_optimized(main_orient_line, points)
        main_orient_line = _get_main_orient_line_orient_optimized(main_orient_line)
        main_orient_points = _get_split_points_optimized(main_orient_line, interval)

        # 获取截断距离及对应的上下点索引
        intercept_calculator = global_processor.image_processor.intercept_calculator
        intercept_distance, up_intercept_indexs, down_intercept_indexs = intercept_calculator.get_intercept_distance_optimized(
            main_orient_points, points
        )

        if not intercept_distance:
            return 0, 0, [], [], [], [], [], [], [], [], [], [], []

        max_intercept_distance = max(intercept_distance)
        max_index = intercept_distance.index(max_intercept_distance)

        # 获取左边和右边的截面数据
        left_orient_line = main_orient_line_dict["left"]
        left_orient_line = _get_main_orient_line_range_optimized(left_orient_line, points)
        left_orient_line = _get_main_orient_line_orient_optimized(left_orient_line)
        left_orient_points = _get_split_points_optimized(left_orient_line, interval)
        left_intercept_distance, left_up_intercept_indexs, left_down_intercept_indexs = intercept_calculator.get_intercept_distance_optimized(
            left_orient_points, points
        )

        right_orient_line = main_orient_line_dict["right"]
        right_orient_line = _get_main_orient_line_range_optimized(right_orient_line, points)
        right_orient_line = _get_main_orient_line_orient_optimized(right_orient_line)
        right_orient_points = _get_split_points_optimized(right_orient_line, interval)
        right_intercept_distance, right_up_intercept_indexs, right_down_intercept_indexs = intercept_calculator.get_intercept_distance_optimized(
            right_orient_points, points
        )

        return (max_index, max_intercept_distance, right_intercept_distance, left_intercept_distance,
                left_up_intercept_indexs, left_down_intercept_indexs, right_intercept_distance,
                right_up_intercept_indexs, right_down_intercept_indexs, up_intercept_indexs,
                down_intercept_indexs, pcamean_list, cntr)

    except Exception as e:
        raise GeometryCalculationError(f"截距计算失败: {e}")

# ==================== 辅助几何函数 ====================

def _get_line_points_optimized(points: List[Tuple[float, float]], interval: float) -> List[Tuple[float, float]]:
    """优化的线点填充"""
    try:
        fill_points = []
        for i in range(len(points)):
            cur_p = points[i]
            next_p = points[(i + 1) % len(points)]

            # 计算距离
            distance = cal_point_distance(cur_p, next_p)
            if distance > interval:
                # 需要插值
                num_segments = int(distance // interval)
                for j in range(num_segments):
                    t = j / num_segments
                    interp_x = cur_p[0] + t * (next_p[0] - cur_p[0])
                    interp_y = cur_p[1] + t * (next_p[1] - cur_p[1])
                    fill_points.append((interp_x, interp_y))
            else:
                fill_points.append(cur_p)

        return fill_points
    except Exception as e:
        logging.warning(f"线点填充失败: {e}")
        return points

def _get_main_orient_line_range_optimized(main_orient_line: List[Tuple[float, float]],
                                        points: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    """优化的主方向线范围计算"""
    try:
        if not main_orient_line or len(main_orient_line) < 2:
            return main_orient_line

        min_v = float("inf")
        max_v = float("-inf")
        min_p = None
        max_p = None

        for point in points:
            cross_point = _get_vertical_cross_point_optimized(main_orient_line, point)
            d = _cal_point_orient_distance_optimized(main_orient_line[0], main_orient_line[1], cross_point)

            if d > max_v:
                max_v = d
                max_p = cross_point
            if d < min_v:
                min_v = d
                min_p = cross_point

        return [min_p, max_p] if min_p and max_p else main_orient_line
    except Exception as e:
        logging.warning(f"主方向线范围计算失败: {e}")
        return main_orient_line

def _get_main_orient_line_orient_optimized(main_orient_line: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    """优化的主方向线方向调整"""
    try:
        if len(main_orient_line) < 2:
            return main_orient_line

        start_point, end_point = main_orient_line
        if start_point[0] > end_point[0]:
            return [end_point, start_point]
        return main_orient_line
    except Exception as e:
        logging.warning(f"主方向线方向调整失败: {e}")
        return main_orient_line

def _get_split_points_optimized(line: List[Tuple[float, float]], gap: float) -> List[Tuple[float, float]]:
    """优化的分割点计算"""
    try:
        if len(line) < 2:
            return line

        split_points = []
        start_point, end_point = line
        line_d = cal_point_distance(start_point, end_point)

        if line_d == 0:
            return [start_point]

        n = int(line_d // gap)
        for i in range(n):
            inter_d = gap * i
            t = inter_d / line_d
            x = start_point[0] + t * (end_point[0] - start_point[0])
            y = start_point[1] + t * (end_point[1] - start_point[1])
            split_points.append((x, y))

        split_points.append(end_point)
        return split_points
    except Exception as e:
        logging.warning(f"分割点计算失败: {e}")
        return line

def _get_vertical_cross_point_optimized(line: List[Tuple[float, float]],
                                      orient_point: Tuple[float, float]) -> Tuple[float, float]:
    """优化的垂直交点计算"""
    try:
        p1, p2 = line
        A = p1[1] - p2[1]
        B = p2[0] - p1[0]
        C = p1[0] * p2[1] - p2[0] * p1[1]

        x0, y0 = orient_point

        denominator = A * A + B * B
        if abs(denominator) < 1e-10:
            return orient_point

        vert_point_x = (B * B * x0 - A * B * y0 - A * C) / denominator
        vert_point_y = (-A * B * x0 + A * A * y0 - B * C) / denominator

        return (vert_point_x, vert_point_y)
    except Exception as e:
        logging.warning(f"垂直交点计算失败: {e}")
        return orient_point

def _cal_point_orient_distance_optimized(start_point: Tuple[float, float], end_point: Tuple[float, float],
                                       point: Tuple[float, float]) -> float:
    """优化的点方向距离计算"""
    try:
        x1, x2, x = start_point[0], end_point[0], point[0]
        d = cal_point_distance(start_point, point)

        if (x - x1) * (x2 - x1) < 0:
            d = -d

        return d
    except Exception as e:
        logging.warning(f"点方向距离计算失败: {e}")
        return 0.0

def _find_left_index_optimized(values: List[float], toal_value: float, max_index: int) -> int:
    """优化的左侧索引查找"""
    try:
        if not values or max_index >= len(values):
            return 0

        # 使用向量化操作查找
        values_arr = np.array(values[:max_index])

        # 查找小于阈值的位置
        valid_indices = np.where(values_arr < toal_value)[0]

        if len(valid_indices) > 0:
            return valid_indices[-1]  # 返回最后一个满足条件的索引

        return 0
    except Exception as e:
        logging.warning(f"左侧索引查找失败: {e}")
        return 0

def _find_right_index_optimized(values: List[float], toal_value: float, max_index: int) -> int:
    """优化的右侧索引查找"""
    try:
        if not values or max_index >= len(values):
            return len(values) - 1 if values else 0

        # 从最大索引后开始查找
        search_start = max_index
        values_arr = np.array(values[search_start:])

        # 查找小于阈值的位置
        valid_indices = np.where(values_arr < toal_value)[0]

        if len(valid_indices) > 0:
            return search_start + valid_indices[0]  # 返回第一个满足条件的索引

        return len(values) - 1
    except Exception as e:
        logging.warning(f"右侧索引查找失败: {e}")
        return len(values) - 1 if values else 0

# ==================== 主要业务逻辑函数 ====================

@performance_monitor
@safe_execute(max_retries=2)
def do_yejian_optimized(img_path: str, source_json_root: str, output_json_root: str) -> None:
    """
    优化的叶尖处理函数 - 替代原始的 do_yejian

    主要优化：
    1. 批量JSON处理
    2. 错误处理增强
    3. 内存管理优化
    """
    try:
        print(f'dealing with {img_path}')
        image = bf.cv2_read_file(img_path)

        # 构建JSON路径
        source_json_path = bf.pathjoin(source_json_root, bf.get_file_name(bf.rename_add_post(img_path, post='json')))
        output_json_path = bf.pathjoin(output_json_root, bf.get_file_name(bf.rename_add_post(img_path, post='json')))

        # 读取源JSON文件
        if not bf.fileexist(source_json_path):
            print(f"源JSON文件不存在: {source_json_path}")
            return

        json_data_temp = bf.load_json_dict_orig(source_json_path, encoding='utf-8-sig')
        json_data = json_data_temp.copy()

        width, height = bf.get_cv2_size(image)
        zhumai_points = find_shape(json_data, 'zhumai_zhengti')
        lunkuo_points = find_shape(json_data, 'canque_fill')

        # 检查必要的标签
        if zhumai_points is False:
            print(f"警告: 缺少 'zhumai_zhengti' 标签，跳过叶尖处理: {img_path}")
        if lunkuo_points is False:
            print(f"警告: 缺少 'canque_fill' 标签，跳过叶尖处理: {img_path}")

        if zhumai_points is not False and lunkuo_points is not False:
            # 处理主脉点
            zhumai_real_points = []
            for point in zhumai_points:
                zhumai_real_points.append([int(point[0] * width), int(point[1] * height)])

            # 计算主脉长度
            zhumai_len = 0
            for i in range(len(zhumai_real_points) - 1):
                zhumai_len += getDist_P2P(zhumai_real_points[i], zhumai_real_points[i + 1])

            print(f'zhumai_len={zhumai_len}')
            position = zhumai_len / 2
            calc_len = 0

            # 找到四分之一叶尖处主脉中心点
            main_point = None
            for index in range(len(zhumai_real_points) - 1, 1, -1):
                if calc_len >= position:
                    main_point = zhumai_real_points[index]
                    print(f'main_point={main_point}')
                    break
                calc_len += getDist_P2P(zhumai_real_points[index], zhumai_real_points[index - 1])

            if main_point is not None:
                # 处理叶尖分割
                lunkuo_points_top, lunkuo_points_bottom = _process_leaf_tip_division_optimized(
                    main_point, zhumai_real_points, lunkuo_points, width, height
                )

                # 创建形状字典
                shape_dict_top = {
                    'label': 'shang_yejian',
                    'points': lunkuo_points_top,
                    'group_id': None,
                    'shape_type': 'linestrip',
                    'flags': {}
                }
                shape_dict_bottom = {
                    'label': 'xia_yejian',
                    'points': lunkuo_points_bottom,
                    'group_id': None,
                    'shape_type': 'linestrip',
                    'flags': {}
                }

                # 更新JSON数据
                shapes = [x for x in json_data['shapes'] if
                         not (x["label"] == "shang_yejian" or x["label"] == "xia_yejian")]
                shapes.append(shape_dict_top)
                shapes.append(shape_dict_bottom)
                json_data['shapes'] = shapes

                # 保存到输出目录
                bf.save_json_dict_orig(json_data, output_json_path)
                print(f'finish! path={img_path} -> {output_json_path}')
        else:
            # 保存原始JSON文件到输出目录
            print(f"保存原始JSON文件（缺少必要标签）: {img_path} -> {output_json_path}")
            bf.save_json_dict_orig(json_data, output_json_path)

    except Exception as e:
        raise ProcessingError(f"叶尖处理失败: {e}")

def _process_leaf_tip_division_optimized(main_point: List[float], zhumai_real_points: List[List[float]],
                                       lunkuo_points: List[List[float]], width: int, height: int) -> Tuple[List, List]:
    """优化的叶尖分割处理"""
    try:
        lunkuo_points_top = []
        lunkuo_points_bottom = []

        # 计算斜率
        try:
            ind = zhumai_real_points.index(main_point)
            calc_point_index = max(0, min(int(ind - ind / 10), len(zhumai_real_points) - 1))
            calc_point_index_2 = max(0, min(int(ind + ind / 10), len(zhumai_real_points) - 1))

            if calc_point_index == calc_point_index_2:
                if calc_point_index > 0:
                    calc_point_index -= 1
                elif calc_point_index_2 < len(zhumai_real_points) - 1:
                    calc_point_index_2 += 1
                else:
                    slope_zhumai = 0.000000001
                    slope = -1 / slope_zhumai

            if calc_point_index != calc_point_index_2:
                x_diff = zhumai_real_points[calc_point_index_2][0] - zhumai_real_points[calc_point_index][0]
                if abs(x_diff) < 1e-6:
                    slope_zhumai = 999999999.9999999
                else:
                    slope_zhumai = (zhumai_real_points[calc_point_index][1] - zhumai_real_points[calc_point_index_2][1]) / x_diff

            if slope_zhumai == 0:
                slope_zhumai = 0.000000001
            slope = -1 / slope_zhumai

        except (ValueError, IndexError) as e:
            print(f'警告: 计算主脉斜率时出错: {e}，使用默认斜率')
            slope_zhumai = 0.000000001
            slope = -1 / slope_zhumai

        print(f'slope={slope}')

        # 处理轮廓点
        p_x_min = 1
        p_x_idx = 0
        for i, p in enumerate(lunkuo_points):
            if p[0] < p_x_min:
                p_x_idx = i
                p_x_min = p[0]

        # 重新排列轮廓点
        lunkuo_points_tmp = []
        for i in range(len(lunkuo_points)):
            lunkuo_points_tmp.append(lunkuo_points[p_x_idx])
            p_x_idx += 1
            if p_x_idx > len(lunkuo_points) - 1:
                p_x_idx = 0

        right_temp = (max([x[0] * width for x in lunkuo_points]) - width * 0.05) / width

        if lunkuo_points_tmp[10][1] > lunkuo_points_tmp[-10][1]:
            lunkuo_points_tmp.reverse()

        # 分割上半部分
        for p in lunkuo_points_tmp:
            point_x_float_temp = (main_point[0] + ((main_point[1] - p[1] * height) / slope)) / width
            if right_temp >= p[0] >= point_x_float_temp:
                lunkuo_points_top.append(p)
            if p[0] >= right_temp:
                break

        # 分割下半部分
        lunkuo_points_tmp.reverse()
        for p in lunkuo_points_tmp:
            point_x_float_temp = (main_point[0] + ((main_point[1] - p[1] * height) / slope)) / width
            if right_temp >= p[0] >= point_x_float_temp:
                lunkuo_points_bottom.append(p)
            if p[0] >= right_temp:
                break

        print(f'len(lunkuo_points_top)={len(lunkuo_points_top)}')
        print(f'len(lunkuo_points_bottom)={len(lunkuo_points_bottom)}')

        return lunkuo_points_top, lunkuo_points_bottom

    except Exception as e:
        logging.warning(f"叶尖分割处理失败: {e}")
        return [], []

@performance_monitor
@safe_execute(max_retries=2)
def do_one_optimized(path: str, json_path_source: str, json_path_root: str,
                    is_do_beiyong6: bool = True, is_do_yejian: bool = True) -> None:
    """
    优化的单文件处理函数 - 替代原始的 do_one

    主要优化：
    1. 使用优化的处理流程
    2. 错误处理增强
    3. 内存管理优化
    """
    try:
        if is_do_beiyong6:
            json_path = bf.pathjoin(json_path_root, bf.get_file_name(bf.rename_add_post(path, post='json')))
            print(f'dealing={path}')

            if bf.fileexist(json_path):
                json_data = bf.load_json_dict_orig(json_path, encoding='utf-8-sig')
            else:
                json_data = bf.make_labelme_json_dict(bf.get_file_name(path))

            # 使用优化的边缘裁剪
            edge_tailor_arr, canques_list, pcamean_list = get_edge_tailor_optimized(
                path, flag_show_orig_edge=False, flag_show_canque=False, flag_show_edge_tailor=False
            )

            print(f'len(edge_tailor_arr)={len(edge_tailor_arr)}')

            json_data_temp = json_data.copy()
            beiyong6_exist = find_shape(json_data_temp, 'beiyong6')
            beiyong6 = []
            biao = 0
            width, height = bf.get_cv2_size(bf.cv2_read_file(path))

            # 处理beiyong6点
            for p in edge_tailor_arr:
                if biao % 5 == 0:
                    point = p[0]
                    beiyong6.append([point[0] / width, point[1] / height])
                biao += 1

            if beiyong6_exist is not False:
                change_shape_points(json_data_temp, 'beiyong6', beiyong6)
            else:
                shape_dict_6 = {
                    'label': 'beiyong6',
                    'points': beiyong6,
                    'group_id': None,
                    'shape_type': 'polygon',
                    'flags': {}
                }
                json_data_temp['shapes'].append(shape_dict_6)

            bf.save_json_dict_orig(json_data_temp, json_path)

        if is_do_yejian:
            do_yejian_optimized(path, json_path_source, json_path_root)

    except Exception as e:
        raise ProcessingError(f"单文件处理失败: {e}")

# ==================== 主程序入口 ====================

def main():
    """优化版主入口函数"""
    print("烟叶锯齿平滑处理模块 - 优化部署程序")
    print("=" * 50)

    # 配置路径 - 使用绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.dirname(current_dir)

    json_dir = os.path.join(root_dir, "yuandu_halcon", "json_output")
    image_dir = os.path.join(root_dir, "test_images")
    output_dir = os.path.join(current_dir, "output")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    print(f"输入JSON目录: {json_dir}")
    print(f"输入图像目录: {image_dir}")
    print(f"输出目录: {output_dir}")

    # 检查图像目录是否存在
    if not os.path.exists(image_dir):
        print(f"❌ 图像目录不存在: {image_dir}")
        return

    # 获取第一个可用的测试图像
    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith('.bmp')]
    if not image_files:
        print(f"❌ 在目录 {image_dir} 中未找到BMP图像文件")
        return

    # 查找有对应JSON文件的图像
    test_image = None
    for img_file in image_files:
        json_name = img_file.replace('.bmp', '.json')
        json_path = os.path.join(json_dir, json_name)
        if os.path.exists(json_path):
            test_image = img_file
            break

    # 如果没有找到有JSON的图像，使用第一个图像
    if test_image is None:
        test_image = image_files[0]
        print(f"⚠️ 未找到对应的JSON文件，将使用默认处理: {test_image}")

    image_path = os.path.join(image_dir, test_image)

    print(f"\n开始处理测试文件: {test_image}")

    try:
        # 记录开始时间
        start_time = time.time()

        # 调用优化的主处理函数
        do_one_optimized(image_path, json_dir, output_dir, is_do_beiyong6=False, is_do_yejian=True)

        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time

        print(f"✅ 处理成功: {test_image}")
        print(f"⏱️ 处理耗时: {processing_time:.2f}秒")

        # 显示性能统计
        _show_performance_stats()

    except Exception as e:
        print(f"❌ 处理失败: {test_image}, 错误: {e}")
        traceback.print_exc()

    print("\n处理完成!")

def batch_process_optimized():
    """优化的批量处理函数"""
    print("烟叶锯齿平滑处理模块 - 优化批量处理")
    print("=" * 50)

    # 配置路径
    image_dir = "coderafactor/test_images"
    output_dir = "coderafactor/dealing_images_juchi/output"

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有图像文件
    if not os.path.exists(image_dir):
        print(f"图像目录不存在: {image_dir}")
        return

    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.bmp', '.jpg', '.jpeg', '.png'))]

    if not image_files:
        print(f"在目录 {image_dir} 中未找到图像文件")
        return

    print(f"找到 {len(image_files)} 个图像文件")

    success_count = 0
    start_time = time.time()

    for i, image_file in enumerate(image_files, 1):
        image_path = os.path.join(image_dir, image_file)
        print(f"\n[{i}/{len(image_files)}] 处理: {image_file}")

        try:
            # 调用优化的主处理函数
            do_one_optimized(image_path, output_dir, is_do_beiyong6=False, is_do_yejian=True)
            success_count += 1
            print(f"✅ 处理成功")
        except Exception as e:
            print(f"❌ 处理失败: {e}")

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n批量处理完成!")
    print(f"总文件数: {len(image_files)}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {len(image_files) - success_count}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均耗时: {total_time/len(image_files):.2f}秒/文件")

    # 显示性能统计
    _show_performance_stats()

def _show_performance_stats():
    """显示性能统计信息"""
    try:
        print("\n📊 性能统计:")
        print(f"PCA缓存命中数: {len(pca_cache.cache)}")
        print(f"内存池当前大小: {memory_pool.current_size / 1024 / 1024:.2f}MB")

        # 清理缓存
        if len(pca_cache.cache) > 500:
            pca_cache.clear()
            print("🧹 已清理PCA缓存")

        if memory_pool.current_size > 512 * 1024 * 1024:  # 512MB
            memory_pool.clear()
            print("🧹 已清理内存池")

    except Exception as e:
        logging.warning(f"性能统计显示失败: {e}")

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dealing_images_juchi_optimized.log'),
            logging.StreamHandler()
        ]
    )

    main()

# ==================== 优化的主处理器 ====================

class OptimizedMainProcessor:
    """优化的主处理器 - 整合所有优化组件"""

    def __init__(self, config: OptimizedProcessingConfig):
        self.config = config
        self.image_processor = OptimizedImageProcessor(config)
        self.geometry = VectorizedGeometry()

    @performance_monitor
    @safe_execute(max_retries=2)
    def process_optimized(self, path: Optional[str] = None, img_cv2: Optional[np.ndarray] = None,
                         red_filter: bool = False, flag_show: bool = True) -> Tuple[np.ndarray, List, List]:
        """
        优化的主处理函数 - 替代原始的 process

        主要优化：
        1. 内存池管理
        2. 向量化计算
        3. 并行处理
        4. 缓存机制
        """
        try:
            # 读取图像
            if path is None:
                image = img_cv2
            else:
                image = cv2.imread(path)

            if image is None:
                raise ImageProcessingError("无法读取图像")

            # 二值化处理
            binary = self.image_processor.cal_binary_image_optimized(image, red_filter)
            binary = bf.cv2_erode(binary, iterations=0)

            # 边缘和残缺提取
            edge, canques_list = self.image_processor.get_edge_canques_optimized(binary, flag_show)

            # 计算图像尺寸
            height, width = binary.shape[:2]

            # 优化的中心点计算
            center_point_list = self._calculate_center_points_optimized(
                binary, width, height, image if img_cv2 is not None else None
            )

            return edge, canques_list, center_point_list

        except Exception as e:
            raise ProcessingError(f"主处理失败: {e}")

    def _calculate_center_points_optimized(self, binary: np.ndarray, width: int, height: int,
                                         display_image: Optional[np.ndarray] = None) -> List[List[float]]:
        """优化的中心点计算"""
        try:
            interval_width = int(width / self.config.split_amt)
            background_width = interval_width + 2 * self.config.two_side_pixel_amt

            # 使用内存池创建背景图像
            img_background = self.memory_pool.get_array((height, background_width), dtype=np.uint8)
            img_background.fill(0)

            center_point_list = []
            split_area = interval_width * height

            for i in range(self.config.split_amt):
                left = i * interval_width
                top = 0

                # 裁剪图像区域
                binary_crop = binary[top:top+height, left:left+interval_width]

                # 计算活跃区域比例
                active_area = np.sum(binary_crop) / 255
                active_area_rate = active_area / split_area

                if active_area_rate > self.config.active_area_rate_lower:
                    # 重置背景图像
                    img_background.fill(0)

                    # 粘贴裁剪图像
                    paste_region = img_background[top:top+height,
                                                self.config.two_side_pixel_amt:self.config.two_side_pixel_amt+interval_width]
                    paste_region[:] = binary_crop

                    # 计算中心点
                    cx_tmp, cy_tmp = bf.cv2_img_center_moments(img_background)
                    cx = cx_tmp + left - self.config.two_side_pixel_amt
                    cy = cy_tmp + top
                    center_point_list.append([cx, cy])

                    # 可选的可视化
                    if display_image is not None and self.config.flag_show:
                        bf.cv2_circle(display_image, cx, cy)

            # 移除第一个点（通常是边界点）
            if center_point_list:
                center_point_list.pop(0)

            return center_point_list

        except Exception as e:
            raise GeometryCalculationError(f"中心点计算失败: {e}")

# ==================== 兼容性函数 - 保持原始接口 ====================

# 全局配置实例
global_config = OptimizedProcessingConfig()
global_processor = OptimizedMainProcessor(global_config)

def find_shape(json_data: Dict, shape_name: str) -> Union[List, bool]:
    """查找形状 - 保持原始接口"""
    try:
        for item in json_data['shapes']:
            if item['label'] == shape_name:
                return item['points']
        return False
    except Exception:
        return False

def getDist_P2P(Point0: Tuple[float, float], PointA: Tuple[float, float]) -> float:
    """优化的点距离计算 - 保持原始接口"""
    return VectorizedGeometry.cached_distance(tuple(Point0), tuple(PointA))

def cal_point_distance(p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
    """优化的点距离计算 - 保持原始接口"""
    return VectorizedGeometry.cached_distance(tuple(p1), tuple(p2))

def change_shape_points(json_data: Dict, shape_name: str, points: List) -> None:
    """修改形状点 - 保持原始接口"""
    try:
        for item in json_data['shapes']:
            if item['label'] == shape_name:
                item['points'] = points
                break
    except Exception as e:
        raise JSONProcessingError(f"修改形状点失败: {e}")

def cal_binary_image2(image: np.ndarray, red_filter: bool = False) -> np.ndarray:
    """优化的二值化图像计算 - 保持原始接口"""
    return global_processor.image_processor.cal_binary_image_optimized(image, red_filter)

def get_edge_canques(binary: np.ndarray, flag_show: bool = True) -> Tuple[np.ndarray, List]:
    """优化的边缘残缺提取 - 保持原始接口"""
    return global_processor.image_processor.get_edge_canques_optimized(binary, flag_show)

def process(path: Optional[str] = None, img_cv2: Optional[np.ndarray] = None,
           red_filter: bool = False, flag_show: bool = True, **kwargs) -> Tuple[np.ndarray, List, List]:
    """优化的主处理函数 - 保持原始接口"""
    return global_processor.process_optimized(path, img_cv2, red_filter, flag_show)
