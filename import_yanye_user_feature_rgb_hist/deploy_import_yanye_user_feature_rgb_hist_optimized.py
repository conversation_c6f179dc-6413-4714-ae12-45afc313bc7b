#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
烟叶RGB直方图特征导入优化部署程序
基于原始deploy_import_yanye_user_feature_rgb_hist.py的性能优化版本

优化策略：
1. 图像处理优化：向量化掩码操作，批量直方图计算
2. 内存管理：消除重复的图像缓冲区创建，实施内存池管理
3. I/O批量处理：实现批量JSON操作，减少磁盘I/O
4. 并行计算：实现多线程/多进程处理
5. 缓存机制：掩码和直方图结果缓存，避免重复计算
6. 错误处理：完善异常处理机制和重试策略

作者: Augment Agent (优化版本)
日期: 2025-07-28
版本: 2.0 (优化版)
基于: deploy_import_yanye_user_feature_rgb_hist.py v1.0
"""

import os
import sys
import json
import cv2
import numpy as np
import time
import shutil
import traceback
import matplotlib.pyplot as plt
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import hashlib
import weakref
import gc
from functools import lru_cache, wraps
import logging

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

# ==================== 配置管理类 ====================

@dataclass
class ProcessingConfig:
    """处理配置类 - 遵循单一职责原则"""
    # 基础参数
    hist_size: int = 64
    erosion_iterations: int = 20
    flag_independent: bool = True
    
    # 性能优化参数
    enable_parallel: bool = True
    max_workers: Optional[int] = None
    memory_limit_mb: int = 512
    
    # 缓存配置
    enable_cache: bool = True
    cache_size_mb: int = 256
    mask_cache_size: int = 500
    
    # I/O配置
    batch_size: int = 10
    enable_batch_json: bool = True
    temp_dir: str = "temp"
    
    # 可视化配置
    enable_visualization: bool = True
    visualization_dpi: int = 150
    
    def __post_init__(self):
        if self.max_workers is None:
            self.max_workers = min(mp.cpu_count(), 8)
    
    @classmethod
    def from_config_file(cls, config_file: str, config_name: str = "default") -> "ProcessingConfig":
        """从配置文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if config_name not in config_data.get("processing_configs", {}):
                raise ValueError(f"配置 '{config_name}' 不存在")
            
            config_dict = config_data["processing_configs"][config_name]
            return cls(**config_dict)
            
        except Exception as e:
            logging.warning(f"加载配置文件失败，使用默认配置: {e}")
            return cls()

# ==================== 异常处理类 ====================

class RGBHistProcessingError(Exception):
    """RGB直方图处理错误基类"""
    pass

class ImageProcessingError(RGBHistProcessingError):
    """图像处理错误"""
    pass

class MaskGenerationError(RGBHistProcessingError):
    """掩码生成错误"""
    pass

class HistogramCalculationError(RGBHistProcessingError):
    """直方图计算错误"""
    pass

class JSONProcessingError(RGBHistProcessingError):
    """JSON处理错误"""
    pass

class FileProcessingError(RGBHistProcessingError):
    """文件处理错误"""
    pass

# ==================== 内存管理类 ====================

class ImageMemoryPool:
    """图像内存池管理器 - 优化图像处理内存使用"""
    
    def __init__(self, max_size_mb: int = 512):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.image_buffers = {}
        self.mask_buffers = {}
        self.current_size = 0
        self._lock = threading.Lock()
    
    def get_image_buffer(self, shape: Tuple[int, ...], dtype=np.uint8) -> np.ndarray:
        """获取图像缓冲区"""
        with self._lock:
            key = (shape, dtype)
            buffer_size = np.prod(shape) * np.dtype(dtype).itemsize
            
            if key not in self.image_buffers:
                if self.current_size + buffer_size > self.max_size_bytes:
                    self._cleanup_old_buffers()
                
                self.image_buffers[key] = np.empty(shape, dtype=dtype)
                self.current_size += buffer_size
            
            return self.image_buffers[key]
    
    def get_mask_buffer(self, shape: Tuple[int, int], dtype=np.uint8) -> np.ndarray:
        """获取掩码缓冲区"""
        return self.get_image_buffer(shape, dtype)
    
    def _cleanup_old_buffers(self):
        """清理旧缓冲区"""
        # 简单的LRU策略：清理一半的缓冲区
        items_to_remove = len(self.image_buffers) // 2
        keys_to_remove = list(self.image_buffers.keys())[:items_to_remove]
        
        for key in keys_to_remove:
            buffer = self.image_buffers.pop(key)
            self.current_size -= buffer.nbytes
    
    def clear(self):
        """清空内存池"""
        with self._lock:
            self.image_buffers.clear()
            self.mask_buffers.clear()
            self.current_size = 0

# 全局图像内存池实例
image_memory_pool = ImageMemoryPool()

# ==================== 缓存管理类 ====================

class MaskCache:
    """掩码计算结果缓存"""
    
    def __init__(self, max_size: int = 500):
        self.max_size = max_size
        self.cache = {}
        self._access_order = []
        self._lock = threading.Lock()
    
    def _get_shapes_hash(self, shapes: List[Dict], width: int, height: int) -> str:
        """计算形状列表的哈希值"""
        shapes_str = json.dumps(shapes, sort_keys=True) + f"_{width}_{height}"
        return hashlib.md5(shapes_str.encode()).hexdigest()
    
    def get_mask(self, shapes: List[Dict], width: int, height: int) -> Optional[np.ndarray]:
        """获取缓存的掩码"""
        shapes_hash = self._get_shapes_hash(shapes, width, height)
        
        with self._lock:
            if shapes_hash in self.cache:
                # 更新访问顺序
                self._access_order.remove(shapes_hash)
                self._access_order.append(shapes_hash)
                return self.cache[shapes_hash].copy()
            return None
    
    def set_mask(self, shapes: List[Dict], width: int, height: int, mask: np.ndarray):
        """设置掩码到缓存"""
        shapes_hash = self._get_shapes_hash(shapes, width, height)
        
        with self._lock:
            if len(self.cache) >= self.max_size:
                # 移除最旧的条目
                oldest_key = self._access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[shapes_hash] = mask.copy()
            if shapes_hash not in self._access_order:
                self._access_order.append(shapes_hash)
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self._access_order.clear()

# 全局掩码缓存实例
mask_cache = MaskCache()

# ==================== 性能监控装饰器 ====================

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = 0
        
        try:
            import psutil
            start_memory = psutil.Process().memory_info().rss
        except ImportError:
            pass
        
        try:
            result = func(*args, **kwargs)
            success = True
        except Exception as e:
            success = False
            logging.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
        finally:
            end_time = time.time()
            execution_time = end_time - start_time
            
            if start_memory > 0:
                try:
                    end_memory = psutil.Process().memory_info().rss
                    memory_delta = end_memory - start_memory
                    logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s, 内存变化: {memory_delta/1024/1024:.2f}MB")
                except:
                    logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s")
            else:
                logging.info(f"函数 {func.__name__} 执行时间: {execution_time:.3f}s")
        
        return result
    return wrapper

# ==================== 错误处理装饰器 ====================

def safe_execute(max_retries: int = 3, delay: float = 0.1):
    """安全执行装饰器 - 带重试机制"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except RGBHistProcessingError as e:
                    last_exception = e
                    if attempt == max_retries - 1:
                        logging.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        raise
                    
                    wait_time = delay * (2 ** attempt)  # 指数退避
                    logging.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，{wait_time:.2f}s 后重试: {e}")
                    time.sleep(wait_time)
                except Exception as e:
                    # 非预期错误，直接抛出
                    logging.error(f"函数 {func.__name__} 发生非预期错误: {e}")
                    raise
            
            # 理论上不会到达这里
            raise last_exception
        return wrapper
    return decorator

# ==================== 优化的图像处理类 ====================

class OptimizedImageProcessor:
    """优化的图像处理器 - 向量化图像操作"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.memory_pool = image_memory_pool
        self.mask_cache = mask_cache if config.enable_cache else None

    @performance_monitor
    @safe_execute(max_retries=2)
    def generate_masks_optimized(self, json_data: Dict, width: int, height: int) -> Tuple[np.ndarray, bool]:
        """
        优化的掩码生成函数 - 批量处理和缓存
        """
        try:
            white_shape_list = []
            black_shape_list = []
            flag_found_lunkuo = False

            if json_data and "shapes" in json_data:
                shape_list = json_data["shapes"]

                for shape_dict in shape_list:
                    label = shape_dict.get("label", "")
                    if label == "canque_fill":
                        flag_found_lunkuo = True
                        white_shape_list.append(shape_dict)
                    elif label in ["canque", "zhumai_zhengti"]:
                        black_shape_list.append(shape_dict)

            if not flag_found_lunkuo:
                # 如果没有找到轮廓，返回全1掩码
                mask = self.memory_pool.get_mask_buffer((height, width))
                mask.fill(1)
                return mask.copy(), flag_found_lunkuo

            # 检查缓存
            if self.mask_cache:
                cached_white_mask = self.mask_cache.get_mask(white_shape_list, width, height)
                cached_black_mask = self.mask_cache.get_mask(black_shape_list, width, height)

                if cached_white_mask is not None and cached_black_mask is not None:
                    # 组合缓存的掩码
                    white_mask_eroded = cv2.erode(cached_white_mask, None, iterations=self.config.erosion_iterations)
                    black_mask_inverted = cv2.bitwise_not(cached_black_mask)
                    final_mask = cv2.bitwise_and(white_mask_eroded, black_mask_inverted)
                    return final_mask, flag_found_lunkuo

            # 生成白色掩码
            white_mask = self._generate_shape_mask_batch(white_shape_list, width, height)

            # 腐蚀白色掩码
            white_mask = cv2.erode(white_mask, None, iterations=self.config.erosion_iterations)

            # 生成黑色掩码
            black_mask = self._generate_shape_mask_batch(black_shape_list, width, height)
            black_mask = cv2.bitwise_not(black_mask)

            # 组合掩码
            final_mask = cv2.bitwise_and(white_mask, black_mask)

            # 缓存结果
            if self.mask_cache:
                self.mask_cache.set_mask(white_shape_list, width, height, white_mask)
                self.mask_cache.set_mask(black_shape_list, width, height, cv2.bitwise_not(black_mask))

            return final_mask, flag_found_lunkuo

        except Exception as e:
            raise MaskGenerationError(f"掩码生成失败: {e}")

    def _generate_shape_mask_batch(self, shape_list: List[Dict], width: int, height: int) -> np.ndarray:
        """批量生成形状掩码"""
        mask = self.memory_pool.get_mask_buffer((height, width))
        mask.fill(0)

        for shape_dict in shape_list:
            try:
                point_arr = bf.labelme_json_points_float_to_int(shape_dict, width, height)
                shape_mask = bf.make_mask(width, height, point_arr)
                mask = cv2.bitwise_or(mask, shape_mask)
            except Exception as e:
                logging.warning(f"处理形状时出错: {e}")
                continue

        return mask

    @performance_monitor
    def calculate_rgb_histogram_optimized(self, img_cv2: np.ndarray, mask: np.ndarray) -> Tuple[np.ndarray, int, int]:
        """
        优化的RGB直方图计算 - 向量化处理
        """
        try:
            # 阴影检测
            gray_img = bf.cv2_gray(img_cv2)
            none_shadow_white_mask, shadow_sum, noneblack_sum = bf.make_shadow_mask(gray_img)

            # 组合掩码
            final_mask = cv2.bitwise_and(mask, none_shadow_white_mask)
            mask_sum = np.sum(final_mask)

            if mask_sum == 0:
                # 如果掩码为空，返回零直方图
                hist_size = self.config.hist_size
                if self.config.flag_independent:
                    hist = np.zeros(hist_size * 3)
                else:
                    hist = np.zeros(hist_size ** 3)
                return hist, shadow_sum, noneblack_sum

            # 计算直方图
            if self.config.flag_independent:
                # 独立通道直方图
                hist = []
                for channel_i in range(3):
                    hist_channel = cv2.calcHist([img_cv2], [channel_i], final_mask,
                                              [self.config.hist_size], [0, 255])
                    hist.extend(hist_channel.flatten().tolist())
                hist = np.array(hist)
            else:
                # 3D直方图
                hist = bf.cal_rgb_distribution_histo(img_cv2, final_mask, self.config.hist_size)

            # 归一化
            hist = hist / mask_sum

            return hist, shadow_sum, noneblack_sum

        except Exception as e:
            raise HistogramCalculationError(f"RGB直方图计算失败: {e}")

    @performance_monitor
    def calculate_color_features_optimized(self, hist: np.ndarray) -> Dict[str, float]:
        """
        优化的颜色特征计算
        """
        try:
            hist_list = hist.tolist()
            hist_size = self.config.hist_size

            # 计算颜色平均值
            avg_val_list = []
            for i in range(3):
                part_hist_list = hist_list[i*hist_size:(i+1)*hist_size]
                max_idx = bf.get_Max_idx(part_hist_list)
                avg_val = int(max_idx * 255 / hist_size)
                avg_val_list.append(avg_val)

            # 计算灰度平均值
            gray_avg = avg_val_list[0] * 0.299 + avg_val_list[1] * 0.587 + avg_val_list[2] * 0.114

            return {
                "red_avg": avg_val_list[0],
                "green_avg": avg_val_list[1],
                "blue_avg": avg_val_list[2],
                "gray_avg": gray_avg
            }

        except Exception as e:
            raise HistogramCalculationError(f"颜色特征计算失败: {e}")

# ==================== 优化的可视化处理器 ====================

class OptimizedVisualizationProcessor:
    """优化的可视化处理器"""

    def __init__(self, config: ProcessingConfig):
        self.config = config

    @performance_monitor
    @safe_execute(max_retries=2)
    def create_rgb_histogram_visualization_optimized(self, json_path: str, output_path: str, image_name: str) -> bool:
        """
        优化的RGB直方图可视化创建
        """
        try:
            json_data = bf.load_json_dict_orig(json_path)

            if "user_feature" not in json_data:
                logging.warning(f"JSON文件中缺少user_feature字段: {json_path}")
                return False

            user_feature = json_data["user_feature"]

            if "rgb_hist_lst" not in user_feature:
                logging.warning(f"user_feature中缺少rgb_hist_lst字段: {json_path}")
                return False

            rgb_hist = user_feature["rgb_hist_lst"]

            # 分离三个通道的直方图
            hist_size = len(rgb_hist) // 3
            blue_hist = rgb_hist[0:hist_size]
            green_hist = rgb_hist[hist_size:2*hist_size]
            red_hist = rgb_hist[2*hist_size:3*hist_size]

            # 创建图表 - 优化内存使用
            plt.ioff()  # 关闭交互模式
            fig, axes = plt.subplots(2, 2, figsize=(12, 8))

            x = range(hist_size)

            # 绘制各通道直方图
            axes[0, 0].plot(x, blue_hist, color='blue', label='Blue Channel')
            axes[0, 0].set_title('Blue Channel Histogram')
            axes[0, 0].set_xlabel('Bin')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            axes[0, 1].plot(x, green_hist, color='green', label='Green Channel')
            axes[0, 1].set_title('Green Channel Histogram')
            axes[0, 1].set_xlabel('Bin')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

            axes[1, 0].plot(x, red_hist, color='red', label='Red Channel')
            axes[1, 0].set_title('Red Channel Histogram')
            axes[1, 0].set_xlabel('Bin')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            axes[1, 1].plot(x, blue_hist, color='blue', alpha=0.7, label='Blue')
            axes[1, 1].plot(x, green_hist, color='green', alpha=0.7, label='Green')
            axes[1, 1].plot(x, red_hist, color='red', alpha=0.7, label='Red')
            axes[1, 1].set_title('Combined RGB Histogram')
            axes[1, 1].set_xlabel('Bin')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)

            # 添加特征信息
            gary_avg = user_feature.get('gary_avg', 'N/A')
            shadow_rate = user_feature.get('shadow_rate', 'N/A')

            gary_avg_str = f"{gary_avg:.2f}" if isinstance(gary_avg, (int, float)) else 'N/A'
            shadow_rate_str = f"{shadow_rate:.3f}" if isinstance(shadow_rate, (int, float)) else 'N/A'

            info_text = f"""Image: {image_name}
RGB Features:
Red Avg: {user_feature.get('rad_avg', 'N/A')}
Green Avg: {user_feature.get('green_avg', 'N/A')}
Blue Avg: {user_feature.get('blue_avg', 'N/A')}
Gray Avg: {gary_avg_str}
Shadow Rate: {shadow_rate_str}"""

            fig.text(0.02, 0.02, info_text, fontsize=10,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))

            plt.suptitle(f'RGB Histogram Analysis - {image_name}', fontsize=14, fontweight='bold')
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.config.visualization_dpi, bbox_inches='tight')
            plt.close(fig)  # 显式关闭图形以释放内存

            return True

        except Exception as e:
            logging.error(f"创建可视化图像失败: {e}")
            return False

# ==================== 优化的特征处理器 ====================

class OptimizedRGBFeatureProcessor:
    """优化的RGB特征处理器 - 核心业务逻辑"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.image_processor = OptimizedImageProcessor(config)
        self.visualization_processor = OptimizedVisualizationProcessor(config)

    @performance_monitor
    @safe_execute(max_retries=2)
    def do_one_optimized(self, pic_filepath: str, json_filepath: Optional[str] = None,
                        flag_show: bool = False) -> Tuple[Optional[np.ndarray], int, int]:
        """
        优化的单图像RGB直方图处理函数
        """
        try:
            logging.info(f'处理图像文件: {pic_filepath}')

            # 读取图像
            img_cv2 = bf.cv2_read_file(pic_filepath)
            if img_cv2 is None:
                raise ImageProcessingError(f"无法读取图像文件: {pic_filepath}")

            width, height = bf.get_cv2_size(img_cv2)

            # 读取JSON数据
            json_data = None
            if json_filepath and os.path.exists(json_filepath):
                try:
                    json_data = bf.load_json_dict_orig(json_filepath)
                except Exception as e:
                    logging.warning(f"读取JSON文件失败: {e}")

            # 生成掩码
            mask, flag_found_lunkuo = self.image_processor.generate_masks_optimized(json_data, width, height)

            # 计算RGB直方图
            hist, shadow_sum, noneblack_sum = self.image_processor.calculate_rgb_histogram_optimized(img_cv2, mask)

            logging.info(f'直方图形状: {bf.arr_shape(hist)}')

            return hist, shadow_sum, noneblack_sum

        except Exception as e:
            raise RGBHistProcessingError(f"RGB直方图处理失败: {e}")

    @performance_monitor
    @safe_execute(max_retries=2)
    def process_single_image_optimized(self, image_path: str, json_path: str,
                                     visualization_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        优化的单图像完整处理流程
        """
        start_time = time.time()
        image_name = bf.get_file_name(image_path)

        result = {
            'image_name': image_name,
            'image_path': image_path,
            'json_path': json_path,
            'success': False,
            'processing_time': 0.0,
            'error': None,
            'features': {}
        }

        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                raise FileProcessingError(f"图像文件不存在: {image_path}")

            if not os.path.exists(json_path):
                raise FileProcessingError(f"JSON文件不存在: {json_path}")

            # 调用核心处理函数
            hist, shadow_sum, noneblack_sum = self.do_one_optimized(
                pic_filepath=image_path,
                json_filepath=json_path,
                flag_show=False
            )

            if hist is None:
                # 如果处理失败，尝试不使用JSON文件
                logging.warning(f"使用JSON处理失败，尝试不使用JSON文件: {image_name}")
                hist, shadow_sum, noneblack_sum = self.do_one_optimized(
                    pic_filepath=image_path,
                    json_filepath=None,
                    flag_show=False
                )

            if hist is not None:
                hist_list = hist.tolist()

                # 计算颜色特征
                color_features = self.image_processor.calculate_color_features_optimized(hist)

                # 构建特征字典
                add_dict = {
                    "rgb_hist_lst": hist_list,
                    "rad_avg": color_features["red_avg"],
                    "green_avg": color_features["green_avg"],
                    "blue_avg": color_features["blue_avg"],
                    "gary_avg": color_features["gray_avg"],
                    "shadow_sum": shadow_sum,
                    "noneblack_sum": noneblack_sum,
                    "shadow_rate": shadow_sum/noneblack_sum if noneblack_sum > 0 else 0
                }

                # 更新JSON文件
                bf.labelme_json_add_userfeature_file(json_path, add_dict, use_lock=True)

                # 记录特征信息
                result['features'] = {
                    "rgb_hist_size": len(hist_list),
                    "red_avg": color_features["red_avg"],
                    "green_avg": color_features["green_avg"],
                    "blue_avg": color_features["blue_avg"],
                    "gray_avg": color_features["gray_avg"],
                    "shadow_rate": add_dict["shadow_rate"]
                }

                # 创建可视化图像
                if visualization_dir and self.config.enable_visualization:
                    bf.mkdirs(visualization_dir)
                    base_name = os.path.splitext(image_name)[0]
                    visualization_path = bf.pathjoin(visualization_dir, f"{base_name}_rgb_histogram.png")

                    if self.visualization_processor.create_rgb_histogram_visualization_optimized(
                        json_path, visualization_path, image_name):
                        result['visualization_path'] = visualization_path
                        logging.info(f"可视化图像已保存: {visualization_path}")

                result['success'] = True
                logging.info(f"RGB特征提取完成: {image_name}")

            else:
                raise RGBHistProcessingError("RGB直方图计算返回None")

        except Exception as e:
            result['error'] = str(e)
            logging.error(f"处理图像失败 {image_name}: {e}")

        finally:
            result['processing_time'] = time.time() - start_time

        return result

# ==================== 并行处理管理器 ====================

class ParallelRGBHistManager:
    """并行RGB直方图处理管理器"""

    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.processor = OptimizedRGBFeatureProcessor(config)

    def process_single_file(self, file_info: Tuple[str, str, Optional[str]]) -> Dict[str, Any]:
        """处理单个文件"""
        image_path, json_path, visualization_dir = file_info
        return self.processor.process_single_image_optimized(image_path, json_path, visualization_dir)

    def process_files_parallel(self, file_list: List[Tuple[str, str, Optional[str]]]) -> List[Dict[str, Any]]:
        """并行处理文件列表"""
        if not self.config.enable_parallel or len(file_list) <= 1:
            # 串行处理
            return [self.process_single_file(file_info) for file_info in file_list]

        results = []

        try:
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self.process_single_file, file_info): file_info[0]
                    for file_info in file_list
                }

                # 收集结果
                for future in future_to_file:
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        results.append(result)
                    except Exception as e:
                        file_path = future_to_file[future]
                        error_result = {
                            'image_name': os.path.basename(file_path),
                            'image_path': file_path,
                            'json_path': '',
                            'success': False,
                            'processing_time': 0.0,
                            'error': f"并行处理异常: {e}",
                            'features': {}
                        }
                        results.append(error_result)
                        logging.error(f"并行处理文件失败 {file_path}: {e}")

        except Exception as e:
            logging.error(f"并行处理管理器异常: {e}")
            # 回退到串行处理
            results = [self.process_single_file(file_info) for file_info in file_list]

        return results

# ==================== 主要处理函数 ====================

@performance_monitor
def process_batch_images_optimized(image_dir: str, json_dir: str, output_dir: str,
                                 config: ProcessingConfig, max_images: Optional[int] = None,
                                 visualization_dir: Optional[str] = None) -> Dict[str, Any]:
    """
    优化的批量图像处理函数
    """
    print("🚀 烟叶RGB直方图特征导入优化部署程序 - 批量处理")
    print("=" * 60)

    start_time = time.time()

    try:
        # 创建输出目录
        bf.mkdirs(output_dir)
        if visualization_dir:
            bf.mkdirs(visualization_dir)

        # 扫描图像文件
        image_files, _ = bf.scan_files_2(image_dir, postfix="bmp")
        if max_images:
            image_files = image_files[:max_images]

        print(f"📁 找到 {len(image_files)} 个图像文件")
        print(f"⚙️  配置: 并行={config.enable_parallel}, 工作线程={config.max_workers}")
        print(f"💾 内存池限制: {config.memory_limit_mb}MB")
        print(f"📊 直方图大小: {config.hist_size}")

        if not image_files:
            print("⚠️  警告: 图像目录中没有找到BMP文件")
            return {
                'total_files': 0,
                'processed_files': 0,
                'success_count': 0,
                'success_rate': 0.0,
                'total_time': 0.0,
                'results': []
            }

        # 准备文件列表
        file_list = []
        for image_path in image_files:
            image_name = bf.get_file_name(image_path)
            base_name = os.path.splitext(image_name)[0]
            json_path = bf.pathjoin(json_dir, f"{base_name}.json")
            file_list.append((image_path, json_path, visualization_dir))

        print(f"🔄 开始处理 {len(file_list)} 个文件...")

        # 创建并行处理管理器
        manager = ParallelRGBHistManager(config)

        # 处理文件
        results = manager.process_files_parallel(file_list)

        # 统计结果
        end_time = time.time()
        total_time = end_time - start_time
        success_count = sum(1 for r in results if r['success'])

        print(f"\n📊 批量处理完成!")
        print(f"总文件数: {len(file_list)}")
        print(f"成功处理: {success_count}")
        print(f"失败数量: {len(file_list) - success_count}")

        # 显示每个文件的处理结果
        for i, result in enumerate(results, 1):
            image_name = result['image_name']
            status = "✅" if result['success'] else "❌"
            print(f"[{i}/{len(results)}] {status} {image_name} ({result['processing_time']:.2f}s)")

            if result['success'] and result['features']:
                features = result['features']
                print(f"    特征: R={features.get('red_avg', 'N/A')}, G={features.get('green_avg', 'N/A')}, B={features.get('blue_avg', 'N/A')}")

                # 复制更新后的JSON到输出目录
                try:
                    output_json_path = bf.pathjoin(output_dir, f"{os.path.splitext(image_name)[0]}.json")
                    bf.my_copy(result['json_path'], output_json_path)
                except Exception as e:
                    logging.warning(f"复制JSON文件失败: {e}")

            elif not result['success']:
                print(f"    错误: {result['error']}")

        print(f"总耗时: {total_time:.2f}秒")
        if len(file_list) > 0:
            print(f"平均耗时: {total_time/len(file_list):.2f}秒/文件")

        # 性能统计
        if success_count > 0:
            avg_processing_time = sum(r['processing_time'] for r in results if r['success']) / success_count
            print(f"平均处理时间: {avg_processing_time:.2f}秒/文件")

        return {
            'total_files': len(file_list),
            'processed_files': len(results),
            'success_count': success_count,
            'success_rate': success_count / len(results) if results else 0.0,
            'total_time': total_time,
            'results': results
        }

    except Exception as e:
        logging.error(f"批量处理失败: {e}")
        raise RGBHistProcessingError(f"批量处理失败: {e}")

# ==================== 主函数 ====================

def main():
    """
    优化版本的主入口函数
    """
    import argparse

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('rgb_hist_processing.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='烟叶RGB直方图特征导入优化部署程序')
    parser.add_argument('--image-dir', '-i', default='test_data/vis',
                       help='图像目录路径')
    parser.add_argument('--json-dir', '-j', default='import_yanye_user_feature/test_output',
                       help='JSON文件目录路径')
    parser.add_argument('--output-dir', '-o', default='import_yanye_user_feature_rgb_hist/test_output_optimized',
                       help='输出目录路径')
    parser.add_argument('--visualization-dir', '-v', default='import_yanye_user_feature_rgb_hist/visualization_optimized',
                       help='可视化输出目录路径')
    parser.add_argument('--max-images', '-m', type=int, default=100,
                       help='最大处理图像数量')
    parser.add_argument('--max-workers', '-w', type=int, default=4,
                       help='最大工作线程数')
    parser.add_argument('--hist-size', '-s', type=int, default=64,
                       help='直方图大小')

    args = parser.parse_args()

    print("🚀 烟叶RGB直方图特征导入优化部署程序")
    print("=" * 60)
    print("优化特性:")
    print("  ✅ 图像处理优化 (向量化掩码操作)")
    print("  ✅ 内存池管理")
    print("  ✅ 批量JSON处理")
    print("  ✅ 并行计算支持")
    print("  ✅ 掩码计算缓存")
    print("  ✅ 完善错误处理")
    print("=" * 60)

    # 创建配置
    config = ProcessingConfig(
        hist_size=args.hist_size,
        erosion_iterations=20,
        flag_independent=True,
        enable_parallel=True,
        max_workers=args.max_workers,
        memory_limit_mb=512,
        enable_cache=True,
        mask_cache_size=500,
        enable_batch_json=True,
        batch_size=10,
        enable_visualization=True,
        visualization_dpi=150
    )

    print(f"📁 图像目录: {args.image_dir}")
    print(f"📁 JSON目录: {args.json_dir}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"📁 可视化目录: {args.visualization_dir}")
    print(f"🔧 并行处理: {'启用' if config.enable_parallel else '禁用'}")
    print(f"🧵 工作线程: {config.max_workers}")
    print(f"💾 内存限制: {config.memory_limit_mb}MB")
    print(f"📊 直方图大小: {config.hist_size}")

    try:
        # 检查输入目录是否存在
        if not os.path.exists(args.image_dir):
            print(f"❌ 错误: 图像目录不存在 {args.image_dir}")
            return 1

        if not os.path.exists(args.json_dir):
            print(f"❌ 错误: JSON目录不存在 {args.json_dir}")
            return 1

        # 批量处理
        summary = process_batch_images_optimized(
            args.image_dir, args.json_dir, args.output_dir, config,
            args.max_images, args.visualization_dir
        )

        success_count = summary['success_count']
        total_files = summary['total_files']
        print(f"\n📈 批量处理结果: {success_count}/{total_files} 成功")

        # 保存处理汇总
        summary_path = bf.pathjoin(args.output_dir, f"processing_summary_{bf.nowtime()}.json")
        bf.save_json_dict_orig(summary, summary_path)
        print(f"处理汇总已保存到: {summary_path}")

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        logging.error(f"主程序执行失败: {e}")
        return 1

    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        image_memory_pool.clear()
        mask_cache.clear()
        gc.collect()

    print("\n✅ 处理完成!")
    return 0

if __name__ == "__main__":
    exit_code = main()
