#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU资源管理器

智能分配12个检测模块到2块GPU上，确保资源合理利用
"""

import threading
from typing import Dict, List
from dataclasses import dataclass


@dataclass
class ModuleConfig:
    """模块配置信息"""
    name: str
    model_path: str
    gpu_id: int
    estimated_memory: int  # MB
    complexity: str  # 'light', 'medium', 'heavy'
    dependencies: List[str] = None


class GPUResourceManager:
    """GPU资源管理器 - 智能分配模块到GPU"""
    
    def __init__(self):
        self.gpu0_modules = []  # GPU 0分配的模块
        self.gpu1_modules = []  # GPU 1分配的模块
        self.module_configs = {}
        self.gpu_memory_limit = 24 * 1024  # 24GB in MB
        self._lock = threading.Lock()
        
        # 初始化模块配置
        self._initialize_module_configs()
        
        # 执行智能分配
        self._allocate_modules()
    
    def _initialize_module_configs(self):
        """初始化所有模块的配置信息"""
        
        # 基础路径
        base_path = "/home/<USER>/xm/code/coderafactor"
        
        # 模块配置 - 基于实际分析的显存使用情况
        configs = [
            # GPU 0 - 轻量级到中等复杂度模块 (~15GB)
            ModuleConfig("zhumaidakai", f"{base_path}/seg_det_zhumaidakai/zhumaidakai.onnx", 0, 2048, "light"),
            ModuleConfig("zhumaizoushi", f"{base_path}/seg_det_zhumaizoushi/zhumaizoushi.onnx", 0, 2048, "light"),
            ModuleConfig("zheheng", f"{base_path}/seg_det_zheheng/zheheng.onnx", 0, 3072, "medium"),
            ModuleConfig("zhimai", f"{base_path}/seg_det_zhimai/zhimai.onnx", 0, 3072, "medium"),
            ModuleConfig("zhimaiqing", f"{base_path}/seg_det_zhimaiqing/zhimaiqing.onnx", 0, 3072, "medium"),
            ModuleConfig("hengwen", f"{base_path}/seg_det_hengwen/hengwen.onnx", 0, 2048, "light"),
            
            # GPU 1 - 中等到重量级模块 (~20GB) - 优化分配
            ModuleConfig("lunkuo_canque_fill", f"{base_path}/seg_det_lunkuo_canque_fill", 1, 6144, "heavy"),  # 双模型，减少估算
            ModuleConfig("jiaodian", f"{base_path}/seg_det_jiaodian/fuqing.onnx", 1, 3072, "heavy"),  # 减少估算
            ModuleConfig("kaohong", f"{base_path}/seg_det_kaohong/kaohong.onnx", 1, 2560, "medium"),
            ModuleConfig("guahui", f"{base_path}/seg_det_kaohong/guahui.onnx", 1, 2560, "medium"),
            ModuleConfig("zhousuo", f"{base_path}/seg_det_zhousuo/zhousuo.onnx", 1, 2560, "medium"),
            ModuleConfig("chaohong", f"{base_path}/seg_det_chaohong/chaohong.onnx", 1, 2560, "medium"),
        ]
        
        for config in configs:
            self.module_configs[config.name] = config
    
    def _allocate_modules(self):
        """智能分配模块到GPU"""
        
        print("🔧 开始智能分配模块到GPU...")
        
        # 按照预设的分配策略
        for module_name, config in self.module_configs.items():
            if config.gpu_id == 0:
                self.gpu0_modules.append(module_name)
            else:
                self.gpu1_modules.append(module_name)
        
        # 计算显存使用情况
        gpu0_memory = sum(self.module_configs[name].estimated_memory for name in self.gpu0_modules)
        gpu1_memory = sum(self.module_configs[name].estimated_memory for name in self.gpu1_modules)
        
        print(f"📊 GPU资源分配结果:")
        print(f"   GPU 0: {len(self.gpu0_modules)}个模块, 预估显存: {gpu0_memory/1024:.1f}GB")
        print(f"   GPU 1: {len(self.gpu1_modules)}个模块, 预估显存: {gpu1_memory/1024:.1f}GB")
        
        # 显示详细分配
        print(f"   GPU 0模块: {', '.join(self.gpu0_modules)}")
        print(f"   GPU 1模块: {', '.join(self.gpu1_modules)}")
        
        # 检查是否超出限制
        if gpu0_memory > self.gpu_memory_limit:
            print(f"⚠️  GPU 0显存可能不足: {gpu0_memory/1024:.1f}GB > {self.gpu_memory_limit/1024:.1f}GB")
        if gpu1_memory > self.gpu_memory_limit:
            print(f"⚠️  GPU 1显存可能不足: {gpu1_memory/1024:.1f}GB > {self.gpu_memory_limit/1024:.1f}GB")
    
    def get_module_gpu(self, module_name: str) -> int:
        """获取模块分配的GPU ID"""
        config = self.module_configs.get(module_name)
        if config:
            return config.gpu_id
        raise ValueError(f"Unknown module: {module_name}")
    
    def get_module_config(self, module_name: str) -> ModuleConfig:
        """获取模块配置"""
        config = self.module_configs.get(module_name)
        if config:
            return config
        raise ValueError(f"Unknown module: {module_name}")
    
    def get_gpu_modules(self, gpu_id: int) -> List[str]:
        """获取指定GPU上的模块列表"""
        if gpu_id == 0:
            return self.gpu0_modules.copy()
        elif gpu_id == 1:
            return self.gpu1_modules.copy()
        else:
            raise ValueError(f"Invalid GPU ID: {gpu_id}")
    
    def get_all_modules(self) -> List[str]:
        """获取所有模块名称"""
        return list(self.module_configs.keys())
    
    def validate_allocation(self) -> bool:
        """验证分配是否合理"""
        
        # 检查所有模块都已分配
        all_modules = set(self.module_configs.keys())
        allocated_modules = set(self.gpu0_modules + self.gpu1_modules)
        
        if all_modules != allocated_modules:
            missing = all_modules - allocated_modules
            extra = allocated_modules - all_modules
            print(f"❌ 分配验证失败:")
            if missing:
                print(f"   未分配模块: {missing}")
            if extra:
                print(f"   多余模块: {extra}")
            return False
        
        # 检查显存使用
        gpu0_memory = sum(self.module_configs[name].estimated_memory for name in self.gpu0_modules)
        gpu1_memory = sum(self.module_configs[name].estimated_memory for name in self.gpu1_modules)
        
        if gpu0_memory > self.gpu_memory_limit * 0.98:  # 98%阈值，更宽松
            print(f"❌ GPU 0显存使用过高: {gpu0_memory/1024:.1f}GB")
            return False

        if gpu1_memory > self.gpu_memory_limit * 0.98:  # 98%阈值，更宽松
            print(f"❌ GPU 1显存使用过高: {gpu1_memory/1024:.1f}GB")
            return False
        
        print("✅ GPU资源分配验证通过")
        return True
    
    def get_resource_stats(self) -> Dict:
        """获取资源使用统计"""
        gpu0_memory = sum(self.module_configs[name].estimated_memory for name in self.gpu0_modules)
        gpu1_memory = sum(self.module_configs[name].estimated_memory for name in self.gpu1_modules)
        
        return {
            'gpu0': {
                'modules': len(self.gpu0_modules),
                'memory_mb': gpu0_memory,
                'memory_gb': gpu0_memory / 1024,
                'utilization': gpu0_memory / self.gpu_memory_limit * 100
            },
            'gpu1': {
                'modules': len(self.gpu1_modules),
                'memory_mb': gpu1_memory,
                'memory_gb': gpu1_memory / 1024,
                'utilization': gpu1_memory / self.gpu_memory_limit * 100
            },
            'total': {
                'modules': len(self.module_configs),
                'memory_gb': (gpu0_memory + gpu1_memory) / 1024
            }
        }
    
    def optimize_allocation(self):
        """优化分配策略（预留接口）"""
        # 未来可以实现动态优化算法
        pass
