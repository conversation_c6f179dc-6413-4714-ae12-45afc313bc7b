#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合检测可视化器

可视化12个检测模块的综合结果
"""

import os
import cv2
import numpy as np
from typing import List, Dict, Optional, Tuple
from .data_structures import ComprehensivePipelineData


class ComprehensiveVisualizer:
    """综合检测可视化器"""
    
    def __init__(self):
        # 模块颜色映射 - 与结果合并器保持一致
        self.module_colors = {
            'zhumaidakai': (255, 0, 0),      # 红色
            'zhumaizoushi': (255, 128, 0),   # 橙色
            'zheheng': (255, 255, 0),        # 黄色
            'zhimai': (128, 255, 0),         # 黄绿色
            'zhimaiqing': (0, 255, 0),       # 绿色
            'lunkuo_canque_fill': (0, 255, 128),  # 青绿色
            'jiaodian': (0, 255, 255),       # 青色
            'hengwen': (0, 128, 255),        # 浅蓝色
            'kaohong': (0, 0, 255),          # 蓝色
            'guahui': (128, 0, 255),         # 紫色
            'zhousuo': (255, 0, 255),        # 品红色
            'chaohong': (255, 0, 128),       # 粉红色
        }
        
        # 线条粗细
        self.line_thickness = 2
        self.font_scale = 0.6
        self.font_thickness = 1
    
    def visualize_comprehensive_results(self, pipeline_data: ComprehensivePipelineData, 
                                      output_path: str) -> bool:
        """可视化综合检测结果"""
        
        try:
            # 读取原始图像
            image = cv2.imread(pipeline_data.image_path)
            if image is None:
                print(f"❌ 无法读取图像: {pipeline_data.image_path}")
                return False
            
            # 创建可视化图像
            vis_image = image.copy()
            
            # 绘制所有检测结果
            if pipeline_data.merged_shapes:
                vis_image = self._draw_all_shapes(vis_image, pipeline_data.merged_shapes)
            
            # 添加图例
            vis_image = self._add_legend(vis_image, pipeline_data.detection_summary)
            
            # 添加统计信息
            vis_image = self._add_statistics(vis_image, pipeline_data.detection_summary)
            
            # 保存可视化结果
            cv2.imwrite(output_path, vis_image)
            
            print(f"✅ 可视化结果已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 可视化失败: {e}")
            return False
    
    def _draw_all_shapes(self, image: np.ndarray, shapes: List[Dict]) -> np.ndarray:
        """绘制所有检测形状"""
        
        height, width = image.shape[:2]
        
        for shape in shapes:
            points = shape.get('points', [])
            label = shape.get('label', 'unknown')
            module_source = shape.get('module_source', 'unknown')
            has_conflicts = shape.get('has_conflicts', False)
            
            if not points:
                continue
            
            # 获取颜色
            color = self.module_colors.get(module_source, (128, 128, 128))
            
            # 如果有冲突，使用虚线或不同颜色
            if has_conflicts:
                color = tuple(int(c * 0.7) for c in color)  # 降低亮度
            
            # 转换相对坐标为绝对坐标
            abs_points = []
            for point in points:
                x = int(point[0] * width)
                y = int(point[1] * height)
                abs_points.append((x, y))
            
            # 绘制形状
            shape_type = shape.get('shape_type', 'rectangle')
            
            if shape_type == 'rectangle' and len(abs_points) == 2:
                # 矩形
                cv2.rectangle(image, abs_points[0], abs_points[1], color, self.line_thickness)
                
                # 添加标签
                label_text = f"{label}({module_source})"
                self._draw_label(image, abs_points[0], label_text, color)
                
            elif shape_type == 'polygon' and len(abs_points) > 2:
                # 多边形
                pts = np.array(abs_points, np.int32)
                pts = pts.reshape((-1, 1, 2))
                cv2.polylines(image, [pts], True, color, self.line_thickness)
                
                # 添加标签
                center_x = sum(p[0] for p in abs_points) // len(abs_points)
                center_y = sum(p[1] for p in abs_points) // len(abs_points)
                label_text = f"{label}({module_source})"
                self._draw_label(image, (center_x, center_y), label_text, color)
        
        return image
    
    def _draw_label(self, image: np.ndarray, position: Tuple[int, int], 
                   text: str, color: Tuple[int, int, int]):
        """绘制标签文本"""
        
        x, y = position
        
        # 计算文本大小
        (text_width, text_height), baseline = cv2.getTextSize(
            text, cv2.FONT_HERSHEY_SIMPLEX, self.font_scale, self.font_thickness
        )
        
        # 绘制背景矩形
        cv2.rectangle(image, 
                     (x, y - text_height - baseline),
                     (x + text_width, y + baseline),
                     color, -1)
        
        # 绘制文本
        cv2.putText(image, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX,
                   self.font_scale, (255, 255, 255), self.font_thickness)
    
    def _add_legend(self, image: np.ndarray, detection_summary: Optional[Dict]) -> np.ndarray:
        """添加图例"""
        
        if not detection_summary:
            return image
        
        height, width = image.shape[:2]
        
        # 图例位置（右上角）
        legend_x = width - 300
        legend_y = 30
        
        # 绘制图例背景
        cv2.rectangle(image, (legend_x - 10, legend_y - 10),
                     (width - 10, legend_y + len(self.module_colors) * 25 + 10),
                     (0, 0, 0), -1)
        
        # 绘制图例标题
        cv2.putText(image, "Detection Modules", (legend_x, legend_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 绘制每个模块的图例
        y_offset = legend_y + 25
        module_counts = detection_summary.get('module_counts', {})
        
        for module_name, color in self.module_colors.items():
            count = module_counts.get(module_name, 0)
            
            # 绘制颜色块
            cv2.rectangle(image, (legend_x, y_offset - 8),
                         (legend_x + 15, y_offset + 8), color, -1)
            
            # 绘制模块名称和数量
            text = f"{module_name}: {count}"
            cv2.putText(image, text, (legend_x + 20, y_offset + 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            y_offset += 20
        
        return image
    
    def _add_statistics(self, image: np.ndarray, detection_summary: Optional[Dict]) -> np.ndarray:
        """添加统计信息"""
        
        if not detection_summary:
            return image
        
        height, width = image.shape[:2]
        
        # 统计信息位置（左上角）
        stats_x = 10
        stats_y = 30
        
        # 获取统计数据
        total_detections = detection_summary.get('total_detections', 0)
        conflict_count = detection_summary.get('conflict_count', 0)
        conflict_rate = detection_summary.get('conflict_rate', 0.0)
        
        # 绘制统计信息背景
        cv2.rectangle(image, (stats_x - 5, stats_y - 20),
                     (stats_x + 250, stats_y + 80), (0, 0, 0), -1)
        
        # 绘制统计信息
        stats_text = [
            f"Total Detections: {total_detections}",
            f"Conflicts: {conflict_count}",
            f"Conflict Rate: {conflict_rate:.1%}",
            f"Active Modules: {len([c for c in detection_summary.get('module_counts', {}).values() if c > 0])}/12"
        ]
        
        for i, text in enumerate(stats_text):
            cv2.putText(image, text, (stats_x, stats_y + i * 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        return image
    
    def create_module_comparison_visualization(self, pipeline_data: ComprehensivePipelineData,
                                             output_dir: str) -> bool:
        """创建模块对比可视化"""
        
        try:
            # 读取原始图像
            image = cv2.imread(pipeline_data.image_path)
            if image is None:
                return False
            
            # 为每个模块创建单独的可视化
            results = pipeline_data.get_all_results()
            
            for module_name, result in results.items():
                if result and result.get('shapes'):
                    module_image = image.copy()
                    
                    # 只绘制当前模块的结果
                    shapes = result['shapes']
                    color = self.module_colors.get(module_name, (128, 128, 128))
                    
                    module_image = self._draw_module_shapes(module_image, shapes, color, module_name)
                    
                    # 保存模块可视化
                    base_name = os.path.splitext(os.path.basename(pipeline_data.image_path))[0]
                    output_path = os.path.join(output_dir, f"{base_name}_{module_name}.png")
                    cv2.imwrite(output_path, module_image)
            
            return True
            
        except Exception as e:
            print(f"❌ 模块对比可视化失败: {e}")
            return False
    
    def _draw_module_shapes(self, image: np.ndarray, shapes: List[Dict], 
                           color: Tuple[int, int, int], module_name: str) -> np.ndarray:
        """绘制单个模块的检测形状"""
        
        height, width = image.shape[:2]
        
        for shape in shapes:
            points = shape.get('points', [])
            label = shape.get('label', 'unknown')
            
            if not points:
                continue
            
            # 转换相对坐标为绝对坐标
            abs_points = []
            for point in points:
                x = int(point[0] * width)
                y = int(point[1] * height)
                abs_points.append((x, y))
            
            # 绘制形状
            shape_type = shape.get('shape_type', 'rectangle')
            
            if shape_type == 'rectangle' and len(abs_points) == 2:
                cv2.rectangle(image, abs_points[0], abs_points[1], color, self.line_thickness)
                self._draw_label(image, abs_points[0], label, color)
                
            elif shape_type == 'polygon' and len(abs_points) > 2:
                pts = np.array(abs_points, np.int32)
                pts = pts.reshape((-1, 1, 2))
                cv2.polylines(image, [pts], True, color, self.line_thickness)
                
                center_x = sum(p[0] for p in abs_points) // len(abs_points)
                center_y = sum(p[1] for p in abs_points) // len(abs_points)
                self._draw_label(image, (center_x, center_y), label, color)
        
        # 添加模块标题
        cv2.putText(image, f"Module: {module_name}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)
        
        return image
