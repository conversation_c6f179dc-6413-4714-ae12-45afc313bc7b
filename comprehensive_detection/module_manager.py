#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块管理器

管理12个检测模块的加载、调度和执行
"""

import os
import sys
import time
import threading
import importlib.util
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

from .gpu_resource_manager import GPUResourceManager, ModuleConfig
from .data_structures import ComprehensivePipelineData


class ModuleWrapper:
    """模块包装器 - 统一接口封装不同的检测模块"""
    
    def __init__(self, module_name: str, config: ModuleConfig):
        self.module_name = module_name
        self.config = config
        self.model_manager = None
        self.processor = None
        self._initialized = False
        self._lock = threading.Lock()
        
        print(f"🔧 创建模块包装器: {module_name} (GPU {config.gpu_id})")
    
    def load_model(self):
        """按需加载模型"""
        with self._lock:
            if self._initialized:
                return
            
            print(f"🔄 加载模块: {self.module_name}")
            
            try:
                if self.module_name == "lunkuo_canque_fill":
                    # 特殊处理双模型模块
                    self._load_lunkuo_canque_fill()
                else:
                    # 标准单模型模块
                    self._load_standard_module()
                
                self._initialized = True
                print(f"✅ 模块加载成功: {self.module_name}")
                
            except Exception as e:
                print(f"❌ 模块加载失败: {self.module_name}, 错误: {e}")
                raise
    
    def _load_lunkuo_canque_fill(self):
        """加载轮廓残缺补全双模型"""
        from seg_det_lunkuo_canque_fill.deploy_seg_lunkuo_canque_fill_onnx_gpu_final import DualGPUModelManager
        
        canque_model_path = os.path.join(self.config.model_path, "canque.onnx")
        lunkuo_model_path = os.path.join(self.config.model_path, "lunkuo.onnx")
        
        self.model_manager = DualGPUModelManager(
            canque_model_path=canque_model_path,
            lunkuo_model_path=lunkuo_model_path,
            thread_id=threading.current_thread().ident
        )
    
    def _load_standard_module(self):
        """加载标准单模型模块"""
        
        # 根据模块名称选择对应的模型管理器
        if self.module_name == "zhumaidakai":
            from seg_det_zhumaidakai.deploy_seg_det_zhumaidakai_onnx_gpu_final import GPUZhumaidakaiModelManager
            self.model_manager = GPUZhumaidakaiModelManager(
                self.config.model_path, 
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "zhumaizoushi":
            from seg_det_zhumaizoushi.deploy_seg_det_zhumaizoushi_onnx_gpu_final import GPUZhumaizoushiModelManager
            self.model_manager = GPUZhumaizoushiModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "zheheng":
            from seg_det_zheheng.deploy_seg_det_zheheng_onnx_gpu_final import GPUZhehengModelManager
            self.model_manager = GPUZhehengModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "zhimai":
            from seg_det_zhimai.deploy_seg_det_zhimai_onnx_gpu_final import GPUZhimaiModelManager
            self.model_manager = GPUZhimaiModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "zhimaiqing":
            from seg_det_zhimaiqing.deploy_seg_det_zhimaiqing_onnx_gpu_final import GPUZhimaiqingModelManager
            self.model_manager = GPUZhimaiqingModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "jiaodian":
            from seg_det_jiaodian.deploy_seg_det_condition_onnx_gpu_final import GPUConditionModelManager
            self.model_manager = GPUConditionModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "hengwen":
            from seg_det_hengwen.deploy_seg_det_hengwen_onnx_gpu_final import GPUHengwenModelManager
            self.model_manager = GPUHengwenModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "kaohong":
            from seg_det_kaohong.deploy_seg_det_kaohong_onnx_gpu_final import GPUKaohongModelManager
            self.model_manager = GPUKaohongModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "guahui":
            from seg_det_kaohong.deploy_seg_det_guahui_onnx_gpu_final import GPUGuahuiModelManager
            self.model_manager = GPUGuahuiModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "zhousuo":
            from seg_det_zhousuo.deploy_seg_det_zhousuo_onnx_gpu_final import GPUZhousuoModelManager
            self.model_manager = GPUZhousuoModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        elif self.module_name == "chaohong":
            from seg_det_chaohong.deploy_seg_det_chaohong_onnx_gpu_final import GPUChaohongModelManager
            self.model_manager = GPUChaohongModelManager(
                self.config.model_path,
                thread_id=threading.current_thread().ident
            )
            
        else:
            raise ValueError(f"Unknown module: {self.module_name}")
    
    def process_image(self, image_data) -> Dict:
        """处理图像并返回结果"""
        if not self._initialized:
            self.load_model()
        
        start_time = time.time()
        
        try:
            if self.module_name == "lunkuo_canque_fill":
                # 双模型特殊处理
                result = self._process_lunkuo_canque_fill(image_data)
            else:
                # 标准模块处理
                result = self._process_standard_module(image_data)
            
            processing_time = time.time() - start_time
            
            return {
                'shapes': result.get('shapes', []),
                'detection_count': len(result.get('shapes', [])),
                'processing_time': processing_time,
                'module_name': self.module_name
            }
            
        except Exception as e:
            print(f"❌ 模块处理异常: {self.module_name}, 错误: {e}")
            return {
                'shapes': [],
                'detection_count': 0,
                'processing_time': time.time() - start_time,
                'module_name': self.module_name,
                'error': str(e)
            }
    
    def _process_lunkuo_canque_fill(self, image_data) -> Dict:
        """处理轮廓残缺补全双模型"""
        # 这里需要实现具体的双模型处理逻辑
        # 暂时返回空结果
        return {'shapes': []}
    
    def _process_standard_module(self, image_data) -> Dict:
        """处理标准单模型"""
        # 简化处理：直接调用模型管理器的推理方法
        # 具体实现需要根据每个模块的接口进行适配
        try:
            if hasattr(self.model_manager, 'predict'):
                # YOLO类模型
                result = self.model_manager.predict(image_data)
                # 转换为统一格式
                shapes = self._convert_to_shapes(result)
                return {'shapes': shapes}
            elif hasattr(self.model_manager, 'run_inference'):
                # 分割类模型
                result = self.model_manager.run_inference(image_data)
                shapes = self._convert_to_shapes(result)
                return {'shapes': shapes}
            else:
                return {'shapes': []}
        except Exception as e:
            print(f"⚠️  模块 {self.module_name} 处理异常: {e}")
            return {'shapes': []}

    def _convert_to_shapes(self, result) -> List[Dict]:
        """将模型结果转换为统一的shapes格式"""
        # 这里需要根据不同模块的输出格式进行转换
        # 暂时返回空列表，具体实现需要适配每个模块
        return []
    
    def unload_model(self):
        """卸载模型释放内存"""
        with self._lock:
            if self._initialized:
                self.model_manager = None
                self.processor = None
                self._initialized = False
                print(f"🗑️  卸载模块: {self.module_name}")


class ModuleManager:
    """模块管理器 - 管理所有检测模块的生命周期"""
    
    def __init__(self, gpu_manager: GPUResourceManager):
        self.gpu_manager = gpu_manager
        self.module_wrappers: Dict[str, ModuleWrapper] = {}
        self.loaded_modules = set()
        self._lock = threading.Lock()
        
        print("🔧 初始化模块管理器...")
        self._initialize_wrappers()
    
    def _initialize_wrappers(self):
        """初始化所有模块包装器"""
        for module_name in self.gpu_manager.get_all_modules():
            config = self.gpu_manager.get_module_config(module_name)
            wrapper = ModuleWrapper(module_name, config)
            self.module_wrappers[module_name] = wrapper
        
        print(f"✅ 初始化了 {len(self.module_wrappers)} 个模块包装器")
    
    def load_module(self, module_name: str):
        """加载指定模块"""
        with self._lock:
            if module_name in self.loaded_modules:
                return
            
            wrapper = self.module_wrappers.get(module_name)
            if wrapper:
                wrapper.load_model()
                self.loaded_modules.add(module_name)
            else:
                raise ValueError(f"Unknown module: {module_name}")
    
    def load_all_modules(self):
        """加载所有模块"""
        print("🔄 开始加载所有检测模块...")
        
        start_time = time.time()
        
        # 并行加载模块以提高效率
        with ThreadPoolExecutor(max_workers=4, thread_name_prefix="ModuleLoader") as executor:
            futures = []
            for module_name in self.module_wrappers.keys():
                future = executor.submit(self.load_module, module_name)
                futures.append((module_name, future))
            
            # 等待所有模块加载完成
            for module_name, future in futures:
                try:
                    future.result(timeout=60)  # 60秒超时
                except Exception as e:
                    print(f"❌ 模块加载失败: {module_name}, 错误: {e}")
        
        load_time = time.time() - start_time
        print(f"✅ 所有模块加载完成，耗时: {load_time:.2f}秒")
    
    def process_image_with_module(self, module_name: str, image_data) -> Dict:
        """使用指定模块处理图像"""
        wrapper = self.module_wrappers.get(module_name)
        if wrapper:
            return wrapper.process_image(image_data)
        else:
            raise ValueError(f"Unknown module: {module_name}")
    
    def process_image_with_all_modules(self, pipeline_data: ComprehensivePipelineData) -> ComprehensivePipelineData:
        """使用所有模块处理图像"""
        
        # 并行处理所有模块
        with ThreadPoolExecutor(max_workers=12, thread_name_prefix="ModuleProcessor") as executor:
            futures = {}
            
            for module_name in self.module_wrappers.keys():
                future = executor.submit(self.process_image_with_module, module_name, pipeline_data.image_data)
                futures[module_name] = future
            
            # 收集结果
            for module_name, future in futures.items():
                try:
                    result = future.result(timeout=30)  # 30秒超时
                    pipeline_data.set_result(module_name, result)
                except Exception as e:
                    print(f"❌ 模块处理失败: {module_name}, 错误: {e}")
                    # 设置空结果
                    pipeline_data.set_result(module_name, {
                        'shapes': [],
                        'detection_count': 0,
                        'processing_time': 0.0,
                        'module_name': module_name,
                        'error': str(e)
                    })
        
        return pipeline_data
    
    def unload_all_modules(self):
        """卸载所有模块"""
        print("🗑️  卸载所有模块...")
        
        with self._lock:
            for wrapper in self.module_wrappers.values():
                wrapper.unload_model()
            self.loaded_modules.clear()
        
        print("✅ 所有模块已卸载")
    
    def get_loaded_modules(self) -> List[str]:
        """获取已加载的模块列表"""
        with self._lock:
            return list(self.loaded_modules)
