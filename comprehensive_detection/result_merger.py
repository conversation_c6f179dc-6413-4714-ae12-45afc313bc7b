#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果合并器

智能合并12个检测模块的结果，处理重叠和冲突
"""

import time
from typing import List, Dict, Optional, Any, Tuple
from .data_structures import ComprehensivePipelineData


class ResultMerger:
    """结果合并器 - 智能合并所有模块的检测结果"""
    
    def __init__(self):
        # 模块优先级 - 数值越高优先级越高
        self.module_priorities = {
            'lunkuo_canque_fill': 10,  # 最高优先级，轮廓相关
            'zhumaidakai': 9,          # 主脉相关
            'zhumaizoushi': 9,
            'zhimai': 8,               # 支脉相关
            'zhimaiqing': 8,
            'jiaodian': 7,             # 焦点浮青
            'zheheng': 6,              # 折痕
            'hengwen': 5,              # 横纹
            'kaohong': 4,              # 烤红
            'guahui': 4,               # 挂灰
            'zhousuo': 4,              # 皱缩
            'chaohong': 4,             # 潮红
        }
        
        # 模块颜色映射 - 用于可视化
        self.module_colors = {
            'zhumaidakai': [255, 0, 0],      # 红色
            'zhumaizoushi': [255, 128, 0],   # 橙色
            'zheheng': [255, 255, 0],        # 黄色
            'zhimai': [128, 255, 0],         # 黄绿色
            'zhimaiqing': [0, 255, 0],       # 绿色
            'lunkuo_canque_fill': [0, 255, 128],  # 青绿色
            'jiaodian': [0, 255, 255],       # 青色
            'hengwen': [0, 128, 255],        # 浅蓝色
            'kaohong': [0, 0, 255],          # 蓝色
            'guahui': [128, 0, 255],         # 紫色
            'zhousuo': [255, 0, 255],        # 品红色
            'chaohong': [255, 0, 128],       # 粉红色
        }
    
    def merge_all_results(self, pipeline_data: ComprehensivePipelineData) -> ComprehensivePipelineData:
        """合并所有模块的检测结果"""
        
        print(f"🔄 开始合并检测结果: {pipeline_data.image_path}")
        
        start_time = time.time()
        
        # 收集所有有效的shapes
        all_shapes = []
        module_stats = {}
        
        results = pipeline_data.get_all_results()
        
        for module_name, result in results.items():
            if result and 'shapes' in result:
                shapes = result['shapes']
                if shapes:
                    # 为每个shape添加模块信息
                    for shape in shapes:
                        shape['module_source'] = module_name
                        shape['module_priority'] = self.module_priorities.get(module_name, 0)
                        shape['module_color'] = self.module_colors.get(module_name, [128, 128, 128])
                    
                    all_shapes.extend(shapes)
                    module_stats[module_name] = len(shapes)
                else:
                    module_stats[module_name] = 0
            else:
                module_stats[module_name] = 0
        
        # 处理重叠和冲突
        merged_shapes = self._resolve_conflicts(all_shapes)
        
        # 按优先级排序
        merged_shapes.sort(key=lambda x: x.get('module_priority', 0), reverse=True)
        
        # 生成检测摘要
        detection_summary = self._generate_summary(module_stats, merged_shapes)
        
        # 更新pipeline_data
        pipeline_data.merged_shapes = merged_shapes
        pipeline_data.detection_summary = detection_summary
        pipeline_data.stage = "merged"
        
        merge_time = time.time() - start_time
        
        print(f"✅ 结果合并完成: {len(merged_shapes)}个检测结果, 耗时: {merge_time:.3f}秒")
        
        return pipeline_data
    
    def _resolve_conflicts(self, all_shapes: List[Dict]) -> List[Dict]:
        """解决重叠和冲突的检测结果"""
        
        if not all_shapes:
            return []
        
        # 简单的冲突解决策略：保留所有结果，但标记重叠
        resolved_shapes = []
        
        for i, shape in enumerate(all_shapes):
            # 检查与其他shapes的重叠
            overlaps = []
            for j, other_shape in enumerate(all_shapes):
                if i != j and self._is_overlapping(shape, other_shape):
                    overlaps.append({
                        'index': j,
                        'module': other_shape.get('module_source', 'unknown'),
                        'priority': other_shape.get('module_priority', 0)
                    })
            
            # 添加重叠信息
            if overlaps:
                shape['overlaps'] = overlaps
                shape['has_conflicts'] = True
            else:
                shape['has_conflicts'] = False
            
            resolved_shapes.append(shape)
        
        return resolved_shapes
    
    def _is_overlapping(self, shape1: Dict, shape2: Dict) -> bool:
        """检查两个shape是否重叠"""
        
        # 获取边界框
        bbox1 = self._get_bounding_box(shape1)
        bbox2 = self._get_bounding_box(shape2)
        
        if not bbox1 or not bbox2:
            return False
        
        # 计算重叠面积
        overlap_area = self._calculate_overlap_area(bbox1, bbox2)
        
        # 如果重叠面积大于较小框的50%，认为是重叠
        min_area = min(self._calculate_area(bbox1), self._calculate_area(bbox2))
        
        return overlap_area > min_area * 0.5
    
    def _get_bounding_box(self, shape: Dict) -> Optional[Tuple[float, float, float, float]]:
        """获取shape的边界框 (x1, y1, x2, y2)"""
        
        points = shape.get('points', [])
        if not points:
            return None
        
        if len(points) == 2:
            # 矩形格式 [[x1, y1], [x2, y2]]
            x1, y1 = points[0]
            x2, y2 = points[1]
            return (min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2))
        
        elif len(points) > 2:
            # 多边形格式
            xs = [p[0] for p in points]
            ys = [p[1] for p in points]
            return (min(xs), min(ys), max(xs), max(ys))
        
        return None
    
    def _calculate_overlap_area(self, bbox1: Tuple[float, float, float, float], 
                               bbox2: Tuple[float, float, float, float]) -> float:
        """计算两个边界框的重叠面积"""
        
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2
        
        # 计算重叠区域
        x1_overlap = max(x1_1, x1_2)
        y1_overlap = max(y1_1, y1_2)
        x2_overlap = min(x2_1, x2_2)
        y2_overlap = min(y2_1, y2_2)
        
        # 检查是否有重叠
        if x1_overlap >= x2_overlap or y1_overlap >= y2_overlap:
            return 0.0
        
        return (x2_overlap - x1_overlap) * (y2_overlap - y1_overlap)
    
    def _calculate_area(self, bbox: Tuple[float, float, float, float]) -> float:
        """计算边界框面积"""
        x1, y1, x2, y2 = bbox
        return (x2 - x1) * (y2 - y1)
    
    def _generate_summary(self, module_stats: Dict[str, int], merged_shapes: List[Dict]) -> Dict:
        """生成检测摘要"""
        
        total_detections = len(merged_shapes)
        
        # 按模块统计
        module_counts = {}
        for module_name, count in module_stats.items():
            module_counts[module_name] = count
        
        # 按类型统计
        type_counts = {}
        for shape in merged_shapes:
            label = shape.get('label', 'unknown')
            type_counts[label] = type_counts.get(label, 0) + 1
        
        # 冲突统计
        conflict_count = sum(1 for shape in merged_shapes if shape.get('has_conflicts', False))
        
        summary = {
            'total_detections': total_detections,
            'module_counts': module_counts,
            'type_counts': type_counts,
            'conflict_count': conflict_count,
            'conflict_rate': conflict_count / total_detections if total_detections > 0 else 0.0,
            'timestamp': time.time()
        }
        
        return summary
    
    def create_comprehensive_json(self, pipeline_data: ComprehensivePipelineData, 
                                 image_path: str) -> Dict:
        """创建综合检测的JSON输出"""
        
        import os
        
        json_data = {
            "version": "4.5.6",
            "flags": {},
            "shapes": pipeline_data.merged_shapes or [],
            "imagePath": os.path.basename(image_path),
            "imageData": None,
            "imageHeight": pipeline_data.height,
            "imageWidth": pipeline_data.width,
            "detection_summary": pipeline_data.detection_summary,
            "processing_info": {
                "timestamp": pipeline_data.timestamp,
                "stage": pipeline_data.stage,
                "total_modules": 12,
                "successful_modules": len([r for r in pipeline_data.get_all_results().values() if r])
            }
        }
        
        return json_data
    
    def get_module_colors(self) -> Dict[str, List[int]]:
        """获取模块颜色映射"""
        return self.module_colors.copy()
    
    def get_module_priorities(self) -> Dict[str, int]:
        """获取模块优先级映射"""
        return self.module_priorities.copy()
