#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合检测数据结构定义

统一的数据流架构，支持12个检测模块的并行处理
"""

import time
import queue
import threading
import numpy as np
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field


@dataclass
class ComprehensivePipelineData:
    """综合流水线数据结构 - 支持所有12个检测模块"""
    
    # 基础信息
    image_path: str
    image_data: Optional[np.ndarray] = None
    width: int = 0
    height: int = 0
    timestamp: float = field(default_factory=time.time)
    stage: str = "init"  # init, preprocessed, inferred, postprocessed
    
    # 各模块的检测结果
    zhumaidakai_result: Optional[Dict] = None      # 主脉打开检测
    zhumaizoushi_result: Optional[Dict] = None     # 主脉走势检测
    zheheng_result: Optional[Dict] = None          # 折痕检测
    zhimai_result: Optional[Dict] = None           # 支脉检测
    zhimaiqing_result: Optional[Dict] = None       # 支脉青检测
    lunkuo_canque_fill_result: Optional[Dict] = None  # 轮廓残缺补全检测
    jiaodian_result: Optional[Dict] = None         # 焦点浮青等检测
    hengwen_result: Optional[Dict] = None          # 横纹检测
    kaohong_result: Optional[Dict] = None          # 烤红检测
    guahui_result: Optional[Dict] = None           # 挂灰检测
    zhousuo_result: Optional[Dict] = None          # 皱缩检测
    chaohong_result: Optional[Dict] = None         # 潮红检测
    
    # 合并结果
    merged_shapes: Optional[List] = None
    detection_summary: Optional[Dict] = None
    
    def get_all_results(self) -> Dict[str, Optional[Dict]]:
        """获取所有模块的检测结果"""
        return {
            'zhumaidakai': self.zhumaidakai_result,
            'zhumaizoushi': self.zhumaizoushi_result,
            'zheheng': self.zheheng_result,
            'zhimai': self.zhimai_result,
            'zhimaiqing': self.zhimaiqing_result,
            'lunkuo_canque_fill': self.lunkuo_canque_fill_result,
            'jiaodian': self.jiaodian_result,
            'hengwen': self.hengwen_result,
            'kaohong': self.kaohong_result,
            'guahui': self.guahui_result,
            'zhousuo': self.zhousuo_result,
            'chaohong': self.chaohong_result
        }
    
    def set_result(self, module_name: str, result: Dict):
        """设置指定模块的检测结果"""
        if hasattr(self, f"{module_name}_result"):
            setattr(self, f"{module_name}_result", result)
        else:
            raise ValueError(f"Unknown module: {module_name}")
    
    def is_all_modules_completed(self) -> bool:
        """检查是否所有模块都已完成"""
        results = self.get_all_results()
        return all(result is not None for result in results.values())


class AsyncQueue:
    """异步队列 - 支持流水线各阶段的数据传递"""
    
    def __init__(self, maxsize: int = 32):
        self.queue = queue.Queue(maxsize=maxsize)
        self.maxsize = maxsize
        
    def put(self, item, timeout: float = 1.0) -> bool:
        """放入数据项"""
        try:
            self.queue.put(item, timeout=timeout)
            return True
        except queue.Full:
            return False
    
    def get(self, timeout: float = 1.0) -> Optional[Any]:
        """获取数据项"""
        try:
            return self.queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()


class MultiGPUMemoryPool:
    """多GPU内存池管理器 - 为不同模块分配GPU内存"""
    
    def __init__(self, pool_size: int = 16):
        self.pool_size = pool_size
        self.gpu0_buffers = queue.Queue()  # GPU 0 缓冲区
        self.gpu1_buffers = queue.Queue()  # GPU 1 缓冲区
        self.used_buffers = set()
        self._lock = threading.Lock()
        
        print(f"🔧 初始化多GPU内存池 - 池大小: {pool_size}")
        
        # 为GPU 0预分配缓冲区 (轻量级模块)
        for _ in range(pool_size):
            buffer = np.zeros((1, 3, 640, 640), dtype=np.float32)
            self.gpu0_buffers.put(buffer)
        
        # 为GPU 1预分配缓冲区 (重量级模块)
        for _ in range(pool_size):
            buffer = np.zeros((1, 3, 1024, 1024), dtype=np.float32)
            self.gpu1_buffers.put(buffer)
    
    def get_gpu0_buffer(self) -> Optional[np.ndarray]:
        """获取GPU 0内存缓冲区"""
        try:
            buffer = self.gpu0_buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            return buffer
        except queue.Empty:
            print("⚠️  GPU 0内存池已满，创建临时缓冲区")
            return np.zeros((1, 3, 640, 640), dtype=np.float32)
    
    def get_gpu1_buffer(self) -> Optional[np.ndarray]:
        """获取GPU 1内存缓冲区"""
        try:
            buffer = self.gpu1_buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            return buffer
        except queue.Empty:
            print("⚠️  GPU 1内存池已满，创建临时缓冲区")
            return np.zeros((1, 3, 1024, 1024), dtype=np.float32)
    
    def return_gpu0_buffer(self, buffer: np.ndarray):
        """归还GPU 0内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.gpu0_buffers.put_nowait(buffer)
                    except queue.Full:
                        pass  # 忽略队列满的情况
    
    def return_gpu1_buffer(self, buffer: np.ndarray):
        """归还GPU 1内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.gpu1_buffers.put_nowait(buffer)
                    except queue.Full:
                        pass  # 忽略队列满的情况
    
    def get_pool_stats(self) -> Dict[str, int]:
        """获取内存池统计信息"""
        with self._lock:
            return {
                'gpu0_available': self.gpu0_buffers.qsize(),
                'gpu1_available': self.gpu1_buffers.qsize(),
                'used': len(self.used_buffers),
                'total': self.pool_size * 2
            }


class PerformanceMonitor:
    """性能监控器 - 记录各项性能指标"""
    
    def __init__(self):
        self.metrics = {
            'processing_speed': [],
            'gpu_utilization': {'gpu0': [], 'gpu1': []},
            'memory_usage': {'gpu0': [], 'gpu1': []},
            'module_timing': {},
            'total_detections': 0,
            'processed_images': 0
        }
        self._lock = threading.Lock()
    
    def record_module_timing(self, module_name: str, processing_time: float):
        """记录模块处理时间"""
        with self._lock:
            if module_name not in self.metrics['module_timing']:
                self.metrics['module_timing'][module_name] = []
            self.metrics['module_timing'][module_name].append(processing_time)
    
    def record_detection_count(self, count: int):
        """记录检测数量"""
        with self._lock:
            self.metrics['total_detections'] += count
    
    def record_processed_image(self):
        """记录处理的图像数量"""
        with self._lock:
            self.metrics['processed_images'] += 1
    
    def get_average_timing(self, module_name: str) -> float:
        """获取模块平均处理时间"""
        with self._lock:
            timings = self.metrics['module_timing'].get(module_name, [])
            return sum(timings) / len(timings) if timings else 0.0
    
    def generate_report(self) -> Dict:
        """生成性能报告"""
        with self._lock:
            report = {
                'total_detections': self.metrics['total_detections'],
                'processed_images': self.metrics['processed_images'],
                'module_timings': {}
            }
            
            for module_name, timings in self.metrics['module_timing'].items():
                if timings:
                    report['module_timings'][module_name] = {
                        'average': sum(timings) / len(timings),
                        'min': min(timings),
                        'max': max(timings),
                        'count': len(timings)
                    }
            
            return report
