#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合检测流水线处理器

管理12个检测模块的并行流水线处理
"""

import os
import time
import threading
import cv2
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

from .data_structures import ComprehensivePipelineData, AsyncQueue, MultiGPUMemoryPool, PerformanceMonitor
from .gpu_resource_manager import GPUResourceManager
from .module_manager import ModuleManager
from .result_merger import ResultMerger
from .visualizer import ComprehensiveVisualizer


class ComprehensivePipelineProcessor:
    """综合检测流水线处理器 - 统一管理12个检测模块"""
    
    def __init__(self, preprocess_workers: int = 2, postprocess_workers: int = 2, 
                 queue_size: int = 32):
        
        print("🚀 初始化综合检测流水线处理器...")
        
        # 工作线程配置
        self.preprocess_workers = preprocess_workers
        self.postprocess_workers = postprocess_workers
        
        # 初始化核心组件
        self.gpu_manager = GPUResourceManager()
        self.module_manager = ModuleManager(self.gpu_manager)
        self.result_merger = ResultMerger()
        self.visualizer = ComprehensiveVisualizer()
        
        # 初始化队列系统
        self.preprocess_queue = AsyncQueue(queue_size)
        self.inference_queue = AsyncQueue(queue_size)
        self.postprocess_queue = AsyncQueue(queue_size)
        self.result_queue = AsyncQueue(queue_size)
        
        # 初始化内存池和监控
        self.memory_pool = MultiGPUMemoryPool(pool_size=queue_size)
        self.performance_monitor = PerformanceMonitor()
        
        # 控制标志
        self._stop_event = threading.Event()
        self._threads = []
        
        # 线程安全锁
        self._postprocess_lock = threading.Lock()
        
        print("✅ 综合检测流水线处理器初始化完成")
    
    def initialize_all_modules(self):
        """初始化所有检测模块"""
        print("🔄 开始初始化所有检测模块...")
        
        # 验证GPU资源分配
        if not self.gpu_manager.validate_allocation():
            raise RuntimeError("GPU资源分配验证失败")
        
        # 加载所有模块
        self.module_manager.load_all_modules()
        
        print("✅ 所有检测模块初始化完成")
    
    def process_images_pipeline(self, image_paths: List[str], output_dir: str) -> Dict:
        """使用流水线处理图像列表"""
        
        if not image_paths:
            print("⚠️  没有图像需要处理")
            return {}
        
        print(f"🚀 开始综合检测流水线处理 - {len(image_paths)}张图像")
        
        total_start_time = time.time()
        
        # 启动流水线
        self._start_pipeline(output_dir)
        
        try:
            # 将所有图像路径放入预处理队列
            print(f"📥 将 {len(image_paths)} 张图像放入处理队列...")
            for image_path in image_paths:
                pipeline_data = ComprehensivePipelineData(image_path=image_path)
                
                # 等待队列有空间
                while not self.preprocess_queue.put(pipeline_data, timeout=0.1):
                    if self._stop_event.is_set():
                        break
                    time.sleep(0.01)
            
            # 等待所有任务完成
            print("⏳ 等待所有任务完成...")
            processed_count = 0
            total_detections = 0
            
            # 监控处理进度
            last_progress_time = time.time()
            while processed_count < len(image_paths):
                result_item = self.result_queue.get(timeout=2.0)
                if result_item is not None:
                    processed_count += 1
                    
                    if result_item.detection_summary:
                        total_detections += result_item.detection_summary.get('total_detections', 0)
                        self.performance_monitor.record_detection_count(
                            result_item.detection_summary.get('total_detections', 0)
                        )
                    
                    self.performance_monitor.record_processed_image()
                    
                    # 显示进度
                    current_time = time.time()
                    progress = (processed_count / len(image_paths)) * 100
                    if (current_time - last_progress_time > 5.0) or (processed_count % max(1, len(image_paths) // 10) == 0):
                        # 获取队列状态
                        preprocess_size = self.preprocess_queue.qsize()
                        inference_size = self.inference_queue.qsize()
                        postprocess_size = self.postprocess_queue.qsize()
                        memory_stats = self.memory_pool.get_pool_stats()
                        
                        print(f"📊 进度: {processed_count}/{len(image_paths)} ({progress:.1f}%) | "
                              f"队列: 预处理{preprocess_size} 推理{inference_size} 后处理{postprocess_size} | "
                              f"内存池: {memory_stats['gpu0_available']}+{memory_stats['gpu1_available']}/{memory_stats['total']}")
                        last_progress_time = current_time
                
                # 检查是否超时
                if time.time() - total_start_time > 600:  # 10分钟超时
                    print("⚠️  处理超时，强制停止")
                    break
            
            total_time = time.time() - total_start_time
            
            # 生成性能报告
            performance_report = self._generate_performance_report(len(image_paths), total_detections, total_time)
            
            return performance_report
            
        finally:
            # 停止流水线
            self._stop_pipeline()
    
    def _start_pipeline(self, output_dir: str):
        """启动流水线处理"""
        print("🚀 启动综合检测流水线...")
        
        # 启动预处理工作线程
        for i in range(self.preprocess_workers):
            thread = threading.Thread(target=self._preprocess_worker, args=(i,))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)
        
        # 启动推理工作线程（GPU 0和GPU 1各一个）
        for gpu_id in [0, 1]:
            thread = threading.Thread(target=self._inference_worker, args=(gpu_id,))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)
        
        # 启动后处理工作线程
        for i in range(self.postprocess_workers):
            thread = threading.Thread(target=self._postprocess_worker, args=(i, output_dir))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)
        
        print(f"✅ 综合检测流水线已启动 - 总线程数: {len(self._threads)}")
    
    def _stop_pipeline(self):
        """停止流水线处理"""
        print("🛑 停止综合检测流水线...")
        self._stop_event.set()
        
        # 等待所有线程结束
        for thread in self._threads:
            thread.join(timeout=3.0)
        
        print("✅ 综合检测流水线已停止")
    
    def _preprocess_worker(self, worker_id: int):
        """预处理工作线程"""
        print(f"🔵 启动预处理工作线程 {worker_id}")
        
        while not self._stop_event.is_set():
            # 从预处理队列获取任务
            item = self.preprocess_queue.get(timeout=0.1)
            if item is None:
                continue
                
            try:
                start_time = time.time()
                
                # 读取图像
                image = cv2.imread(item.image_path)
                if image is None:
                    print(f"⚠️  [预处理{worker_id}] 无法读取图像: {item.image_path}")
                    continue
                
                # 获取图像尺寸
                height, width = image.shape[:2]
                item.width = width
                item.height = height
                item.image_data = image
                item.stage = "preprocessed"
                
                # 放入推理队列
                if not self.inference_queue.put(item, timeout=1.0):
                    print(f"⚠️  [预处理{worker_id}] 推理队列已满，丢弃任务")
                    continue
                
                preprocess_time = time.time() - start_time
                self.performance_monitor.record_module_timing('preprocess', preprocess_time)
                
            except Exception as e:
                print(f"❌ [预处理{worker_id}] 处理异常: {e}")
                continue
        
        print(f"🔵 预处理工作线程 {worker_id} 已停止")
    
    def _inference_worker(self, gpu_id: int):
        """推理工作线程"""
        print(f"🔶 启动GPU{gpu_id}推理工作线程")
        
        while not self._stop_event.is_set():
            # 从推理队列获取任务
            item = self.inference_queue.get(timeout=0.1)
            if item is None:
                continue
                
            try:
                start_time = time.time()
                
                # 使用模块管理器处理所有模块
                item = self.module_manager.process_image_with_all_modules(item)
                item.stage = "inferred"
                
                # 放入后处理队列
                if not self.postprocess_queue.put(item, timeout=1.0):
                    print(f"⚠️  [推理GPU{gpu_id}] 后处理队列已满，丢弃任务")
                    continue
                
                inference_time = time.time() - start_time
                self.performance_monitor.record_module_timing('inference', inference_time)
                
            except Exception as e:
                print(f"❌ [推理GPU{gpu_id}] 处理异常: {e}")
                continue
        
        print(f"🔶 GPU{gpu_id}推理工作线程已停止")
    
    def _postprocess_worker(self, worker_id: int, output_dir: str):
        """后处理工作线程"""
        print(f"🟢 启动后处理工作线程 {worker_id}")
        
        while not self._stop_event.is_set():
            # 从后处理队列获取任务
            item = self.postprocess_queue.get(timeout=0.1)
            if item is None:
                continue
                
            try:
                start_time = time.time()
                
                # 使用线程锁确保后处理的线程安全
                with self._postprocess_lock:
                    # 合并所有模块的结果
                    item = self.result_merger.merge_all_results(item)
                    
                    # 生成综合JSON
                    json_data = self.result_merger.create_comprehensive_json(item, item.image_path)
                    
                    # 保存JSON文件
                    base_name = os.path.splitext(os.path.basename(item.image_path))[0]
                    json_path = os.path.join(output_dir, f"{base_name}_comprehensive.json")
                    
                    with open(json_path, 'w', encoding='utf-8') as f:
                        import json
                        json.dump(json_data, f, ensure_ascii=False, indent=2)
                    
                    # 生成可视化结果
                    vis_path = os.path.join(output_dir, f"{base_name}_comprehensive.png")
                    self.visualizer.visualize_comprehensive_results(item, vis_path)
                
                item.stage = "postprocessed"
                
                # 放入结果队列
                self.result_queue.put(item, timeout=1.0)
                
                postprocess_time = time.time() - start_time
                self.performance_monitor.record_module_timing('postprocess', postprocess_time)
                
                total_detections = item.detection_summary.get('total_detections', 0) if item.detection_summary else 0
                print(f"✅ [后处理{worker_id}] 处理完成: {os.path.basename(item.image_path)} - 检测到 {total_detections} 个目标")
                
            except Exception as e:
                print(f"❌ [后处理{worker_id}] 处理异常: {e}")
                continue
        
        print(f"🟢 后处理工作线程 {worker_id} 已停止")
    
    def _generate_performance_report(self, total_images: int, total_detections: int, total_time: float) -> Dict:
        """生成性能报告"""
        
        report = self.performance_monitor.generate_report()
        
        # 添加总体统计
        report.update({
            'total_images': total_images,
            'total_detections': total_detections,
            'total_time': total_time,
            'average_time_per_image': total_time / total_images if total_images > 0 else 0,
            'processing_speed': total_images / total_time if total_time > 0 else 0,
            'gpu_resource_stats': self.gpu_manager.get_resource_stats()
        })
        
        # 打印性能统计
        self._print_performance_stats(report)
        
        return report
    
    def _print_performance_stats(self, report: Dict):
        """打印性能统计信息"""
        print(f"\n📊 综合检测流水线性能统计:")
        print(f"   总图像数量: {report['total_images']}")
        print(f"   总检测数量: {report['total_detections']}")
        print(f"   总处理时间: {report['total_time']:.2f}秒")
        print(f"   平均每张图像: {report['average_time_per_image']:.2f}秒")
        print(f"   处理速度: {report['processing_speed']:.2f} 张/秒")
        
        # 模块时间统计
        if 'module_timings' in report:
            print(f"   模块平均处理时间:")
            for module_name, timing in report['module_timings'].items():
                print(f"     {module_name}: {timing['average']:.3f}秒")
        
        # GPU资源统计
        gpu_stats = report.get('gpu_resource_stats', {})
        if gpu_stats:
            print(f"   GPU资源使用:")
            print(f"     GPU 0: {gpu_stats['gpu0']['modules']}个模块, {gpu_stats['gpu0']['memory_gb']:.1f}GB ({gpu_stats['gpu0']['utilization']:.1f}%)")
            print(f"     GPU 1: {gpu_stats['gpu1']['modules']}个模块, {gpu_stats['gpu1']['memory_gb']:.1f}GB ({gpu_stats['gpu1']['utilization']:.1f}%)")
    
    def cleanup(self):
        """清理资源"""
        print("🧹 清理综合检测流水线资源...")
        
        # 卸载所有模块
        self.module_manager.unload_all_modules()
        
        print("✅ 资源清理完成")
