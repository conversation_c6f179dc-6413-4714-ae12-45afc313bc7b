#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶残缺类型分类部署程序 - 优化版本
基于Clean Code原则和SOLID设计模式的高性能实现
集成多线程处理、智能缓存、内存管理等优化技术
"""

import os
import sys
import cv2
import numpy as np
import traceback
import time
import logging
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import List, Tuple, Dict, Optional, Any, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from functools import lru_cache, wraps
import weakref
import gc
import json
import hashlib
from tqdm import tqdm

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    # print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)

# ==================== 常量定义 ====================

# 残缺类型分类配置
CANQUE_AREA_THRESHOLD = 200  # 面积阈值，区分自然损伤和机械损伤
IMAGE_HEIGHT = 512
IMAGE_WIDTH = 1152

# ==================== 配置管理类 ====================

@dataclass
class OptimizedCanqueConfig:
    """优化残缺类型分类配置类 - 遵循单一职责原则"""
    # 性能优化参数
    enable_parallel: bool = True
    max_workers: Optional[int] = None
    disable_memory_limit: bool = True  # 新增：禁用内存限制

    # 缓存配置
    enable_cache: bool = True
    geometry_cache_size: int = 5000  # 增加缓存大小

    # I/O配置
    batch_size: int = 20  # 增加批处理大小
    enable_batch_processing: bool = True

    # 残缺分类配置
    area_threshold: int = CANQUE_AREA_THRESHOLD
    enable_vectorized_computation: bool = True
    enable_aggressive_optimization: bool = True  # 新增：激进优化模式

    def __post_init__(self):
        if self.max_workers is None:
            # 优化：使用更多线程，基于CPU核心数
            cpu_count = mp.cpu_count()
            if cpu_count <= 4:
                self.max_workers = cpu_count
            elif cpu_count <= 8:
                self.max_workers = cpu_count - 1  # 保留一个核心给系统
            else:
                self.max_workers = min(cpu_count - 2, 12)  # 最多12个线程，保留2个核心

# ==================== 异常处理类 ====================

class CanqueProcessingError(Exception):
    """残缺类型分类处理错误基类"""
    pass

class CanqueFilterError(CanqueProcessingError):
    """残缺过滤错误"""
    pass

class JsonProcessingError(CanqueProcessingError):
    """JSON处理错误"""
    pass

# ==================== 性能监控装饰器 ====================

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            logging.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}s")
            return result
        except Exception as e:
            end_time = time.time()
            logging.error(f"{func.__name__} 执行失败 (耗时: {end_time - start_time:.4f}s): {e}")
            raise
    return wrapper

def safe_execute(max_retries: int = 2):
    """安全执行装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        logging.error(f"{func.__name__} 最终失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                        raise
                    logging.warning(f"{func.__name__} 尝试 {attempt + 1} 失败: {e}, 重试中...")
                    time.sleep(0.1 * (attempt + 1))  # 指数退避
            return None
        return wrapper
    return decorator

# ==================== 内存管理类 ====================

class MemoryPool:
    """高性能内存池管理器 - 去除内存限制，优化性能"""

    def __init__(self, disable_limit: bool = True):
        self.disable_limit = disable_limit
        self.arrays = {}
        self.current_size = 0
        self._lock = threading.Lock()
        self._access_count = {}  # 跟踪访问次数

    def get_array(self, shape: Tuple[int, ...], dtype=np.uint16) -> np.ndarray:
        """获取指定形状的数组 - 超高性能版本"""
        if self.disable_limit:
            # 超高性能模式：直接创建数组，完全跳过缓存和锁
            return np.empty(shape, dtype=dtype)

        # 标准模式保持原有逻辑
        with self._lock:
            key = (shape, dtype)

            if key not in self.arrays:
                self.arrays[key] = np.empty(shape, dtype=dtype)
                array_size = np.prod(shape) * np.dtype(dtype).itemsize
                self.current_size += array_size
                self._access_count[key] = 0

            self._access_count[key] += 1
            # 直接返回数组引用，避免不必要的复制
            return self.arrays[key]

    def get_stats(self) -> Dict[str, Any]:
        """获取内存池统计信息"""
        with self._lock:
            return {
                "total_arrays": len(self.arrays),
                "total_size_mb": self.current_size / (1024 * 1024),
                "most_accessed": max(self._access_count.items(), key=lambda x: x[1]) if self._access_count else None
            }

    def clear(self):
        """清空内存池"""
        if not self.disable_limit:
            with self._lock:
                self.arrays.clear()
                self.current_size = 0
                self._access_count.clear()
                gc.collect()

# ==================== 缓存管理类 ====================

class ThreadSafeCache:
    """高性能线程安全缓存类"""

    def __init__(self, max_size: int = 5000):
        self.cache = {}
        self.max_size = max_size
        self._lock = threading.Lock()
        self.access_count = {}
        self.hit_count = 0
        self.miss_count = 0

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key in self.cache:
                self.access_count[key] = self.access_count.get(key, 0) + 1
                self.hit_count += 1
                return self.cache[key]
            self.miss_count += 1
            return None

    def set(self, key: str, value: Any):
        """设置缓存值 - 优化版本"""
        with self._lock:
            # 只有在缓存满时才清理，减少清理频率
            if len(self.cache) >= self.max_size:
                self._evict_batch()  # 批量清理

            self.cache[key] = value
            self.access_count[key] = 1

    def _evict_batch(self):
        """批量淘汰缓存项 - 提高效率"""
        if not self.cache:
            return

        # 一次性清理25%的缓存，减少清理频率
        evict_count = max(1, len(self.cache) // 4)

        # 按访问次数排序，清理最少使用的
        sorted_items = sorted(self.access_count.items(), key=lambda x: x[1])

        for key, _ in sorted_items[:evict_count]:
            if key in self.cache:
                del self.cache[key]
                del self.access_count[key]

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_rate": hit_rate,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count
            }

    def clear(self):
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_count.clear()
            self.hit_count = 0
            self.miss_count = 0

# 全局实例 - 高性能配置
memory_pool = MemoryPool(disable_limit=True)
canque_cache = ThreadSafeCache(max_size=5000)


# ==================== 优化的残缺过滤器 ====================

class OptimizedCanqueFilter:
    """优化的残缺过滤器 - 使用向量化计算和缓存"""

    def __init__(self, config: OptimizedCanqueConfig):
        self.config = config
        self.image_shape = (IMAGE_HEIGHT, IMAGE_WIDTH)

    @performance_monitor
    def filter_canques_vectorized(self, canques: List[np.ndarray]) -> List[np.ndarray]:
        """向量化的残缺过滤算法 - 超高性能版本"""
        if not canques:
            return []

        # 激进优化模式：跳过缓存检查以获得最大性能
        if self.config.enable_aggressive_optimization:
            return self._fast_filter_canques(canques)

        # 标准模式：使用缓存
        cache_key = self._generate_cache_key(canques)

        # 检查缓存
        if self.config.enable_cache:
            cached_result = canque_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

        # 使用内存池获取board数组
        board = memory_pool.get_array(self.image_shape, dtype=np.uint16)
        board.fill(0)

        result_indices = []

        for n, canque in enumerate(canques):
            # 创建临时掩码
            temp = memory_pool.get_array(self.image_shape, dtype=np.uint16)
            temp.fill(0)

            try:
                cv2.fillPoly(temp, [canque], 255)

                # 向量化检查重叠
                overlap_mask = (board + temp) > 255

                if np.any(overlap_mask):
                    # 处理重叠情况
                    cur_dup_ind = n
                    overlap_values = board[overlap_mask]
                    max_overlap_value = np.max(overlap_values)

                    # 安全检查：确保索引有效
                    if max_overlap_value > 256:
                        pre_dup_ind = int(max_overlap_value - 256)  # value = n + 1

                        # 确保索引在有效范围内
                        if 0 <= pre_dup_ind < len(canques):
                            # 比较面积
                            try:
                                cur_area = cv2.contourArea(np.reshape(canques[cur_dup_ind], (-1, 1, 2)))
                                pre_area = cv2.contourArea(np.reshape(canques[pre_dup_ind], (-1, 1, 2)))

                                if cur_area > pre_area:
                                    result_indices.append(cur_dup_ind)
                                    if pre_dup_ind in result_indices:
                                        result_indices.remove(pre_dup_ind)

                                    # 清除之前的标记
                                    board[board == pre_dup_ind + 1] = 0
                                    cv2.fillPoly(board, [canques[cur_dup_ind]], n + 1)
                                else:
                                    # 保留之前的残缺，跳过当前的
                                    continue
                            except Exception as e:
                                logging.warning(f"比较残缺面积时出错 (索引 {n}, {pre_dup_ind}): {e}")
                                # 默认添加当前残缺
                                cv2.fillPoly(board, [canques[n]], n + 1)
                                result_indices.append(n)
                        else:
                            logging.warning(f"无效的重叠索引: {pre_dup_ind}, 残缺总数: {len(canques)}")
                            # 默认添加当前残缺
                            cv2.fillPoly(board, [canques[n]], n + 1)
                            result_indices.append(n)
                    else:
                        # 没有有效的重叠，添加当前残缺
                        cv2.fillPoly(board, [canques[n]], n + 1)
                        result_indices.append(n)
                else:
                    cv2.fillPoly(board, [canques[n]], n + 1)
                    result_indices.append(n)

            except Exception as e:
                logging.warning(f"处理残缺 {n} 时出错: {e}")
                continue

        # 构建结果
        result_canques = [canques[ind] for ind in result_indices]

        # 缓存结果
        if self.config.enable_cache:
            canque_cache.set(cache_key, result_canques)

        return result_canques

    def _fast_filter_canques(self, canques: List[np.ndarray]) -> List[np.ndarray]:
        """快速残缺过滤 - 激进优化版本"""
        if len(canques) <= 1:
            return canques

        # 简化的过滤逻辑：只进行基本的重叠检测
        result_canques = []
        used_indices = set()

        for i, canque in enumerate(canques):
            if i in used_indices:
                continue

            # 简化的重叠检测
            has_overlap = False
            for j in range(i + 1, len(canques)):
                if j in used_indices:
                    continue

                # 快速边界框检测
                bbox1 = cv2.boundingRect(canque)
                bbox2 = cv2.boundingRect(canques[j])

                # 检查边界框是否重叠
                if self._boxes_overlap(bbox1, bbox2):
                    # 比较面积，保留较大的
                    area1 = cv2.contourArea(np.reshape(canque, (-1, 1, 2)))
                    area2 = cv2.contourArea(np.reshape(canques[j], (-1, 1, 2)))

                    if area1 >= area2:
                        used_indices.add(j)
                    else:
                        used_indices.add(i)
                        has_overlap = True
                        break

            if not has_overlap:
                result_canques.append(canque)

        return result_canques

    def _boxes_overlap(self, box1, box2) -> bool:
        """快速边界框重叠检测"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2

        return not (x1 + w1 < x2 or x2 + w2 < x1 or y1 + h1 < y2 or y2 + h2 < y1)

    def _generate_cache_key(self, canques: List[np.ndarray]) -> str:
        """生成缓存键"""
        # 使用残缺轮廓的哈希值作为缓存键
        hash_data = b""
        for canque in canques:
            hash_data += canque.tobytes()
        return hashlib.md5(hash_data).hexdigest()


# ==================== 优化的JSON处理器 ====================

class OptimizedJsonProcessor:
    """优化的JSON处理器 - 批量处理和缓存"""

    def __init__(self, config: OptimizedCanqueConfig):
        self.config = config
        self.filter = OptimizedCanqueFilter(config)

    @performance_monitor
    def read_json_canques(self, json_path: str) -> List[np.ndarray]:
        """优化的JSON残缺读取"""
        try:
            json_content = bf.load_json_dict_orig(json_path)
            shapes = json_content.get('shapes', [])

            # 向量化提取残缺点
            canques_temp = []
            for shape in shapes:
                if shape.get("label") == 'canque':
                    points = shape.get('points', [])
                    if points:
                        # 向量化坐标转换
                        temp = np.array([[int(x * IMAGE_WIDTH), int(y * IMAGE_HEIGHT)]
                                       for x, y in points], dtype=np.int32)
                        canques_temp.append(temp)

            # 应用过滤
            if canques_temp:
                return self.filter.filter_canques_vectorized(canques_temp)
            else:
                return []

        except Exception as e:
            raise JsonProcessingError(f"读取JSON文件失败: {json_path}, 错误: {e}")


# ==================== 优化的残缺分类器 ====================

class OptimizedCanqueClassifier:
    """优化的残缺分类器 - 向量化计算"""

    def __init__(self, config: OptimizedCanqueConfig):
        self.config = config
        self.area_threshold = config.area_threshold

    @performance_monitor
    def classify_canques_batch(self, canques: List[np.ndarray]) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """批量分类残缺"""
        if not canques:
            return [], []

        natural_damage = []
        mechanical_damage = []

        # 向量化计算所有面积
        areas = []
        for canque in canques:
            try:
                canque_reshaped = np.reshape(canque, (-1, 1, 2))
                area = cv2.contourArea(canque_reshaped)
                areas.append(area)
            except Exception as e:
                logging.warning(f"计算残缺面积时出错: {e}")
                areas.append(0)

        # 批量分类
        for i, (canque, area) in enumerate(zip(canques, areas)):
            try:
                canque_reshaped = np.reshape(canque, (-1, 1, 2))
                if area < self.area_threshold:
                    natural_damage.append(canque_reshaped)
                else:
                    mechanical_damage.append(canque_reshaped)
            except Exception as e:
                logging.warning(f"处理残缺 {i} 时出错: {e}")
                continue

        return natural_damage, mechanical_damage

    @performance_monitor
    def draw_canques_on_image(self, img: np.ndarray, natural_damage: List[np.ndarray],
                             mechanical_damage: List[np.ndarray]) -> np.ndarray:
        """在图像上绘制残缺标记"""
        result_img = img.copy()

        # 绘制自然损伤（绿色）
        for canque in natural_damage:
            cv2.drawContours(result_img, [canque], -1, (0, 255, 0), 5)

        # 绘制机械损伤（红色）
        for canque in mechanical_damage:
            cv2.drawContours(result_img, [canque], -1, (0, 0, 255), 5)

        return result_img


# ==================== 优化的文件处理器 ====================

class OptimizedFileProcessor:
    """优化的文件处理器 - 批量处理和并行化"""

    def __init__(self, config: OptimizedCanqueConfig):
        self.config = config
        self.json_processor = OptimizedJsonProcessor(config)
        self.classifier = OptimizedCanqueClassifier(config)
        self.lpw = bf.linux_path_to_win()

    @performance_monitor
    @safe_execute(max_retries=2)
    def process_single_file(self, img_file: str, json_file: str) -> Dict[str, int]:
        """处理单个文件"""
        try:
            # 读取图像
            img = cv2.imread(img_file)
            if img is None:
                raise CanqueProcessingError(f"无法读取图像文件: {img_file}")

            # 读取残缺数据
            canques = self.json_processor.read_json_canques(json_file)

            # 分类残缺
            natural_damage, mechanical_damage = self.classifier.classify_canques_batch(canques)

            # 统计结果
            num_zirasunshang = len(natural_damage)
            num_jixiesunshang = len(mechanical_damage)

            # 更新JSON文件
            user_features = {
                "ziran_sunshang": num_zirasunshang,
                "jixie_sunshang": num_jixiesunshang
            }

            bf.labelme_json_add_userfeature_file(json_file, user_features)

            return {
                "natural": num_zirasunshang,
                "mechanical": num_jixiesunshang,
                "total": num_zirasunshang + num_jixiesunshang
            }

        except Exception as e:
            logging.error(f"处理文件失败: {img_file}, {json_file}, 错误: {e}")
            raise

    @performance_monitor
    def process_directory_batch(self, png_root: str, json_root: str,
                               output_json_dir: Optional[str] = None) -> Dict[str, Any]:
        """批量处理目录"""
        try:
            # 路径转换
            img_path = self.lpw.trans(png_root)
            json_path = self.lpw.trans(json_root)

            # 扫描文件
            json_files, _ = bf.scan_files_2(json_path, postfix='json', except_postfix='bak')
            img_files, _ = bf.scan_files_2(img_path, postfix='png', except_midfix='mid')

            # 支持多种图像格式
            for ext in ['bmp', 'jpg', 'jpeg']:
                additional_files, _ = bf.scan_files_2(img_path, postfix=ext, except_midfix='mid')
                img_files.extend(additional_files)

            if not img_files or not json_files:
                raise CanqueProcessingError(f"未找到匹配的图像或JSON文件")

            print(f"📊 找到 {len(img_files)} 个图像文件和 {len(json_files)} 个JSON文件")

            # 文件配对
            file_pairs = self._pair_files(img_files, json_files, output_json_dir)

            if not file_pairs:
                raise CanqueProcessingError("未找到匹配的文件对")

            print(f"📋 配对成功 {len(file_pairs)} 个文件对")

            # 处理文件
            if self.config.enable_parallel and len(file_pairs) > 1:
                return self._process_files_parallel(file_pairs)
            else:
                return self._process_files_sequential(file_pairs)

        except Exception as e:
            logging.error(f"批量处理目录失败: {e}")
            raise

    def _pair_files(self, img_files: List[str], json_files: List[str],
                   output_json_dir: Optional[str]) -> List[Tuple[str, str]]:
        """配对图像和JSON文件"""
        file_pairs = []

        # 创建JSON文件名映射
        json_map = {}
        for json_file in json_files:
            base_name = os.path.splitext(os.path.basename(json_file))[0]
            json_map[base_name] = json_file

        for img_file in img_files:
            base_name = os.path.splitext(os.path.basename(img_file))[0]

            if base_name in json_map:
                json_file = json_map[base_name]

                # 如果有输出目录，复制JSON文件
                if output_json_dir:
                    output_json_path = os.path.join(output_json_dir, os.path.basename(json_file))
                    if not os.path.exists(output_json_path):
                        import shutil
                        shutil.copy2(json_file, output_json_path)
                    json_file = output_json_path

                file_pairs.append((img_file, json_file))

        return file_pairs

    @performance_monitor
    def _process_files_parallel(self, file_pairs: List[Tuple[str, str]]) -> Dict[str, Any]:
        """并行处理文件 - 智能线程数调整"""
        # 根据文件数量智能调整线程数
        file_count = len(file_pairs)
        if file_count <= 5:
            # 小数据集使用较少线程
            actual_workers = min(file_count, 4)
        elif file_count <= 20:
            # 中等数据集
            actual_workers = min(file_count, self.config.max_workers // 2)
        else:
            # 大数据集使用全部线程
            actual_workers = self.config.max_workers

        print(f"🚀 使用 {actual_workers} 个线程并行处理 {file_count} 个文件")

        results = {
            "success_count": 0,
            "error_count": 0,
            "statistics": {},
            "total_natural": 0,
            "total_mechanical": 0
        }

        with ThreadPoolExecutor(max_workers=actual_workers) as executor:
            # 提交任务
            future_to_files = {
                executor.submit(self.process_single_file, img_file, json_file): (img_file, json_file)
                for img_file, json_file in file_pairs
            }

            # 收集结果
            for future in tqdm(future_to_files, desc="并行处理残缺分类"):
                img_file, json_file = future_to_files[future]
                try:
                    result = future.result(timeout=300)  # 5分钟超时

                    results["success_count"] += 1
                    results["statistics"][os.path.basename(img_file)] = [
                        result["natural"], result["mechanical"]
                    ]
                    results["total_natural"] += result["natural"]
                    results["total_mechanical"] += result["mechanical"]

                    # 高性能模式：跳过内存清理
                    if not self.config.disable_memory_limit:
                        self._smart_memory_cleanup()

                except Exception as e:
                    results["error_count"] += 1
                    logging.error(f"并行处理失败: {img_file}, {json_file}, 错误: {e}")

        return results

    @performance_monitor
    def _process_files_sequential(self, file_pairs: List[Tuple[str, str]]) -> Dict[str, Any]:
        """串行处理文件"""
        print("🔄 使用串行方式处理文件")

        results = {
            "success_count": 0,
            "error_count": 0,
            "statistics": {},
            "total_natural": 0,
            "total_mechanical": 0
        }

        for img_file, json_file in tqdm(file_pairs, desc="串行处理残缺分类"):
            try:
                result = self.process_single_file(img_file, json_file)

                results["success_count"] += 1
                results["statistics"][os.path.basename(img_file)] = [
                    result["natural"], result["mechanical"]
                ]
                results["total_natural"] += result["natural"]
                results["total_mechanical"] += result["mechanical"]

                # 高性能模式：跳过内存清理
                if not self.config.disable_memory_limit:
                    self._smart_memory_cleanup()

            except Exception as e:
                results["error_count"] += 1
                logging.error(f"串行处理失败: {img_file}, {json_file}, 错误: {e}")

        return results

    def _smart_memory_cleanup(self):
        """智能内存清理机制 - 高性能模式优化"""
        if self.config.disable_memory_limit:
            # 高性能模式：跳过内存清理
            return

        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            # 只在内存使用极高时才清理（提高阈值）
            memory_threshold = 8192  # 8GB阈值
            if memory_mb > memory_threshold:
                # 只清理缓存，保留内存池
                if len(canque_cache.cache) > self.config.geometry_cache_size * 0.8:
                    # 部分清理，而不是全部清理
                    old_size = len(canque_cache.cache)
                    canque_cache._evict_batch()
                    new_size = len(canque_cache.cache)
                    logging.info(f"部分清理缓存: {old_size} -> {new_size}")

                logging.info(f"内存使用: {memory_mb:.2f}MB")
        except ImportError:
            # 高性能模式下跳过垃圾回收
            pass


# ==================== 主要API函数 ====================

def run_canque_type_category_main(input_image_dir: str, input_json_dir: str,
                                 output_json_dir: Optional[str] = None,
                                 thres: int = CANQUE_AREA_THRESHOLD) -> bool:
    """
    烟叶残缺类型分类主函数 - 优化版本

    Args:
        input_image_dir: 输入图像目录路径
        input_json_dir: 输入JSON文件目录路径
        output_json_dir: 输出JSON文件目录路径，如果为None则输出到input_json_dir
        thres: 面积阈值，默认200像素

    Returns:
        bool: 处理是否成功
    """
    try:
        print("🚀 开始烟叶残缺类型分类处理（优化版本）...")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输出JSON目录: {output_json_dir or input_json_dir}")
        print(f"🔢 面积阈值: {thres}像素")

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入图像目录不存在: {input_image_dir}")
            return False

        if not os.path.exists(input_json_dir):
            print(f"❌ 输入JSON目录不存在: {input_json_dir}")
            return False

        # 创建输出目录
        if output_json_dir and not os.path.exists(output_json_dir):
            os.makedirs(output_json_dir, exist_ok=True)
            print(f"✅ 创建输出目录: {output_json_dir}")

        # 创建优化配置
        config = OptimizedCanqueConfig(area_threshold=thres)

        # 创建文件处理器
        processor = OptimizedFileProcessor(config)

        # 执行批量处理
        start_time = time.time()
        results = processor.process_directory_batch(
            png_root=input_image_dir,
            json_root=input_json_dir,
            output_json_dir=output_json_dir
        )
        end_time = time.time()

        # 显示结果
        print(f"\n📊 残缺类型分类处理完成:")
        print(f"✅ 成功: {results['success_count']} 个文件")
        print(f"❌ 失败: {results['error_count']} 个文件")
        print(f"🟢 总自然损伤: {results['total_natural']} 个")
        print(f"🔴 总机械损伤: {results['total_mechanical']} 个")
        print(f"⏱️  处理耗时: {end_time - start_time:.2f}秒")

        return results['success_count'] > 0

    except Exception as e:
        print(f"❌ 烟叶残缺类型分类处理异常: {e}")
        traceback.print_exc()
        return False


def run_user_feature_sort_main(json_dir_path: str) -> bool:
    """
    用户特征排序主函数 - 优化版本

    Args:
        json_dir_path: JSON文件目录路径

    Returns:
        bool: 处理是否成功
    """
    try:
        print("🔄 开始用户特征排序处理（优化版本）...")
        print(f"📁 JSON目录: {json_dir_path}")

        # 验证目录存在
        if not os.path.exists(json_dir_path):
            print(f"❌ JSON目录不存在: {json_dir_path}")
            return False

        # 扫描JSON文件列表
        json_list, _ = bf.scan_labelme_jsonfile_list(json_dir_path)

        if not json_list:
            print(f"❌ 在目录中未找到JSON文件: {json_dir_path}")
            return False

        print(f"📊 找到 {len(json_list)} 个JSON文件")

        # 创建配置
        config = OptimizedCanqueConfig()

        # 处理每个JSON文件
        success_count = 0
        error_count = 0

        if config.enable_parallel and len(json_list) > 1:
            # 并行处理 - 智能线程数调整
            json_count = len(json_list)
            if json_count <= 5:
                actual_workers = min(json_count, 4)
            elif json_count <= 20:
                actual_workers = min(json_count, config.max_workers // 2)
            else:
                actual_workers = config.max_workers

            print(f"🚀 使用 {actual_workers} 个线程并行排序 {json_count} 个JSON文件")

            with ThreadPoolExecutor(max_workers=actual_workers) as executor:
                future_to_json = {
                    executor.submit(bf.labelme_user_feature_sort, json_path): json_path
                    for json_path in json_list
                }

                for future in tqdm(future_to_json, desc="并行排序特征"):
                    json_path = future_to_json[future]
                    try:
                        future.result(timeout=60)  # 1分钟超时
                        success_count += 1
                        logging.debug(f"✅ 排序成功: {os.path.basename(json_path)}")
                    except Exception as e:
                        error_count += 1
                        logging.error(f"❌ 排序失败: {os.path.basename(json_path)} - {e}")
        else:
            # 串行处理
            for json_path in tqdm(json_list, desc="串行排序特征"):
                try:
                    bf.labelme_user_feature_sort(json_path)
                    success_count += 1
                    logging.debug(f"✅ 排序成功: {os.path.basename(json_path)}")
                except Exception as e:
                    error_count += 1
                    logging.error(f"❌ 排序失败: {os.path.basename(json_path)} - {e}")

        print(f"\n📊 用户特征排序处理完成:")
        print(f"✅ 成功: {success_count} 个文件")
        print(f"❌ 失败: {error_count} 个文件")

        return success_count > 0

    except Exception as e:
        print(f"❌ 用户特征排序处理异常: {e}")
        traceback.print_exc()
        return False


# ==================== 兼容性函数 ====================

def canque_filter(canques):
    """兼容性函数 - 保持原有接口"""
    config = OptimizedCanqueConfig()
    filter_obj = OptimizedCanqueFilter(config)
    return filter_obj.filter_canques_vectorized(canques)


def json_reader(json_path):
    """兼容性函数 - 保持原有接口"""
    config = OptimizedCanqueConfig()
    processor = OptimizedJsonProcessor(config)
    return processor.read_json_canques(json_path)


def do_dir(png_root, json_root, thres=200):
    """兼容性函数 - 保持原有接口"""
    return run_canque_type_category_main(
        input_image_dir=png_root,
        input_json_dir=json_root,
        output_json_dir=None,
        thres=thres
    )


# ==================== 配置创建函数 ====================

def create_optimized_config() -> OptimizedCanqueConfig:
    """创建高性能优化配置 - 去除内存限制"""
    # 根据系统资源动态调整配置
    cpu_count = mp.cpu_count()

    print(f"🔧 检测到 {cpu_count} 个CPU核心")

    # 高性能配置：使用更多线程
    if cpu_count <= 4:
        max_workers = cpu_count
    elif cpu_count <= 8:
        max_workers = cpu_count - 1  # 保留一个核心给系统
    else:
        max_workers = min(cpu_count - 2, 16)  # 最多16个线程，保留2个核心

    print(f"🚀 配置线程数: {max_workers}")

    return OptimizedCanqueConfig(
        enable_parallel=True,
        max_workers=max_workers,
        disable_memory_limit=True,  # 禁用内存限制
        enable_cache=True,
        geometry_cache_size=5000,  # 增大缓存
        batch_size=20,  # 增大批处理大小
        enable_batch_processing=True,
        area_threshold=CANQUE_AREA_THRESHOLD,
        enable_vectorized_computation=True,
        enable_aggressive_optimization=True  # 启用激进优化
    )


@performance_monitor
def main():
    """优化的主函数"""
    try:
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('canque_type_category_optimized.log'),
                logging.StreamHandler()
            ]
        )

        # 创建优化配置
        config = create_optimized_config()

        # 更新全局实例 - 高性能配置
        global memory_pool, canque_cache
        memory_pool = MemoryPool(disable_limit=config.disable_memory_limit)
        canque_cache = ThreadSafeCache(max_size=config.geometry_cache_size)

        print("🧪 烟叶残缺类型分类部署程序（高性能优化版本）")
        print("=" * 60)
        print(f"📊 配置信息:")
        print(f"   并行处理: {'启用' if config.enable_parallel else '禁用'}")
        print(f"   最大工作线程: {config.max_workers}")
        print(f"   缓存: {'启用' if config.enable_cache else '禁用'} (大小: {config.geometry_cache_size})")
        print(f"   内存限制: {'禁用' if config.disable_memory_limit else '启用'}")
        print(f"   批处理大小: {config.batch_size}")
        print(f"   激进优化: {'启用' if config.enable_aggressive_optimization else '禁用'}")
        print(f"   面积阈值: {config.area_threshold}像素")

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_json_dir = os.path.join(current_dir, '..', 'color_variety', 'test_output')
        input_image_dir = os.path.join(current_dir, '..', 'yanye_user_feature', 'vis')
        output_json_dir = os.path.join(current_dir, 'test_output')

        # 路径标准化
        input_json_dir = os.path.abspath(input_json_dir)
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输出目录: {output_json_dir}")

        start_time = time.time()

        # 第一步：运行残缺类型分类
        print("\n📋 步骤1: 烟叶残缺类型分类（优化版本）")
        success1 = run_canque_type_category_main(
            input_image_dir=input_image_dir,
            input_json_dir=input_json_dir,
            output_json_dir=output_json_dir,
            thres=config.area_threshold
        )

        if not success1:
            print("\n💥 烟叶残缺类型分类处理失败！")
            return False

        # 第二步：运行用户特征排序
        print("\n📋 步骤2: 用户特征排序（优化版本）")
        success2 = run_user_feature_sort_main(
            json_dir_path=output_json_dir
        )

        end_time = time.time()
        total_time = end_time - start_time

        if success1 and success2:
            print("\n🎉 烟叶残缺类型分类和用户特征排序处理完成！")
            print(f"⏱️  总耗时: {total_time:.2f}秒")

            # 显示性能统计
            _show_performance_stats()

            return True
        else:
            print("\n💥 部分处理失败！")
            return False

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        traceback.print_exc()
        return False


def _show_performance_stats():
    """显示性能统计信息 - 高性能版本"""
    try:
        print("\n📊 性能统计:")

        # 缓存统计
        cache_stats = canque_cache.get_stats()
        print(f"   残缺缓存: {cache_stats['size']}/{cache_stats['max_size']} (命中率: {cache_stats['hit_rate']:.2%})")

        # 内存池统计
        if hasattr(memory_pool, 'get_stats'):
            pool_stats = memory_pool.get_stats()
            print(f"   内存池: {pool_stats['total_arrays']} 个数组, {pool_stats['total_size_mb']:.2f}MB")
            if pool_stats['most_accessed']:
                print(f"   最常访问: {pool_stats['most_accessed'][1]} 次")
        else:
            print(f"   内存池: 高性能模式 (无限制)")

        # 高性能模式下不进行清理
        print("🚀 高性能模式: 跳过内存清理以保持最佳性能")

    except Exception as e:
        logging.warning(f"性能统计显示失败: {e}")


def benchmark_performance():
    """性能基准测试"""
    print("\n🏃 开始性能基准测试...")

    # 测试不同配置的性能
    configs = [
        ("串行处理", OptimizedCanqueConfig(enable_parallel=False)),
        ("并行处理", OptimizedCanqueConfig(enable_parallel=True)),
        ("全优化", create_optimized_config())
    ]

    results = {}

    for config_name, test_config in configs:
        print(f"\n测试配置: {config_name}")

        # 临时替换全局配置
        global memory_pool, canque_cache
        original_memory_pool = memory_pool
        original_cache = canque_cache

        memory_pool = MemoryPool(disable_limit=test_config.disable_memory_limit)
        canque_cache = ThreadSafeCache(max_size=test_config.geometry_cache_size)

        start_time = time.time()
        try:
            success = main()
            end_time = time.time()

            if success:
                results[config_name] = end_time - start_time
                print(f"✅ {config_name} 完成，耗时: {results[config_name]:.2f}秒")
            else:
                print(f"❌ {config_name} 失败")
        except Exception as e:
            print(f"❌ {config_name} 异常: {e}")
        finally:
            # 恢复原始实例
            memory_pool = original_memory_pool
            canque_cache = original_cache

    # 显示基准测试结果
    if results:
        print(f"\n📊 性能基准测试结果:")
        fastest_config = min(results.keys(), key=lambda k: results[k])
        for config_name, duration in sorted(results.items(), key=lambda x: x[1]):
            speedup = results[fastest_config] / duration if duration > 0 else 0
            print(f"   {config_name}: {duration:.2f}秒 (相对最快: {speedup:.2f}x)")


if __name__ == "__main__":
    print("🚀 烟叶残缺类型分类 - 多线程优化版本")
    print("=" * 50)

    # 检查是否运行基准测试
    if len(sys.argv) > 1 and sys.argv[1] == "--benchmark":
        benchmark_performance()
    else:
        success = main()
        if success:
            print("🎉 程序执行成功！")
        else:
            print("💥 程序执行失败！")
            sys.exit(1)
