#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶残缺类型分类测试程序
测试deploy_canque_type_category.py的功能
"""

import os
import sys
import json
import time
import traceback


# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    import onnxruntime as ort
    print("✅ 成功导入所有必要模块")
except ImportError as e:
    print(f"❌ 错误: 无法导入必要模块 {e}")
    sys.exit(1)


class CanqueTypeCategoryTestRunner:
    """烟叶残缺类型分类测试运行器"""
    
    def __init__(self):
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.setup_paths()
        self.test_results = {
            'total_files': 0,
            'processed_files': 0,
            'success_files': 0,
            'failed_files': 0,
            'errors': []
        }
        
        # 定义需要验证的残缺类型分类特征列表
        self.expected_features = [
            # 残缺类型分类特征
            "ziran_sunshang",  # 自然损伤数量
            "jixie_sunshang"   # 机械损伤数量
        ]
    
    def setup_paths(self):
        """设置测试路径"""
        self.input_json_dir = os.path.join(self.current_dir, '..', 'color_variety', 'test_output')
        self.input_image_dir = os.path.join(self.current_dir, '..', 'yanye_user_feature', 'vis')
        self.output_dir = os.path.join(self.current_dir, 'test_output')
        
        # 路径标准化
        self.input_json_dir = os.path.abspath(self.input_json_dir)
        self.input_image_dir = os.path.abspath(self.input_image_dir)
        self.output_dir = os.path.abspath(self.output_dir)
    
    def validate_environment(self):
        """验证测试环境"""
        print("🔍 验证测试环境...")
        
        # 检查输入路径
        if not os.path.exists(self.input_json_dir):
            print(f"❌ 输入JSON目录不存在: {self.input_json_dir}")
            return False
        
        if not os.path.exists(self.input_image_dir):
            print(f"❌ 输入图像目录不存在: {self.input_image_dir}")
            return False
        
        # 统计输入文件数量
        json_files = [f for f in os.listdir(self.input_json_dir) if f.endswith('.json')]
        image_files = [f for f in os.listdir(self.input_image_dir) if f.endswith('.bmp')]
        
        print(f"✅ 输入JSON文件数量: {len(json_files)}")
        print(f"✅ 输入图像文件数量: {len(image_files)}")
        
        self.test_results['total_files'] = len(json_files)
        
        if len(json_files) == 0:
            print("❌ 没有找到输入JSON文件")
            return False
        
        if len(image_files) == 0:
            print("❌ 没有找到输入图像文件")
            return False
        
        return True
    
    def run_deployment(self):
        """运行部署程序"""
        print("🚀 运行烟叶残缺类型分类部署程序...")
        
        try:
            start_time = time.time()
            success = deploy_main()
            end_time = time.time()
            
            if success:
                print(f"✅ 部署程序执行成功，耗时: {end_time - start_time:.2f}秒")
                return True
            else:
                print("❌ 部署程序执行失败")
                return False
                
        except Exception as e:
            print(f"❌ 部署程序执行异常: {e}")
            traceback.print_exc()
            return False
    
    def validate_output(self):
        """验证输出结果"""
        print("🔍 验证输出结果...")
        
        if not os.path.exists(self.output_dir):
            print(f"❌ 输出目录不存在: {self.output_dir}")
            return False
        
        # 检查输出文件
        input_json_files = [f for f in os.listdir(self.input_json_dir) if f.endswith('.json')]
        output_json_files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]
        
        print(f"📊 输入文件数量: {len(input_json_files)}")
        print(f"📊 输出文件数量: {len(output_json_files)}")
        
        for json_file in input_json_files:
            output_json_path = os.path.join(self.output_dir, json_file)
            
            try:
                if os.path.exists(output_json_path):
                    with open(output_json_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    
                    # 检查是否包含残缺类型分类特征
                    has_canque_features = False
                    feature_count = 0
                    has_sorted_features = False
                    
                    if 'user_feature' in json_data:
                        user_feature = json_data['user_feature']
                        
                        # 检查预期的特征
                        for feature_name in self.expected_features:
                            if feature_name in user_feature:
                                feature_count += 1
                        
                        # 如果有残缺分类特征，认为处理成功
                        if feature_count >= len(self.expected_features):
                            has_canque_features = True
                        
                        # 检查特征是否已排序（通过检查特征顺序）
                        feature_keys = list(user_feature.keys())
                        if len(feature_keys) > 1:
                            # 简单检查：如果有多个特征且包含残缺分类特征，认为已排序
                            has_sorted_features = True
                    
                    self.test_results['processed_files'] += 1
                    
                    if has_canque_features:
                        self.test_results['success_files'] += 1
                        sort_status = "✓" if has_sorted_features else "?"
                        print(f"✅ {json_file}: 残缺类型分类 ✓ 特征排序 {sort_status} ({feature_count}/{len(self.expected_features)})")
                    else:
                        self.test_results['failed_files'] += 1
                        error_msg = f"{json_file}: 缺少残缺类型分类特征 (仅有{feature_count}/{len(self.expected_features)})"
                        self.test_results['errors'].append(error_msg)
                        print(f"❌ {error_msg}")
                else:
                    self.test_results['failed_files'] += 1
                    error_msg = f"{json_file}: 输出文件不存在"
                    self.test_results['errors'].append(error_msg)
                    print(f"❌ {error_msg}")
                
            except Exception as e:
                self.test_results['failed_files'] += 1
                error_msg = f"{json_file}: 处理异常 - {e}"
                self.test_results['errors'].append(error_msg)
                print(f"❌ {error_msg}")
        
        return self.test_results['success_files'] > 0
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 烟叶残缺类型分类测试报告")
        print("="*60)
        
        print(f"总文件数量: {self.test_results['total_files']}")
        print(f"处理文件数量: {self.test_results['processed_files']}")
        print(f"成功文件数量: {self.test_results['success_files']}")
        print(f"失败文件数量: {self.test_results['failed_files']}")
        
        if self.test_results['total_files'] > 0:
            success_rate = (self.test_results['success_files'] / self.test_results['total_files']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        if self.test_results['errors']:
            print("\n❌ 错误详情:")
            for error in self.test_results['errors'][:5]:  # 只显示前5个错误
                print(f"  - {error}")
            if len(self.test_results['errors']) > 5:
                print(f"  ... 还有 {len(self.test_results['errors']) - 5} 个错误")
        
        print("="*60)
        
        # 判断测试是否通过
        if self.test_results['success_files'] == self.test_results['total_files']:
            print("🎉 所有测试通过！")
            return True
        elif self.test_results['success_files'] > 0:
            print("⚠️  部分测试通过")
            return True
        else:
            print("💥 所有测试失败！")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始烟叶残缺类型分类测试")
        print("="*60)
        
        # 1. 验证环境
        if not self.validate_environment():
            return False
        
        # 2. 运行部署程序
        if not self.run_deployment():
            return False
        
        # 3. 验证输出
        if not self.validate_output():
            return False
        
        # 4. 生成报告
        return self.generate_report()


def main():
    """主函数"""
    test_runner = CanqueTypeCategoryTestRunner()
    success = test_runner.run_all_tests()
    
    if success:
        print("\n✅ 烟叶残缺类型分类测试程序执行成功！")
        return True
    else:
        print("\n❌ 烟叶残缺类型分类测试程序执行失败！")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
