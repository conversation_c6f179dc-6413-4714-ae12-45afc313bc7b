#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶残缺类型分类部署程序
从start_predict.py中独立出来的run_canque_type_category_tmp模块
保持原有推理逻辑不变，直接import调用base_function_for_test3中的处理方法
"""

import os
import sys
import cv2
import numpy as np
from tqdm import tqdm
import traceback
import time
import logging
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    import base_function as bf
    print("✅ 成功导入base_function")
except ImportError as e:
    print(f"❌ 错误: 无法导入base_function {e}")
    sys.exit(1)
    
lpw = bf.linux_path_to_win()

def canque_filter(canques):
    board = np.zeros((512,1152), np.uint16)
    result_ind = []
    for n, canque in enumerate(canques):
        temp = np.zeros((512,1152), np.uint16)
        cv2.fillPoly(temp, [canque], 255)
        # print(n)
        # print(temp.max())
        # print(board.max())
        # print((board+temp).max())
        # print("******")

        if (board+temp).max() > 255:
            cur_dup_ind = n
            pre_dup_ind = (board+temp).max()-256 # value = n + 1
            if cv2.contourArea(np.reshape(canques[cur_dup_ind], (-1, 1, 2))) > cv2.contourArea(np.reshape(canques[pre_dup_ind], (-1, 1, 2))):
                result_ind.append(cur_dup_ind)
                result_ind.remove(pre_dup_ind)
                board[board==pre_dup_ind+1] = 0
                cv2.fillPoly(board, [canques[cur_dup_ind]], n + 1)
        else:
            cv2.fillPoly(board, [canques[n]], n + 1)
            result_ind.append(n)
        #
        # plt.imshow(board)
        # plt.show()
    canques_point = []
    for ind in result_ind:
        canques_point.append(canques[ind])
    return canques_point

def json_reader(json_path):
    json_content = bf.load_json_dict_orig(json_path)
    shapes = json_content['shapes']
    canques_temp = [shape['points'] for shape in shapes if shape["label"] == 'canque']
    canques = []
    for canque in canques_temp:
        temp = np.array([[int(x*1152), int(y*512)] for x, y in canque])
        # canques.append(np.reshape(np.array(temp), (-1, 1, 2)))
        canques.append(temp)

    canques = canque_filter(canques)
    return canques

def do_dir(png_root, json_root, thres=200):
    """
    批量处理目录中的图像文件，进行残缺类型分类

    Args:
        png_root: 图像文件根目录
        json_root: JSON文件根目录
        thres: 面积阈值，默认200像素
    """
    img_path, json_path = lpw.trans(png_root), lpw.trans(json_root)
    json_files, _ = bf.scan_files_2(json_path, postfix='json', except_postfix='bak')
    img_files, _ = bf.scan_files_2(img_path, postfix='png', except_midfix='mid')
    # json_files = sorted(json_files, key=lambda x: int(os.path.basename(x).split('.')[0]))
    # img_files = sorted(img_files, key=lambda x: int(os.path.basename(x).split('.')[0]))
    static = {}
    for img_file, json_file in tqdm(zip(img_files, json_files)):
        # print(img_file)
        natural_damage = []
        mechanical_damage = []
        img = cv2.imread(img_file)
        canques = json_reader(json_file)
        for canque in canques:
            # canque_point = canque['points']
            # canque_point = [[int(x*1152), int(y*512)] for x, y in canque_point]
            canque = np.reshape(np.array(canque), (-1, 1, 2))
            if cv2.contourArea(canque) < thres:
                natural_damage.append(canque)
                img = cv2.drawContours(img, [canque], -1, (0, 255, 0), 5)
            else:
                mechanical_damage.append(canque)
                img = cv2.drawContours(img, [canque], -1, (0, 0, 255), 5)

        num_zirasunshang = len(natural_damage)
        num_jixiesunshang = len(mechanical_damage)
        static[f"{os.path.basename(img_file)}"] = [num_zirasunshang, num_jixiesunshang]
        # print(f"natural:{num_zirasunshang}\nmechanical:{num_jixiesunshang}")
        # cv2.imshow('img', img)
        # cv2.waitKey(0)
        bf.labelme_json_add_userfeature_file(json_file, {"ziran_sunshang":num_zirasunshang, "jixie_sunshang":num_jixiesunshang})


def run_canque_type_category_main(input_image_dir, input_json_dir, output_json_dir=None, thres=200):
    """
    烟叶残缺类型分类主函数

    Args:
        input_image_dir: 输入图像目录路径
        input_json_dir: 输入JSON文件目录路径
        output_json_dir: 输出JSON文件目录路径，如果为None则输出到input_json_dir
        thres: 面积阈值，默认200像素

    Returns:
        bool: 处理是否成功
    """
    try:
        print("🚀 开始烟叶残缺类型分类处理...")
        print(f"📁 输入图像目录: {input_image_dir}")
        print(f"📁 输入JSON目录: {input_json_dir}")
        print(f"📁 输出JSON目录: {output_json_dir or input_json_dir}")
        print(f"🔢 面积阈值: {thres}像素")

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入图像目录不存在: {input_image_dir}")
            return False

        if not os.path.exists(input_json_dir):
            print(f"❌ 输入JSON目录不存在: {input_json_dir}")
            return False

        # 创建输出目录
        if output_json_dir and not os.path.exists(output_json_dir):
            os.makedirs(output_json_dir, exist_ok=True)
            print(f"✅ 创建输出目录: {output_json_dir}")

        # 获取图像文件列表 - 支持.bmp格式
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        print(f"📊 找到 {len(image_files)} 个图像文件")

        # 处理每个图像文件
        success_count = 0
        error_count = 0

        for image_file in tqdm(image_files, desc="处理残缺类型分类"):
            try:
                image_path = os.path.join(input_image_dir, image_file)
                print(f"\n🔄 处理文件: {image_file}")

                # 检查对应的JSON文件是否存在
                json_file = os.path.splitext(image_file)[0] + '.json'
                input_json_path = os.path.join(input_json_dir, json_file)

                if not os.path.exists(input_json_path):
                    print(f"⚠️  跳过: 未找到对应的JSON文件 {json_file}")
                    continue

                # 如果有输出目录，先复制JSON文件到输出目录
                if output_json_dir:
                    output_json_path = os.path.join(output_json_dir, json_file)
                    if not os.path.exists(output_json_path):
                        import shutil
                        shutil.copy2(input_json_path, output_json_path)
                        print(f"📋 复制JSON文件: {json_file}")
                    json_path_to_use = output_json_path
                else:
                    json_path_to_use = input_json_path

                # 处理单个文件
                natural_damage = []
                mechanical_damage = []
                img = cv2.imread(image_path)
                canques = json_reader(json_path_to_use)

                for canque in canques:
                    canque = np.reshape(np.array(canque), (-1, 1, 2))
                    if cv2.contourArea(canque) < thres:
                        natural_damage.append(canque)
                        img = cv2.drawContours(img, [canque], -1, (0, 255, 0), 5)
                    else:
                        mechanical_damage.append(canque)
                        img = cv2.drawContours(img, [canque], -1, (0, 0, 255), 5)

                num_zirasunshang = len(natural_damage)
                num_jixiesunshang = len(mechanical_damage)

                # 更新JSON文件
                bf.labelme_json_add_userfeature_file(json_path_to_use, {
                    "ziran_sunshang": num_zirasunshang,
                    "jixie_sunshang": num_jixiesunshang
                })

                success_count += 1
                print(f"✅ 处理成功: {image_file} (自然损伤:{num_zirasunshang}, 机械损伤:{num_jixiesunshang})")

            except Exception as e:
                error_count += 1
                print(f"❌ 处理失败: {image_file} - {e}")
                traceback.print_exc()

        print(f"\n📊 残缺类型分类处理完成:")
        print(f"✅ 成功: {success_count} 个文件")
        print(f"❌ 失败: {error_count} 个文件")

        return success_count > 0

    except Exception as e:
        print(f"❌ 烟叶残缺类型分类处理异常: {e}")
        traceback.print_exc()
        return False


def run_user_feature_sort_main(json_dir_path):
    """
    用户特征排序主函数

    Args:
        json_dir_path: JSON文件目录路径

    Returns:
        bool: 处理是否成功
    """
    try:
        print("🔄 开始用户特征排序处理...")
        print(f"📁 JSON目录: {json_dir_path}")

        # 验证目录存在
        if not os.path.exists(json_dir_path):
            print(f"❌ JSON目录不存在: {json_dir_path}")
            return False

        # 扫描JSON文件列表
        json_list, _ = bf.scan_labelme_jsonfile_list(json_dir_path)

        if not json_list:
            print(f"❌ 在目录中未找到JSON文件: {json_dir_path}")
            return False

        print(f"📊 找到 {len(json_list)} 个JSON文件")

        # 处理每个JSON文件
        success_count = 0
        error_count = 0

        for json_path in json_list:
            try:
                print(f"🔄 排序特征: {os.path.basename(json_path)}")
                bf.labelme_user_feature_sort(json_path)
                success_count += 1
                print(f"✅ 排序成功: {os.path.basename(json_path)}")

            except Exception as e:
                error_count += 1
                print(f"❌ 排序失败: {os.path.basename(json_path)} - {e}")
                traceback.print_exc()

        print(f"\n📊 用户特征排序处理完成:")
        print(f"✅ 成功: {success_count} 个文件")
        print(f"❌ 失败: {error_count} 个文件")

        return success_count > 0

    except Exception as e:
        print(f"❌ 用户特征排序处理异常: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_json_dir = os.path.join(current_dir, '..', 'color_variety', 'test_output')
        input_image_dir = os.path.join(current_dir, '..', 'yanye_user_feature', 'vis')
        output_json_dir = os.path.join(current_dir, 'test_output')

        # 路径标准化
        input_json_dir = os.path.abspath(input_json_dir)
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        print("🧪 烟叶残缺类型分类部署程序")
        print("="*60)

        # 第一步：运行残缺类型分类
        print("📋 步骤1: 烟叶残缺类型分类")
        success1 = run_canque_type_category_main(
            input_image_dir=input_image_dir,
            input_json_dir=input_json_dir,
            output_json_dir=output_json_dir,
            thres=200  # 保持默认阈值200像素
        )

        if not success1:
            print("\n💥 烟叶残缺类型分类处理失败！")
            return False

        # 第二步：运行用户特征排序
        print("\n📋 步骤2: 用户特征排序")
        success2 = run_user_feature_sort_main(
            json_dir_path=output_json_dir
        )

        if success1 and success2:
            print("\n🎉 烟叶残缺类型分类和用户特征排序处理完成！")
            return True
        else:
            print("\n💥 部分处理失败！")
            return False

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
