#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合检测GPU流水线并行处理程序
集成12个检测模块，实现高效的GPU流水线并行处理

作者: 系统架构师
日期: 2025-01-27
版本: 1.0.0

检测模块：
1. 主脉打开检测
2. 主脉走势检测  
3. 折痕检测
4. 支脉检测
5. 支脉青检测
6. 轮廓残缺补全检测
7. 焦点浮青等检测
8. 横纹检测
9. 烤红检测
10. 挂灰检测
11. 皱缩检测
12. 潮红检测
"""

import os
import sys
import time
import threading
import json
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    import base_function as bf
    print("🔥🔥🔥 综合检测GPU流水线并行版本 - 成功导入基础模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入基础模块 {e}")
    sys.exit(1)

# 导入各个检测模块的核心处理器
try:
    # 主脉打开检测
    from seg_det_zhumaidakai.deploy_seg_det_zhumaidakai_onnx_gpu_final import (
        GPUZhumaidakaiModelManager, PipelineZhumaidakaiProcessor
    )
    
    # 主脉走势检测
    from seg_det_zhumaizoushi.deploy_seg_det_zhumaizoushi_onnx_gpu_final import (
        GPUZhumaizoushiModelManager, PipelineZhumaizoushiProcessor
    )
    
    # 折痕检测
    from seg_det_zheheng.deploy_seg_det_zheheng_onnx_gpu_final import (
        GPUZhehengModelManager, PipelineZhehengProcessor
    )
    
    # 支脉检测
    from seg_det_zhimai.deploy_seg_det_zhimai_onnx_gpu_final import (
        GPUZhimaiModelManager, PipelineZhimaiProcessor
    )
    
    # 支脉青检测
    from seg_det_zhimaiqing.deploy_seg_det_zhimaiqing_onnx_gpu_final import (
        GPUZhimaiqingModelManager, PipelineZhimaiqingProcessor
    )
    
    # 轮廓残缺补全检测
    from seg_det_lunkuo_canque_fill.deploy_seg_lunkuo_canque_fill_onnx_gpu_final import (
        DualGPUModelManager, DualGPUPipelineProcessor
    )
    
    # 焦点浮青等检测
    from seg_det_jiaodian.deploy_seg_det_condition_onnx_gpu_final import (
        ConditionPipelineProcessor, GPUJiaodianModelManager, GPUFuqingModelManager
    )
    
    # 横纹检测
    from seg_det_hengwen.deploy_seg_det_hengwen_onnx_gpu_final import (
        GPUHengwenModelManager, HengwenPipelineProcessor
    )
    
    # 烤红检测
    from seg_det_kaohong.deploy_seg_det_kaohong_onnx_gpu_final import (
        GPUKaohongModelManager, KaohongPipelineProcessor
    )
    
    # 挂灰检测
    from seg_det_kaohong.deploy_seg_det_guahui_onnx_gpu_final import (
        GPUGuahuiModelManager, GuahuiPipelineProcessor
    )
    
    # 皱缩检测
    from seg_det_zhousuo.deploy_seg_det_zhousuo_onnx_gpu_final import (
        GPUZhousuoModelManager, ZhousuoPipelineProcessor
    )
    
    # 潮红检测
    from seg_det_chaohong.deploy_seg_det_chaohong_onnx_gpu_final import (
        GPUChaohongModelManager, ChaohongPipelineProcessor
    )
    
    print("✅ 成功导入所有检测模块的核心处理器")
    
except ImportError as e:
    print(f"❌ 错误: 无法导入检测模块 {e}")
    sys.exit(1)


@dataclass
class ModuleConfig:
    """检测模块配置"""
    name: str
    model_path: str
    gpu_id: int
    processor_class: Any
    manager_class: Any
    enabled: bool = True


class GPUResourceManager:
    """GPU资源管理器 - 合理分配GPU资源给各个模块"""
    
    def __init__(self):
        self.gpu0_modules = []  # GPU 0上的模块
        self.gpu1_modules = []  # GPU 1上的模块
        self.module_configs = self._initialize_module_configs()
        
        print("🔧 初始化GPU资源管理器")
        self._allocate_gpu_resources()
    
    def _initialize_module_configs(self) -> Dict[str, ModuleConfig]:
        """初始化模块配置"""
        configs = {
            'zhumaidakai': ModuleConfig(
                name='主脉打开检测',
                model_path='seg_det_zhumaidakai/zhumaidakai.onnx',
                gpu_id=0,
                processor_class=PipelineZhumaidakaiProcessor,
                manager_class=GPUZhumaidakaiModelManager
            ),
            'zheheng': ModuleConfig(
                name='折痕检测',
                model_path='seg_det_zheheng/zheheng.onnx',
                gpu_id=0,
                processor_class=PipelineZhehengProcessor,
                manager_class=GPUZhehengModelManager
            ),
            'zhimaiqing': ModuleConfig(
                name='支脉青检测',
                model_path='seg_det_zhimaiqing/zhimaiqing.onnx',
                gpu_id=0,
                processor_class=PipelineZhimaiqingProcessor,
                manager_class=GPUZhimaiqingModelManager
            ),
            'jiaodian': ModuleConfig(
                name='焦点浮青等检测',
                model_path='seg_det_jiaodian',  # 特殊处理，有两个模型
                gpu_id=0,
                processor_class=ConditionPipelineProcessor,
                manager_class=None  # 特殊处理
            ),
            'kaohong': ModuleConfig(
                name='烤红检测',
                model_path='seg_det_kaohong/kaohong.onnx',
                gpu_id=0,
                processor_class=KaohongPipelineProcessor,
                manager_class=GPUKaohongModelManager
            ),
            'zhousuo': ModuleConfig(
                name='皱缩检测',
                model_path='seg_det_zhousuo/zhousuo.onnx',
                gpu_id=0,
                processor_class=ZhousuoPipelineProcessor,
                manager_class=GPUZhousuoModelManager
            ),
            'zhumaizoushi': ModuleConfig(
                name='主脉走势检测',
                model_path='seg_det_zhumaizoushi/zhumaizoushi.onnx',
                gpu_id=1,
                processor_class=PipelineZhumaizoushiProcessor,
                manager_class=GPUZhumaizoushiModelManager
            ),
            'zhimai': ModuleConfig(
                name='支脉检测',
                model_path='seg_det_zhimai/zhimai.onnx',
                gpu_id=1,
                processor_class=PipelineZhimaiProcessor,
                manager_class=GPUZhimaiModelManager
            ),
            'lunkuo_canque_fill': ModuleConfig(
                name='轮廓残缺补全检测',
                model_path='seg_det_lunkuo_canque_fill',  # 特殊处理，有两个模型
                gpu_id=1,
                processor_class=DualGPUPipelineProcessor,
                manager_class=DualGPUModelManager
            ),
            'hengwen': ModuleConfig(
                name='横纹检测',
                model_path='seg_det_hengwen/hengwen.onnx',
                gpu_id=1,
                processor_class=HengwenPipelineProcessor,
                manager_class=GPUHengwenModelManager
            ),
            'guahui': ModuleConfig(
                name='挂灰检测',
                model_path='seg_det_kaohong/guahui.onnx',
                gpu_id=1,
                processor_class=GuahuiPipelineProcessor,
                manager_class=GPUGuahuiModelManager
            ),
            'chaohong': ModuleConfig(
                name='潮红检测',
                model_path='seg_det_chaohong/chaohong.onnx',
                gpu_id=1,
                processor_class=ChaohongPipelineProcessor,
                manager_class=GPUChaohongModelManager
            )
        }
        
        return configs
    
    def _allocate_gpu_resources(self):
        """分配GPU资源"""
        for module_id, config in self.module_configs.items():
            if config.gpu_id == 0:
                self.gpu0_modules.append(module_id)
            else:
                self.gpu1_modules.append(module_id)
        
        print(f"🔧 GPU 0分配模块: {[self.module_configs[m].name for m in self.gpu0_modules]}")
        print(f"🔧 GPU 1分配模块: {[self.module_configs[m].name for m in self.gpu1_modules]}")
    
    def get_module_config(self, module_id: str) -> Optional[ModuleConfig]:
        """获取模块配置"""
        return self.module_configs.get(module_id)
    
    def get_gpu_modules(self, gpu_id: int) -> List[str]:
        """获取指定GPU上的模块列表"""
        if gpu_id == 0:
            return self.gpu0_modules
        elif gpu_id == 1:
            return self.gpu1_modules
        else:
            return []


class ComprehensiveDetectionProcessor:
    """综合检测处理器 - 统一管理所有检测模块"""

    def __init__(self, test_mode: bool = True):
        self.test_mode = test_mode
        self.gpu_manager = GPUResourceManager()
        self.modules = {}  # 存储所有检测模块实例
        self.module_managers = {}  # 存储所有模型管理器
        self.processing_stats = {}  # 处理统计信息

        print("🚀 初始化综合检测处理器")

        # 初始化所有模块
        self._initialize_all_modules()

    def _initialize_all_modules(self):
        """初始化所有检测模块"""
        print("🔧 开始初始化所有检测模块...")

        for module_id, config in self.gpu_manager.module_configs.items():
            if not config.enabled:
                continue

            try:
                print(f"🔧 初始化 {config.name}...")

                # 特殊处理某些模块
                if module_id == 'jiaodian':
                    self._initialize_jiaodian_module(module_id, config)
                elif module_id == 'lunkuo_canque_fill':
                    self._initialize_lunkuo_canque_fill_module(module_id, config)
                else:
                    self._initialize_standard_module(module_id, config)

                print(f"✅ {config.name} 初始化成功")

            except Exception as e:
                print(f"❌ {config.name} 初始化失败: {e}")
                config.enabled = False

    def _initialize_standard_module(self, module_id: str, config: ModuleConfig):
        """初始化标准模块"""
        # 创建模型管理器
        model_path = os.path.join(current_dir, config.model_path)
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        manager = config.manager_class(model_path, thread_id=threading.current_thread().ident)
        self.module_managers[module_id] = manager

        # 创建流水线处理器
        if module_id in ['hengwen', 'zhousuo']:
            # 横纹检测和皱缩检测使用特殊参数
            processor = config.processor_class(
                manager,
                split_workers=1,
                postprocess_workers=1
            )
        else:
            # 其他模块使用标准参数
            processor = config.processor_class(
                manager,
                preprocess_workers=1,
                postprocess_workers=1
            )

        self.modules[module_id] = processor

    def _initialize_jiaodian_module(self, module_id: str, config: ModuleConfig):
        """初始化焦点浮青等检测模块"""
        # 焦点浮青等检测有两个模型
        jiaodian_model_path = os.path.join(current_dir, 'seg_det_jiaodian/jiaodian.onnx')
        fuqing_model_path = os.path.join(current_dir, 'seg_det_jiaodian/fuqing.onnx')

        if not os.path.exists(jiaodian_model_path):
            raise FileNotFoundError(f"焦点模型文件不存在: {jiaodian_model_path}")
        if not os.path.exists(fuqing_model_path):
            raise FileNotFoundError(f"浮青模型文件不存在: {fuqing_model_path}")

        # 创建两个模型管理器
        jiaodian_manager = GPUJiaodianModelManager(jiaodian_model_path, thread_id=threading.current_thread().ident)
        fuqing_manager = GPUFuqingModelManager(fuqing_model_path, thread_id=threading.current_thread().ident)

        self.module_managers[f'{module_id}_jiaodian'] = jiaodian_manager
        self.module_managers[f'{module_id}_fuqing'] = fuqing_manager

        # 创建条件检测处理器
        processor = config.processor_class(
            jiaodian_manager,
            fuqing_manager,
            preprocess_workers=1,
            postprocess_workers=1
        )

        self.modules[module_id] = processor

    def _initialize_lunkuo_canque_fill_module(self, module_id: str, config: ModuleConfig):
        """初始化轮廓残缺补全检测模块"""
        # 轮廓残缺补全检测有两个模型
        canque_model_path = os.path.join(current_dir, 'seg_det_lunkuo_canque_fill/canque.onnx')
        lunkuo_model_path = os.path.join(current_dir, 'seg_det_lunkuo_canque_fill/lunkuo.onnx')

        if not os.path.exists(canque_model_path):
            raise FileNotFoundError(f"残缺模型文件不存在: {canque_model_path}")
        if not os.path.exists(lunkuo_model_path):
            raise FileNotFoundError(f"轮廓模型文件不存在: {lunkuo_model_path}")

        # 创建双GPU模型管理器
        manager = config.manager_class(
            canque_model_path=canque_model_path,
            lunkuo_model_path=lunkuo_model_path,
            thread_id=threading.current_thread().ident
        )

        self.module_managers[module_id] = manager

        # 创建双GPU流水线处理器
        processor = config.processor_class(
            manager,
            preprocess_workers=1,
            postprocess_workers=1
        )

        self.modules[module_id] = processor

    def process_image_comprehensive(self, image_path: str, output_dir: str) -> Dict[str, Any]:
        """对单张图像进行综合检测"""
        print(f"\n🔍 开始综合检测: {os.path.basename(image_path)}")

        # 创建临时输出目录
        temp_output_dirs = {}
        for module_id in self.modules.keys():
            temp_dir = os.path.join(output_dir, f"temp_{module_id}")
            os.makedirs(temp_dir, exist_ok=True)
            temp_output_dirs[module_id] = temp_dir

        # 存储所有模块的检测结果
        all_results = {}
        processing_times = {}

        # 按GPU分组处理模块
        gpu0_modules = [m for m in self.modules.keys() if self.gpu_manager.get_module_config(m).gpu_id == 0]
        gpu1_modules = [m for m in self.modules.keys() if self.gpu_manager.get_module_config(m).gpu_id == 1]

        # 顺序处理避免GPU冲突
        for module_group, group_name in [(gpu0_modules, "GPU0"), (gpu1_modules, "GPU1")]:
            print(f"🔧 处理 {group_name} 模块组...")

            for module_id in module_group:
                if module_id not in self.modules:
                    continue

                config = self.gpu_manager.get_module_config(module_id)
                processor = self.modules[module_id]
                temp_dir = temp_output_dirs[module_id]

                try:
                    start_time = time.time()
                    print(f"  🔍 执行 {config.name}...")

                    # 执行检测
                    if module_id == 'lunkuo_canque_fill':
                        # 轮廓残缺补全检测特殊处理
                        result = processor.process_images_pipeline([image_path], temp_dir, temp_dir)
                    else:
                        # 其他模块标准处理
                        result = processor.process_images_pipeline([image_path], temp_dir)

                    processing_time = time.time() - start_time
                    processing_times[module_id] = processing_time

                    # 读取生成的JSON结果
                    json_result = self._read_module_result(image_path, temp_dir)
                    all_results[module_id] = {
                        'config': config,
                        'result': result,
                        'json_data': json_result,
                        'processing_time': processing_time
                    }

                    print(f"  ✅ {config.name} 完成 - 耗时: {processing_time:.2f}秒")

                except Exception as e:
                    print(f"  ❌ {config.name} 处理失败: {e}")
                    all_results[module_id] = {
                        'config': config,
                        'result': 0,
                        'json_data': None,
                        'processing_time': 0,
                        'error': str(e)
                    }

        return all_results

    def _read_module_result(self, image_path: str, temp_dir: str) -> Optional[Dict]:
        """读取模块生成的JSON结果"""
        try:
            # 构建JSON文件路径
            image_name = os.path.basename(image_path)
            json_name = bf.get_file_name(bf.rename_add_post(image_name, post="json"))
            json_path = os.path.join(temp_dir, json_name)

            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"⚠️  JSON文件不存在: {json_path}")
                return None

        except Exception as e:
            print(f"❌ 读取JSON结果失败: {e}")
            return None

    def merge_detection_results(self, all_results: Dict[str, Any], image_path: str) -> Dict[str, Any]:
        """合并所有模块的检测结果"""
        merged_shapes = []
        total_detections = 0
        module_stats = {}

        for module_id, result_data in all_results.items():
            if 'error' in result_data:
                continue

            json_data = result_data.get('json_data')
            if not json_data or 'shapes' not in json_data:
                continue

            shapes = json_data['shapes']
            detection_count = len(shapes)
            total_detections += detection_count

            # 为每个shape添加模块来源标记
            for shape in shapes:
                shape['module'] = module_id
                shape['module_name'] = result_data['config'].name
                merged_shapes.append(shape)

            module_stats[module_id] = {
                'name': result_data['config'].name,
                'detection_count': detection_count,
                'processing_time': result_data['processing_time']
            }

        # 创建合并的JSON结构
        merged_json = {
            "version": "4.5.6",
            "flags": {},
            "shapes": merged_shapes,
            "imagePath": os.path.basename(image_path),
            "imageData": None,
            "imageHeight": 1152,
            "imageWidth": 512,
            "comprehensive_stats": {
                "total_detections": total_detections,
                "module_count": len([m for m in all_results.keys() if 'error' not in all_results[m]]),
                "module_stats": module_stats
            }
        }

        return merged_json

    def process_images_batch(self, image_paths: List[str], output_dir: str) -> Dict[str, Any]:
        """批量处理图像"""
        print(f"\n🚀 开始批量综合检测 - 共 {len(image_paths)} 张图像")

        total_start_time = time.time()
        batch_results = {}

        for i, image_path in enumerate(image_paths, 1):
            print(f"\n📊 处理进度: {i}/{len(image_paths)} - {os.path.basename(image_path)}")

            # 处理单张图像
            image_results = self.process_image_comprehensive(image_path, output_dir)

            # 合并检测结果
            merged_result = self.merge_detection_results(image_results, image_path)

            # 保存合并的JSON结果
            self._save_merged_result(image_path, merged_result, output_dir)

            batch_results[image_path] = {
                'individual_results': image_results,
                'merged_result': merged_result
            }

        total_time = time.time() - total_start_time

        # 输出批量处理统计
        self._print_batch_stats(batch_results, total_time)

        return batch_results

    def _save_merged_result(self, image_path: str, merged_result: Dict[str, Any], output_dir: str):
        """保存合并的检测结果"""
        try:
            # 构建输出文件路径
            image_name = os.path.basename(image_path)
            json_name = bf.get_file_name(bf.rename_add_post(image_name, post="json"))
            output_path = os.path.join(output_dir, json_name)

            # 保存JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(merged_result, f, ensure_ascii=False, indent=2)

            print(f"💾 保存合并结果: {output_path}")

        except Exception as e:
            print(f"❌ 保存合并结果失败: {e}")

    def _print_batch_stats(self, batch_results: Dict[str, Any], total_time: float):
        """打印批量处理统计信息"""
        total_images = len(batch_results)
        total_detections = 0
        module_totals = {}

        for _, result_data in batch_results.items():
            merged_result = result_data['merged_result']
            stats = merged_result.get('comprehensive_stats', {})

            total_detections += stats.get('total_detections', 0)

            for module_id, module_stat in stats.get('module_stats', {}).items():
                if module_id not in module_totals:
                    module_totals[module_id] = {
                        'name': module_stat['name'],
                        'total_detections': 0,
                        'total_time': 0
                    }
                module_totals[module_id]['total_detections'] += module_stat['detection_count']
                module_totals[module_id]['total_time'] += module_stat['processing_time']

        print(f"\n📊 综合检测批量处理统计:")
        print(f"   总图像数量: {total_images}")
        print(f"   总检测数量: {total_detections}")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均每张图像: {total_time/total_images:.2f}秒")
        print(f"   处理速度: {total_images/total_time:.2f} 张/秒")

        print(f"\n📊 各模块检测统计:")
        for module_id, stats in module_totals.items():
            avg_time = stats['total_time'] / total_images
            print(f"   {stats['name']}: {stats['total_detections']}个目标, 平均{avg_time:.2f}秒/张")


def main():
    """主函数"""
    try:
        print("🔥🔥🔥 烟叶综合检测GPU流水线并行处理程序 🔥🔥🔥")
        print("🔥🔥🔥 集成12个检测模块的综合检测系统 🔥🔥🔥")
        print("="*80)

        # 配置路径
        input_image_dir = os.path.join(current_dir, 'test_images')
        output_dir = os.path.join(current_dir, 'test_data', 'test_output')

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入目录不存在: {input_image_dir}")
            return False

        # 获取前5张测试图像
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        # 只取前5张图像进行测试
        test_image_files = sorted(image_files)[:5]
        print(f"📊 找到 {len(image_files)} 个图像文件，测试前 {len(test_image_files)} 张")

        # 初始化综合检测处理器
        print(f"\n🚀 初始化综合检测处理器...")
        processor = ComprehensiveDetectionProcessor(test_mode=True)

        # 构建图像路径列表
        image_paths = [os.path.join(input_image_dir, image_file) for image_file in test_image_files]

        print(f"\n🚀 开始综合检测处理...")

        # 批量处理图像
        total_start_time = time.time()
        _ = processor.process_images_batch(image_paths, output_dir)
        total_time = time.time() - total_start_time

        print(f"\n🎉 综合检测处理完成！")
        print(f"📊 总处理时间: {total_time:.2f}秒")
        print(f"📊 平均每张图像: {total_time/len(test_image_files):.2f}秒")
        print(f"📊 处理吞吐量: {len(test_image_files)/total_time:.2f}张/秒")
        print(f"🚀 综合检测架构 - 12个模块并行流水线处理")

        return True

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
