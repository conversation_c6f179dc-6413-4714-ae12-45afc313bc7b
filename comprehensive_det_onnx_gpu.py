#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶综合检测主程序
集成12个检测模块的GPU流水线并行处理系统

作者: 系统架构师
日期: 2025-01-27
版本: 1.0.0

检测模块：
1. 主脉打开检测    2. 主脉走势检测    3. 折痕检测
4. 支脉检测        5. 支脉青检测      6. 轮廓残缺补全检测
7. 焦点浮青等检测  8. 横纹检测        9. 烤红检测
10. 挂灰检测       11. 皱缩检测       12. 潮红检测
"""

import os
import sys
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from comprehensive_detection import ComprehensivePipelineProcessor
    print("🔥🔥🔥 这是GPU流水线并行版本 - 综合检测系统 🔥🔥🔥")
    print("🔥🔥🔥 执行main()函数 - 确认执行正确版本 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入综合检测模块 {e}")
    sys.exit(1)


def validate_environment():
    """验证运行环境"""
    print("🔍 验证运行环境...")
    
    # 检查CUDA是否可用
    try:
        import onnxruntime as ort
        if 'CUDAExecutionProvider' in ort.get_available_providers():
            print("✅ CUDA GPU加速可用")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
    except ImportError:
        print("❌ ONNXRuntime未安装")
        return False
    
    # 检查必要的Python包
    required_packages = ['cv2', 'numpy']
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 可用")
        except ImportError:
            print(f"❌ {package} 未安装")
            return False
    
    return True


def setup_directories(input_dir: str, output_dir: str):
    """设置输入输出目录"""
    print("📁 设置目录结构...")
    
    # 验证输入目录
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"✅ 输出目录已准备: {output_dir}")
    
    return True


def get_test_images(input_dir: str, max_images: int = 5) -> list:
    """获取测试图像列表"""
    print(f"🔍 搜索测试图像 (最多{max_images}张)...")
    
    # 支持的图像格式
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    
    image_files = []
    for ext in image_extensions:
        pattern = f"*{ext}"
        files = list(Path(input_dir).glob(pattern))
        files.extend(list(Path(input_dir).glob(pattern.upper())))
        image_files.extend(files)
    
    # 排序并限制数量
    image_files = sorted(image_files)[:max_images]
    
    if not image_files:
        print(f"❌ 在目录中未找到图像文件: {input_dir}")
        return []
    
    print(f"📊 找到 {len(image_files)} 个图像文件:")
    for i, img_file in enumerate(image_files, 1):
        print(f"   {i}. {img_file.name}")
    
    return [str(img_file) for img_file in image_files]


def save_performance_report(report: dict, output_dir: str):
    """保存性能报告"""
    report_path = os.path.join(output_dir, "performance_report.json")
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📊 性能报告已保存: {report_path}")
        
    except Exception as e:
        print(f"⚠️  保存性能报告失败: {e}")


def main():
    """主函数"""
    try:
        print("🧪 烟叶综合检测GPU流水线并行系统")
        print("="*80)
        print("🎯 集成12个检测模块的统一处理系统")
        print("🚀 GPU流水线并行架构 - 双GPU资源优化")
        print("="*80)
        
        # 验证环境
        if not validate_environment():
            print("❌ 环境验证失败")
            return False
        
        # 配置路径
        input_dir = "/home/<USER>/xm/code/coderafactor/test_images"
        output_dir = "/home/<USER>/xm/code/coderafactor/test_data/test_output"
        
        # 设置目录
        if not setup_directories(input_dir, output_dir):
            print("❌ 目录设置失败")
            return False
        
        # 获取测试图像
        image_paths = get_test_images(input_dir, max_images=5)
        if not image_paths:
            print("❌ 没有找到测试图像")
            return False
        
        print(f"\n🚀 开始综合检测处理...")
        print(f"📊 输入图像: {len(image_paths)}张")
        print(f"📊 输出目录: {output_dir}")
        
        # 初始化综合检测流水线处理器
        print(f"\n🔧 初始化综合检测系统...")
        processor = ComprehensivePipelineProcessor(
            preprocess_workers=2,   # 2个预处理线程
            postprocess_workers=2,  # 2个后处理线程
            queue_size=32          # 队列大小
        )
        
        try:
            # 初始化所有检测模块
            processor.initialize_all_modules()
            
            # 开始流水线处理
            print(f"\n🚀 开始流水线并行处理...")
            total_start_time = time.time()
            
            performance_report = processor.process_images_pipeline(image_paths, output_dir)
            
            total_time = time.time() - total_start_time
            
            print(f"\n🎉 综合检测处理完成！")
            print(f"📊 总处理时间: {total_time:.2f}秒")
            print(f"📊 平均每张图像: {total_time/len(image_paths):.2f}秒")
            print(f"📊 处理吞吐量: {len(image_paths)/total_time:.2f}张/秒")
            
            if performance_report:
                total_detections = performance_report.get('total_detections', 0)
                print(f"📊 总检测数量: {total_detections}")
                
                # 保存性能报告
                save_performance_report(performance_report, output_dir)
            
            print(f"\n📁 输出文件:")
            print(f"   JSON文件: {output_dir}/*_comprehensive.json")
            print(f"   可视化图像: {output_dir}/*_comprehensive.png")
            print(f"   性能报告: {output_dir}/performance_report.json")
            
            print(f"\n🏆 综合检测系统运行成功！")
            print(f"🔥 12个检测模块并行处理完成")
            print(f"🚀 GPU流水线架构性能优异")
            
            return True
            
        finally:
            # 清理资源
            processor.cleanup()
    
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断处理")
        return False
        
    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🔥🔥🔥 启动烟叶综合检测GPU流水线并行系统 🔥🔥🔥")
    
    start_time = time.time()
    success = main()
    end_time = time.time()
    
    print(f"\n{'='*80}")
    if success:
        print(f"✅ 程序执行成功！总耗时: {end_time - start_time:.2f}秒")
        sys.exit(0)
    else:
        print(f"❌ 程序执行失败！总耗时: {end_time - start_time:.2f}秒")
        sys.exit(1)
