'''
                    
    # 烟叶残缺检测 （替换掉）
    run_seg_det_canque()
    # 烟叶主脉打开检测
    run_seg_det_zhumaidakai()
    # 烟叶主脉走势检测
    run_seg_det_zhumaizoushi()
    # 烟叶折痕检测 （重要）
    run_seg_det_zheheng()
    # 烟叶支脉检测 （重要）
    run_seg_det_zhimai()
    # 烟叶支脉青检测 （替换掉）
    run_seg_det_zhimaiqing()

    # 前面五个程序多线程并行检测处理

    # 烟叶轮廓残缺补全 （替换掉）
    run_seg_det_lunkuo_canque_fill()
    bf.sleep(5)
    # 这一部分代码需要优化


    # 烟叶焦点检测（成熟斑、浮青、焦点、气象斑、压油）
    run_test_3030()
    bf.sleep(5)
    # 烟叶横纹检测（不重要）
    run_test_100100()
    bf.sleep(5)
    # 烟叶烤红检测 （替换掉）
    run_test_250250()
    bf.sleep(5)
    # 烟叶潮红检测 （不重要）
    run_test_250250_chaohong()
    bf.sleep(5)
    # 烟叶皱缩检测 （重要）
    run_test_250250_zhousuo()
    bf.sleep(5)

    # 杂色检测模型多线程并行检测处理

    # 主脉打开处理
    run_seg_zhumaidakai_select()
    run_delete_out_labels_tmp()
    run_test_zhumai_zhimai_json_tmp()

    # 这里初步特征汇总

    # 支脉分离处理
    run_zhimai_split_tmp()
    run_delete_out_labels_tmp()
    run_zhimai_rgb_sub_tmp()
    
    # 圆度计算
    run_yuandu_halcon_tmp()
    # 锯齿平滑处理
    run_dealing_images_juchi_tmp()
    # 主要特征分析
    run_import_yanye_user_feature_tmp()
    # 主要特征颜色分析
    run_import_yanye_user_feature_rgb_hist()
    # 烟叶宽度计算
    run_user_feature_tobacoo_width_tmp()
    # 烟叶平滑度计算
    run_user_feature_smoothness()
    # 烟叶RGB直方图处理
    run_split_rgb_hist_tmp()
    # 烟叶相关性分析
    run_correlation_json_generate_tmp()
    # 烟叶特征汇总
    run_sum_yanye_user_feature_tmp()
    # 烟叶颜色多样性分析
    run_color_variety()
    # 烟叶残缺类型分类
    run_canque_type_category_tmp()
    # 烟叶特征排序
    run_user_feature_sort()
    
'''


要求如下：我想把 主脉打开(),主脉走势(),折痕检测(),支脉检测(),支脉青检测(),轮廓残缺补全(),焦点浮青等检测(),横纹检测(),烤红检测(),皱缩检测(),潮红检测()
主脉打开选择(),主脉特征计算(),支脉分离(),删除轮廓外标签(),支脉RGB特征计算(),锯齿平滑处理(),主要特征分析(),主要特征颜色分析(),烟叶宽度计算(),烟叶圆度计算(),烟叶平滑度计算(),烟叶RGB直方图处理(),相关性分析(),烟叶特征汇总(),烟叶颜色多样性分析(),烟叶残缺类型分类(),烟叶特征排序()