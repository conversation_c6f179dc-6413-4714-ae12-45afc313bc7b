#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟叶挂灰特征检测GPU流水线并行版本
基于浮青检测的成功流水线架构，专门适配挂灰检测

作者: 系统架构师
日期: 2025-01-27
版本: 2.0.0

关键特性：
1. GPU流水线并行架构
2. 高性能内存池管理
3. 异步队列系统
4. 与现有程序完全一致的检测结果
5. YOLO目标检测优化
"""

import os
import sys
import time
import threading
import cv2
import numpy as np
import json
import queue
from typing import List, Dict, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

# 添加根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 添加coderafactor目录到Python路径
coderafactor_root = "/home/<USER>/xm/code/coderafactor"
if coderafactor_root not in sys.path:
    sys.path.insert(0, coderafactor_root)

try:
    import base_function as bf
    import onnxruntime as ort
    print("🔥🔥🔥 GPU流水线并行版本 - 成功导入所有必要模块 🔥🔥🔥")
except ImportError as e:
    print(f"❌ 错误: 无法导入必要模块 {e}")
    sys.exit(1)

# 挂灰类别名称
CLASS_NAMES = {
    0: "guahui"
}


@dataclass
class PipelineData:
    """流水线数据结构"""
    image_path: str
    image_data: Optional[np.ndarray] = None
    preprocessed_data: Optional[np.ndarray] = None
    ratio: Optional[Tuple[float, float]] = None
    pad_w: float = 0.0
    pad_h: float = 0.0
    inference_result: Optional[List] = None
    processed_result: Optional[Dict] = None
    timestamp: float = 0.0
    stage: str = "init"  # init, preprocessed, inferred, postprocessed
    width: int = 0
    height: int = 0


class GPUMemoryPool:
    """GPU内存池管理器 - 减少动态内存分配开销"""
    
    def __init__(self, pool_size: int = 16, input_size: Tuple[int, int] = (640, 640)):
        self.pool_size = pool_size
        self.input_height, self.input_width = input_size
        self.buffers = queue.Queue()
        self.used_buffers = set()
        self._lock = threading.Lock()
        
        # 预分配GPU内存缓冲区
        print(f"🔧 初始化GPU内存池 - 池大小: {pool_size}, 输入尺寸: {input_size}")
        
        # 预分配内存缓冲区 (1, 3, 640, 640)
        for _ in range(pool_size):
            buffer = np.zeros((1, 3, self.input_height, self.input_width), dtype=np.float32)
            self.buffers.put(buffer)
    
    def get_buffer(self) -> Optional[np.ndarray]:
        """获取GPU内存缓冲区"""
        try:
            buffer = self.buffers.get_nowait()
            with self._lock:
                self.used_buffers.add(id(buffer))
            return buffer
        except queue.Empty:
            print("⚠️  GPU内存池已满，创建临时缓冲区")
            return np.zeros((1, 3, self.input_height, self.input_width), dtype=np.float32)
    
    def return_buffer(self, buffer: np.ndarray):
        """归还GPU内存缓冲区"""
        if buffer is not None:
            with self._lock:
                if id(buffer) in self.used_buffers:
                    self.used_buffers.remove(id(buffer))
                    try:
                        self.buffers.put_nowait(buffer)
                    except queue.Full:
                        print("⚠️  GPU内存池队列已满")
    
    def get_pool_stats(self) -> Dict[str, int]:
        """获取内存池统计信息"""
        with self._lock:
            return {
                'available': self.buffers.qsize(),
                'used': len(self.used_buffers),
                'total': self.pool_size
            }


class AsyncQueue:
    """异步队列 - 支持流水线各阶段的数据传递"""
    
    def __init__(self, maxsize: int = 16):
        self.queue = queue.Queue(maxsize=maxsize)
        self.maxsize = maxsize
        
    def put(self, item, timeout: float = 1.0) -> bool:
        """放入数据项"""
        try:
            self.queue.put(item, timeout=timeout)
            return True
        except queue.Full:
            return False
    
    def get(self, timeout: float = 1.0) -> Optional[Any]:
        """获取数据项"""
        try:
            return self.queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()


class GPUGuahuiModelManager:
    """GPU挂灰检测模型管理器 - 线程安全版本"""
    
    # 类级别的锁，确保线程安全的初始化
    _init_lock = threading.Lock()
    
    def __init__(self, model_path: str, thread_id: Optional[int] = None):
        """
        初始化GPU挂灰检测模型管理器
        
        Args:
            model_path: 模型文件路径
            thread_id: 线程ID，用于调试
        """
        self.thread_id = thread_id or threading.current_thread().ident
        self.model_path = model_path
        
        # 模型相关属性
        self.session = None
        self.ndtype = None
        self.model_height = None
        self.model_width = None
        self.classes = CLASS_NAMES
        
        # 初始化状态
        self._initialized = False
        
        print(f"🔘 [线程{self.thread_id}] 创建挂灰检测模型管理器")
        
        # 立即加载模型
        self._load_model()
    
    def _load_model(self) -> None:
        """
        加载GPU YOLO模型 - 线程安全
        """
        with self._init_lock:
            if self._initialized:
                return
                
            print(f"🔘 [线程{self.thread_id}] 开始加载挂灰检测模型...")
            
            # 验证模型文件存在
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"ONNX模型文件不存在: {self.model_path}")
            
            # 配置GPU执行提供程序
            providers = []
            if 'CUDAExecutionProvider' in ort.get_available_providers():
                providers.append('CUDAExecutionProvider')
                print(f"🔘 [线程{self.thread_id}] 使用CUDA GPU加速")
            else:
                print(f"🔘 [线程{self.thread_id}] CUDA不可用，回退到CPU")
            
            providers.append('CPUExecutionProvider')
            
            # 创建推理会话
            sess_options = ort.SessionOptions()
            sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
            sess_options.execution_mode = ort.ExecutionMode.ORT_PARALLEL
            
            # GPU推理配置：单线程模式
            sess_options.inter_op_num_threads = 1
            sess_options.intra_op_num_threads = 2
            
            self.session = ort.InferenceSession(
                self.model_path,
                sess_options=sess_options,
                providers=providers
            )
            
            # 根据ONNX模型类型选择数据类型
            self.ndtype = np.half if self.session.get_inputs()[0].type == "tensor(float16)" else np.single
            
            # 获取模型的输入宽度和高度
            self.model_height, self.model_width = [x.shape for x in self.session.get_inputs()][0][-2:]
            
            self._initialized = True
            print(f"✅ [线程{self.thread_id}] 挂灰检测模型加载成功: {self.model_path}")
            print(f"   执行提供程序: {self.session.get_providers()[0]}")
            print(f"   模型输入尺寸: {self.model_width}x{self.model_height}")
            print(f"   数据类型: {self.ndtype}")
    
    def preprocess(self, img: np.ndarray) -> Tuple[np.ndarray, Tuple[float, float], Tuple[float, float]]:
        """
        图像预处理 - 与原版本完全一致
        """
        # 调整输入图像大小并使用letterbox填充
        shape = img.shape[:2]  # 原始图像大小
        new_shape = (self.model_height, self.model_width)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        ratio = r, r
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        pad_w, pad_h = (new_shape[1] - new_unpad[0]) / 2, (new_shape[0] - new_unpad[1]) / 2  # 填充宽高
        
        if shape[::-1] != new_unpad:  # 调整图像大小
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        
        top, bottom = int(round(pad_h - 0.1)), int(round(pad_h + 0.1))
        left, right = int(round(pad_w - 0.1)), int(round(pad_w + 0.1))
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114))
        
        # 转换：HWC -> CHW -> BGR转RGB -> 除以255 -> contiguous -> 添加维度
        img = np.ascontiguousarray(np.einsum("HWC->CHW", img)[::-1], dtype=self.ndtype) / 255.0
        img_process = img[None] if len(img.shape) == 3 else img
        
        return img_process, ratio, (pad_w, pad_h)
    
    def predict(self, img_processed: np.ndarray) -> List:
        """GPU推理"""
        if not self._initialized:
            raise RuntimeError("YOLO模型未正确加载")
        
        # ONNX推理
        preds = self.session.run(None, {self.session.get_inputs()[0].name: img_processed})
        return preds
    
    def postprocess(self, preds: List, im0: np.ndarray, ratio: Tuple[float, float], 
                   pad_w: float, pad_h: float, conf_threshold: float = 0.4, 
                   iou_threshold: float = 0.45, nm: int = 32) -> np.ndarray:
        """
        推理后的结果后处理 - 与原版本完全一致
        """
        x = preds[0]  # 获取模型的预测输出
        
        # 转换维度
        x = np.einsum("bcn->bnc", x)
        
        # 置信度过滤
        x = x[np.amax(x[..., 4:-nm], axis=-1) > conf_threshold]
        
        # 合并边界框、置信度、类别和掩膜
        x = np.c_[x[..., :4], np.amax(x[..., 4:-nm], axis=-1), np.argmax(x[..., 4:-nm], axis=-1), x[..., -nm:]]
        
        # NMS过滤
        x = x[cv2.dnn.NMSBoxes(x[:, :4], x[:, 4], conf_threshold, iou_threshold)]
        
        # 解析并返回结果
        if len(x) > 0:
            # 边界框格式转换：从cxcywh -> xyxy
            x[..., [0, 1]] -= x[..., [2, 3]] / 2
            x[..., [2, 3]] += x[..., [0, 1]]
            
            # 缩放边界框，使其与原始图像尺寸匹配
            x[..., :4] -= [pad_w, pad_h, pad_w, pad_h]
            x[..., :4] /= min(ratio)
            
            # 限制边界框在图像边界内
            x[..., [0, 2]] = x[:, [0, 2]].clip(0, im0.shape[1])
            x[..., [1, 3]] = x[:, [1, 3]].clip(0, im0.shape[0])
            
            return x[..., :6]  # 返回边界框 [x1, y1, x2, y2, conf, cls]
        else:
            return np.array([]).reshape(0, 6)


class HighPerformanceGuahuiProcessor:
    """高性能挂灰后处理器 - 与现有版本完全一致"""

    def __init__(self):
        self.classes = CLASS_NAMES
        self.id_to_label = {v: k for k, v in enumerate(CLASS_NAMES.values())}

        # 图像缓存
        self.image_cache = {}

    def generate_shapes_optimized(self, boxes: np.ndarray, pic_path: str, width: int, height: int) -> list:
        """
        优化的shapes生成 - 与现有版本完全一致
        将YOLO检测框转换为labelme格式的相对坐标points
        """
        shapes = []

        if len(boxes) == 0:
            return shapes

        # 处理每个检测框
        for box in boxes:
            x1, y1, x2, y2, conf, cls = box

            # 确保坐标在图像范围内
            x1 = max(0, min(x1, width))
            y1 = max(0, min(y1, height))
            x2 = max(0, min(x2, width))
            y2 = max(0, min(y2, height))

            # 转换为相对坐标 - 与现有版本的格式完全一致
            shape = {
                "label": CLASS_NAMES[int(cls)],  # 直接查找类别名称
                "points": [
                    [x1 / width, y1 / height],  # 左上角相对坐标
                    [x2 / width, y2 / height]   # 右下角相对坐标
                ],
                "group_id": None,
                "shape_type": "rectangle",
                "flags": {},
                "other_data": {}
            }
            shapes.append(shape)

        return shapes

    def clear_cache(self):
        """清理缓存"""
        self.image_cache.clear()


class GuahuiPipelineProcessor:
    """挂灰检测流水线处理器 - 基于浮青检测的成功架构"""

    def __init__(self, model_manager: 'GPUGuahuiModelManager',
                 preprocess_workers: int = 1, postprocess_workers: int = 1,
                 queue_size: int = 16, conf_threshold: float = 0.4, iou_threshold: float = 0.45):
        self.model_manager = model_manager
        self.preprocess_workers = preprocess_workers
        self.postprocess_workers = postprocess_workers
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold

        # 初始化队列系统
        self.preprocess_queue = AsyncQueue(queue_size)
        self.inference_queue = AsyncQueue(queue_size)
        self.postprocess_queue = AsyncQueue(queue_size)
        self.result_queue = AsyncQueue(queue_size)

        # 初始化GPU内存池
        self.memory_pool = GPUMemoryPool(pool_size=queue_size, input_size=(640, 640))

        # 初始化组件
        self.postprocessor = self._get_global_postprocessor()

        # 控制标志
        self._stop_event = threading.Event()
        self._threads = []

        # 线程安全锁
        self._postprocess_lock = threading.Lock()

        # 统计信息
        self.stats = {
            'processed_count': 0,
            'preprocess_time': 0.0,
            'inference_time': 0.0,
            'postprocess_time': 0.0,
            'total_time': 0.0
        }

        print(f"🚀 初始化挂灰检测流水线处理器 - 预处理工作线程: {preprocess_workers}, 后处理工作线程: {postprocess_workers}")

    def _get_global_postprocessor(self):
        """获取全局后处理器实例 - 确保与现有版本一致"""
        # 使用类级别的单例模式
        if not hasattr(GuahuiPipelineProcessor, '_global_postprocessor'):
            GuahuiPipelineProcessor._global_postprocessor = HighPerformanceGuahuiProcessor()
        return GuahuiPipelineProcessor._global_postprocessor

    def _preprocess_worker(self, worker_id: int):
        """预处理工作线程"""
        print(f"🔘 启动预处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从预处理队列获取任务
            item = self.preprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 读取图像 - 与现有版本完全一致的方式
                image = cv2.imread(item.image_path)
                if image is None:
                    print(f"⚠️  [预处理{worker_id}] 无法读取图像: {item.image_path}")
                    continue

                # 获取图像尺寸
                height, width = image.shape[:2]
                item.width = width
                item.height = height
                item.image_data = image

                # 预处理
                img_processed, ratio, (pad_w, pad_h) = self.model_manager.preprocess(image)
                item.preprocessed_data = img_processed
                item.ratio = ratio
                item.pad_w = pad_w
                item.pad_h = pad_h
                item.stage = "preprocessed"

                # 放入推理队列
                if not self.inference_queue.put(item, timeout=1.0):
                    print(f"⚠️  [预处理{worker_id}] 推理队列已满，丢弃任务")
                    continue

                preprocess_time = time.time() - start_time
                self.stats['preprocess_time'] += preprocess_time

            except Exception as e:
                print(f"❌ [预处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🔘 预处理工作线程 {worker_id} 已停止")

    def _inference_worker(self):
        """GPU推理工作线程"""
        print(f"🔶 启动GPU推理工作线程")

        while not self._stop_event.is_set():
            # 从推理队列获取任务
            item = self.inference_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # GPU推理
                if item.preprocessed_data is not None:
                    preds = self.model_manager.predict(item.preprocessed_data)

                    # 后处理
                    boxes = self.model_manager.postprocess(
                        preds, item.image_data, item.ratio, item.pad_w, item.pad_h,
                        self.conf_threshold, self.iou_threshold
                    )

                    item.inference_result = boxes
                    item.stage = "inferred"

                    # 放入后处理队列
                    if not self.postprocess_queue.put(item, timeout=1.0):
                        print(f"⚠️  [推理] 后处理队列已满，丢弃任务")
                        continue

                inference_time = time.time() - start_time
                self.stats['inference_time'] += inference_time

            except Exception as e:
                print(f"❌ [推理] 处理异常: {e}")
                continue

        print(f"🔶 GPU推理工作线程已停止")

    def _postprocess_worker(self, worker_id: int, json_dir: str):
        """后处理工作线程"""
        print(f"🟢 启动后处理工作线程 {worker_id}")

        while not self._stop_event.is_set():
            # 从后处理队列获取任务
            item = self.postprocess_queue.get(timeout=0.1)
            if item is None:
                continue

            try:
                start_time = time.time()

                # 使用线程锁确保后处理的线程安全
                with self._postprocess_lock:
                    # 生成shapes - 与现有版本完全一致
                    shapes = self.postprocessor.generate_shapes_optimized(
                        item.inference_result, item.image_path, item.width, item.height
                    )

                # 保存JSON - 与现有版本完全一致
                if shapes:
                    json_name = bf.get_file_name(bf.rename_add_post(item.image_path, post="json"))
                    json_path = os.path.join(json_dir, json_name)

                    # 创建基础JSON结构
                    json_data = {
                        "version": "4.5.6",
                        "flags": {},
                        "shapes": shapes,
                        "imagePath": bf.get_file_name(item.image_path),
                        "imageData": None,
                        "imageHeight": 1152,
                        "imageWidth": 512
                    }

                    # 使用标准JSON保存
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, ensure_ascii=False, indent=2)

                    print(f"save json_filepath {json_path}")

                print(f"✅ [后处理{worker_id}] 处理完成: {bf.get_file_name(item.image_path)} - 检测到 {len(shapes)} 个挂灰目标")

                # 保存处理结果
                item.processed_result = {
                    'shapes': shapes,
                    'detection_count': len(shapes)
                }
                item.stage = "postprocessed"

                # 放入结果队列
                self.result_queue.put(item, timeout=1.0)

                postprocess_time = time.time() - start_time
                self.stats['postprocess_time'] += postprocess_time
                self.stats['processed_count'] += 1

            except Exception as e:
                print(f"❌ [后处理{worker_id}] 处理异常: {e}")
                continue

        print(f"🟢 后处理工作线程 {worker_id} 已停止")

    def start_pipeline(self, json_dir: str):
        """启动流水线处理"""
        print("🚀 启动挂灰检测流水线处理...")

        # 启动预处理工作线程
        for i in range(self.preprocess_workers):
            thread = threading.Thread(target=self._preprocess_worker, args=(i,))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        # 启动GPU推理工作线程
        thread = threading.Thread(target=self._inference_worker)
        thread.daemon = True
        thread.start()
        self._threads.append(thread)

        # 启动后处理工作线程
        for i in range(self.postprocess_workers):
            thread = threading.Thread(target=self._postprocess_worker,
                                    args=(i, json_dir))
            thread.daemon = True
            thread.start()
            self._threads.append(thread)

        print(f"✅ 挂灰检测流水线已启动 - 总线程数: {len(self._threads)}")

    def stop_pipeline(self):
        """停止流水线处理"""
        print("🛑 停止挂灰检测流水线处理...")
        self._stop_event.set()

        # 等待所有线程结束
        for thread in self._threads:
            thread.join(timeout=2.0)

        print("✅ 挂灰检测流水线已停止")

    def process_images_pipeline(self, image_paths: List[str], json_dir: str) -> int:
        """使用流水线处理图像列表"""
        if not image_paths:
            return 0

        total_start_time = time.time()

        # 启动流水线
        self.start_pipeline(json_dir)

        try:
            # 将所有图像路径放入预处理队列
            print(f"📥 将 {len(image_paths)} 张图像放入处理队列...")
            for image_path in image_paths:
                item = PipelineData(image_path=image_path, timestamp=time.time())

                # 等待队列有空间
                while not self.preprocess_queue.put(item, timeout=0.1):
                    if self._stop_event.is_set():
                        break
                    time.sleep(0.01)

            # 等待所有任务完成
            print("⏳ 等待所有任务完成...")
            processed_count = 0
            total_detections = 0

            # 监控处理进度
            last_progress_time = time.time()
            while processed_count < len(image_paths):
                result_item = self.result_queue.get(timeout=1.0)
                if result_item is not None:
                    processed_count += 1
                    if result_item.processed_result:
                        total_detections += result_item.processed_result.get('detection_count', 0)

                    # 显示进度（每5秒或每10%显示一次）
                    current_time = time.time()
                    progress = (processed_count / len(image_paths)) * 100
                    if (current_time - last_progress_time > 5.0) or (processed_count % max(1, len(image_paths) // 10) == 0):
                        # 获取队列状态
                        preprocess_size = self.preprocess_queue.qsize()
                        inference_size = self.inference_queue.qsize()
                        postprocess_size = self.postprocess_queue.qsize()
                        memory_stats = self.memory_pool.get_pool_stats()

                        print(f"📊 进度: {processed_count}/{len(image_paths)} ({progress:.1f}%) | "
                              f"队列: 预处理{preprocess_size} 推理{inference_size} 后处理{postprocess_size} | "
                              f"内存池: {memory_stats['available']}/{memory_stats['total']}")
                        last_progress_time = current_time

                # 检查是否超时
                if time.time() - total_start_time > 300:  # 5分钟超时
                    print("⚠️  处理超时，强制停止")
                    break

            total_time = time.time() - total_start_time
            self.stats['total_time'] = total_time

            # 输出性能统计
            self._print_performance_stats(len(image_paths), total_detections, total_time)

            return total_detections

        finally:
            # 停止流水线
            self.stop_pipeline()

    def _print_performance_stats(self, total_images: int, total_detections: int, total_time: float):
        """打印性能统计信息"""
        print(f"\n📊 挂灰检测流水线性能统计:")
        print(f"   总图像数量: {total_images}")
        print(f"   总检测数量: {total_detections}")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均每张图像: {total_time/total_images:.2f}秒")
        print(f"   处理速度: {total_images/total_time:.2f} 张/秒")

        if self.stats['processed_count'] > 0:
            avg_preprocess = self.stats['preprocess_time'] / self.stats['processed_count']
            avg_inference = self.stats['inference_time'] / self.stats['processed_count']
            avg_postprocess = self.stats['postprocess_time'] / self.stats['processed_count']

            print(f"   平均预处理时间: {avg_preprocess:.3f}秒")
            print(f"   平均推理时间: {avg_inference:.3f}秒")
            print(f"   平均后处理时间: {avg_postprocess:.3f}秒")

            # 计算并行效率
            sequential_time = avg_preprocess + avg_inference + avg_postprocess
            parallel_efficiency = (sequential_time / (total_time/total_images)) * 100
            print(f"   并行效率: {parallel_efficiency:.1f}%")


def main():
    """主函数"""
    try:
        print("🔥🔥🔥 这是GPU流水线并行版本 - 全新架构 🔥🔥🔥")
        print("🔥🔥🔥 执行main()函数 - 确认执行正确版本 🔥🔥🔥")
        print("🧪 烟叶挂灰特征检测GPU流水线并行版本")
        print("="*60)

        # 配置路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 输入路径配置
        input_image_dir = os.path.join(current_dir, 'test_guahui_images')
        output_json_dir = os.path.join(current_dir, 'test_output_guahui_pipeline')

        # 路径标准化
        input_image_dir = os.path.abspath(input_image_dir)
        output_json_dir = os.path.abspath(output_json_dir)

        # 设置模型路径
        model_path = os.path.join(current_dir, "guahui.onnx")

        # 设置输出目录
        os.makedirs(output_json_dir, exist_ok=True)

        # 验证输入目录
        if not os.path.exists(input_image_dir):
            print(f"❌ 输入目录不存在: {input_image_dir}")
            return False

        # 验证模型文件
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return False

        # 获取图像文件列表
        image_files = []
        for ext in ['.bmp', '.png', '.jpg', '.jpeg']:
            image_files.extend([f for f in os.listdir(input_image_dir) if f.lower().endswith(ext)])

        if not image_files:
            print(f"❌ 在目录中未找到图像文件: {input_image_dir}")
            return False

        print(f"📊 找到 {len(image_files)} 个图像文件")

        # 初始化GPU模型管理器
        current_thread_id = threading.current_thread().ident
        print(f"🔘 [线程{current_thread_id}] 初始化挂灰检测组件...")

        model_load_start = time.time()
        model_manager = GPUGuahuiModelManager(model_path, thread_id=current_thread_id)
        model_load_time = time.time() - model_load_start
        print(f"✅ 模型加载完成，耗时: {model_load_time:.3f}秒")

        # 使用流水线并行处理器 - 基于浮青检测的成功经验，使用简化配置
        pipeline_processor = GuahuiPipelineProcessor(
            model_manager,
            preprocess_workers=1,  # 1个预处理线程
            postprocess_workers=1,  # 1个后处理线程
            conf_threshold=0.4,
            iou_threshold=0.45
        )

        # 构建图像路径列表
        image_paths = [os.path.join(input_image_dir, image_file) for image_file in image_files]

        print(f"\n🚀 开始挂灰检测流水线并行处理...")

        # 流水线并行处理所有图像
        total_start_time = time.time()
        total_detections = pipeline_processor.process_images_pipeline(
            image_paths, output_json_dir
        )
        total_time = time.time() - total_start_time

        print(f"\n🎉 挂灰检测流水线并行处理完成！")
        print(f"📊 总处理时间: {total_time:.2f}秒")
        print(f"📊 平均每张图像: {total_time/len(image_files):.2f}秒")
        print(f"📊 处理吞吐量: {len(image_files)/total_time:.2f}张/秒")
        print(f"📊 总检测数量: {total_detections}")
        print(f"🚀 流水线并行架构 - 预处理/推理/后处理并行执行")

        return True

    except Exception as e:
        print(f"❌ 主函数执行异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
