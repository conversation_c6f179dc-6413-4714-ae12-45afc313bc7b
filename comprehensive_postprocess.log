2025-07-30 13:36:31,169 - INFO - 🔥🔥🔥 烟叶综合后处理系统启动 🔥🔥🔥
2025-07-30 13:36:31,169 - INFO - ================================================================================
2025-07-30 13:36:31,169 - INFO - 项目: 烟叶综合后处理系统
2025-07-30 13:36:31,169 - INFO - 版本: 1.0.0
2025-07-30 13:36:31,169 - INFO - 作者: 系统架构师
2025-07-30 13:36:31,170 - INFO - ================================================================================
2025-07-30 13:36:31,170 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:36:31,170 - INFO - 📁 输入目录: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:36:31,170 - INFO - 📁 输出目录: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:36:31,170 - INFO - ⚡ 最大工作线程: 16
2025-07-30 13:36:31,170 - INFO - 
🧪 第一阶段：测试各个模块
2025-07-30 13:36:31,170 - INFO - ============================================================
2025-07-30 13:36:31,170 - INFO - 🧪🧪🧪 开始测试各个模块的独立运行 🧪🧪🧪
2025-07-30 13:36:31,170 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:36:31,170 - INFO - 初始化完成: 12 个模块
2025-07-30 13:36:31,170 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:36:31,170 - INFO - 
🧪 测试所有模块...
2025-07-30 13:36:31,170 - INFO - ============================================================
2025-07-30 13:36:31,170 - INFO - 🧪 测试模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:36:31,170 - ERROR - ❌ 模块 zhumaidakai_select 导入失败: No module named 'deploy_seg_zhumaidakai_select_optimized'
2025-07-30 13:36:31,170 - INFO - 🧪 测试模块: seg_zhimai_split (支脉分离处理)
2025-07-30 13:36:31,298 - INFO - ✅ 模块 seg_zhimai_split 导入成功
2025-07-30 13:36:31,298 - INFO - ✅ 模块 seg_zhimai_split 具有main函数
2025-07-30 13:36:31,299 - INFO - 🧪 测试模块: yuandu_halcon (圆度计算)
2025-07-30 13:36:31,301 - INFO - ✅ 模块 yuandu_halcon 导入成功
2025-07-30 13:36:31,301 - INFO - ✅ 模块 yuandu_halcon 具有main函数
2025-07-30 13:36:31,301 - INFO - 🧪 测试模块: dealing_images_juchi (锯齿平滑处理)
2025-07-30 13:36:31,463 - INFO - ✅ 模块 dealing_images_juchi 导入成功
2025-07-30 13:36:31,464 - INFO - ✅ 模块 dealing_images_juchi 具有main函数
2025-07-30 13:36:31,464 - INFO - 🧪 测试模块: import_yanye_user_feature (主要特征分析)
2025-07-30 13:36:31,477 - INFO - ✅ 模块 import_yanye_user_feature 导入成功
2025-07-30 13:36:31,477 - INFO - ✅ 模块 import_yanye_user_feature 具有main函数
2025-07-30 13:36:31,477 - INFO - 🧪 测试模块: import_yanye_user_feature_rgb_hist (主要特征颜色分析)
2025-07-30 13:36:31,735 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 导入成功
2025-07-30 13:36:31,735 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 具有main函数
2025-07-30 13:36:31,735 - INFO - 🧪 测试模块: user_feature_tobacoo_width (烟叶宽度计算)
2025-07-30 13:36:31,740 - INFO - ✅ 模块 user_feature_tobacoo_width 导入成功
2025-07-30 13:36:31,740 - INFO - ✅ 模块 user_feature_tobacoo_width 具有main函数
2025-07-30 13:36:31,740 - INFO - 🧪 测试模块: user_feature_smoothness (烟叶平滑度计算)
2025-07-30 13:36:31,748 - INFO - ✅ 模块 user_feature_smoothness 导入成功
2025-07-30 13:36:31,748 - INFO - ✅ 模块 user_feature_smoothness 具有main函数
2025-07-30 13:36:31,748 - INFO - 🧪 测试模块: correlation_json_generate (烟叶相关性分析)
2025-07-30 13:36:31,758 - INFO - ✅ 模块 correlation_json_generate 导入成功
2025-07-30 13:36:31,758 - INFO - ✅ 模块 correlation_json_generate 具有main函数
2025-07-30 13:36:31,758 - INFO - 🧪 测试模块: sum_yanye_user_feature (烟叶特征汇总)
2025-07-30 13:36:31,761 - INFO - ✅ 模块 sum_yanye_user_feature 导入成功
2025-07-30 13:36:31,761 - INFO - ✅ 模块 sum_yanye_user_feature 具有main函数
2025-07-30 13:36:31,761 - INFO - 🧪 测试模块: color_variety (烟叶颜色多样性分析)
2025-07-30 13:36:31,763 - INFO - ✅ 模块 color_variety 导入成功
2025-07-30 13:36:31,763 - INFO - ✅ 模块 color_variety 具有main函数
2025-07-30 13:36:31,763 - INFO - 🧪 测试模块: canque_type_category (残缺类型分类和排序)
2025-07-30 13:36:31,770 - INFO - ✅ 模块 canque_type_category 导入成功
2025-07-30 13:36:31,770 - INFO - ✅ 模块 canque_type_category 具有main函数
2025-07-30 13:36:31,770 - ERROR - ❌ 1 个模块测试失败:
2025-07-30 13:36:31,770 - ERROR -    - zhumaidakai_select
2025-07-30 13:36:31,770 - ERROR - ❌ 部分模块测试失败，请检查模块配置
2025-07-30 13:36:31,770 - ERROR - ❌ 模块测试失败，程序退出
2025-07-30 13:37:46,820 - INFO - 🔥🔥🔥 烟叶综合后处理系统启动 🔥🔥🔥
2025-07-30 13:37:46,820 - INFO - ================================================================================
2025-07-30 13:37:46,820 - INFO - 项目: 烟叶综合后处理系统
2025-07-30 13:37:46,820 - INFO - 版本: 1.0.0
2025-07-30 13:37:46,821 - INFO - 作者: 系统架构师
2025-07-30 13:37:46,821 - INFO - ================================================================================
2025-07-30 13:37:46,821 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:37:46,821 - INFO - 📁 输入目录: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:37:46,821 - INFO - 📁 输出目录: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:37:46,821 - INFO - ⚡ 最大工作线程: 16
2025-07-30 13:37:46,821 - INFO - 
🧪 第一阶段：测试各个模块
2025-07-30 13:37:46,821 - INFO - ============================================================
2025-07-30 13:37:46,821 - INFO - 🧪🧪🧪 开始测试各个模块的独立运行 🧪🧪🧪
2025-07-30 13:37:46,821 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:37:46,821 - INFO - 初始化完成: 12 个模块
2025-07-30 13:37:46,821 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:37:46,821 - INFO - 
🧪 测试所有模块...
2025-07-30 13:37:46,821 - INFO - ============================================================
2025-07-30 13:37:46,821 - INFO - 🧪 测试模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:37:46,956 - INFO - ✅ 模块 zhumaidakai_select 导入成功
2025-07-30 13:37:46,956 - INFO - ✅ 模块 zhumaidakai_select 具有main函数
2025-07-30 13:37:46,957 - INFO - 🧪 测试模块: seg_zhimai_split (支脉分离处理)
2025-07-30 13:37:46,966 - INFO - ✅ 模块 seg_zhimai_split 导入成功
2025-07-30 13:37:46,966 - INFO - ✅ 模块 seg_zhimai_split 具有main函数
2025-07-30 13:37:46,966 - INFO - 🧪 测试模块: yuandu_halcon (圆度计算)
2025-07-30 13:37:46,967 - INFO - ✅ 模块 yuandu_halcon 导入成功
2025-07-30 13:37:46,967 - INFO - ✅ 模块 yuandu_halcon 具有main函数
2025-07-30 13:37:46,967 - INFO - 🧪 测试模块: dealing_images_juchi (锯齿平滑处理)
2025-07-30 13:37:47,124 - INFO - ✅ 模块 dealing_images_juchi 导入成功
2025-07-30 13:37:47,124 - INFO - ✅ 模块 dealing_images_juchi 具有main函数
2025-07-30 13:37:47,124 - INFO - 🧪 测试模块: import_yanye_user_feature (主要特征分析)
2025-07-30 13:37:47,127 - INFO - ✅ 模块 import_yanye_user_feature 导入成功
2025-07-30 13:37:47,127 - INFO - ✅ 模块 import_yanye_user_feature 具有main函数
2025-07-30 13:37:47,127 - INFO - 🧪 测试模块: import_yanye_user_feature_rgb_hist (主要特征颜色分析)
2025-07-30 13:37:47,403 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 导入成功
2025-07-30 13:37:47,403 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 具有main函数
2025-07-30 13:37:47,403 - INFO - 🧪 测试模块: user_feature_tobacoo_width (烟叶宽度计算)
2025-07-30 13:37:47,404 - INFO - ✅ 模块 user_feature_tobacoo_width 导入成功
2025-07-30 13:37:47,404 - INFO - ✅ 模块 user_feature_tobacoo_width 具有main函数
2025-07-30 13:37:47,404 - INFO - 🧪 测试模块: user_feature_smoothness (烟叶平滑度计算)
2025-07-30 13:37:47,411 - INFO - ✅ 模块 user_feature_smoothness 导入成功
2025-07-30 13:37:47,411 - INFO - ✅ 模块 user_feature_smoothness 具有main函数
2025-07-30 13:37:47,411 - INFO - 🧪 测试模块: correlation_json_generate (烟叶相关性分析)
2025-07-30 13:37:47,414 - INFO - ✅ 模块 correlation_json_generate 导入成功
2025-07-30 13:37:47,414 - INFO - ✅ 模块 correlation_json_generate 具有main函数
2025-07-30 13:37:47,414 - INFO - 🧪 测试模块: sum_yanye_user_feature (烟叶特征汇总)
2025-07-30 13:37:47,418 - INFO - ✅ 模块 sum_yanye_user_feature 导入成功
2025-07-30 13:37:47,419 - INFO - ✅ 模块 sum_yanye_user_feature 具有main函数
2025-07-30 13:37:47,419 - INFO - 🧪 测试模块: color_variety (烟叶颜色多样性分析)
2025-07-30 13:37:47,420 - INFO - ✅ 模块 color_variety 导入成功
2025-07-30 13:37:47,420 - INFO - ✅ 模块 color_variety 具有main函数
2025-07-30 13:37:47,420 - INFO - 🧪 测试模块: canque_type_category (残缺类型分类和排序)
2025-07-30 13:37:47,421 - INFO - ✅ 模块 canque_type_category 导入成功
2025-07-30 13:37:47,421 - INFO - ✅ 模块 canque_type_category 具有main函数
2025-07-30 13:37:47,421 - INFO - ✅ 所有 12 个模块测试通过
2025-07-30 13:37:47,421 - INFO - 🎉 所有模块测试通过，可以进行组合运行
2025-07-30 13:37:47,422 - INFO - ✅ 第一阶段完成：所有模块测试通过
2025-07-30 13:37:47,422 - INFO - 
🚀 第二阶段：执行完整流水线
2025-07-30 13:37:47,422 - INFO - ============================================================
2025-07-30 13:37:47,422 - INFO - 初始化完成: 12 个模块
2025-07-30 13:37:47,422 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:37:47,422 - INFO - 🚀 开始执行烟叶综合后处理流水线
2025-07-30 13:37:47,422 - INFO - ================================================================================
2025-07-30 13:37:47,422 - INFO - 
🔍 阶段1: 系统初始化
2025-07-30 13:37:47,422 - INFO - --------------------------------------------------
2025-07-30 13:37:47,422 - INFO - 🔍 验证运行环境...
2025-07-30 13:37:47,422 - INFO - Python版本: 3.12.9
2025-07-30 13:37:47,422 - INFO - ✅ 输入目录存在: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:37:47,422 - INFO - ✅ 输出目录准备完成: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:37:47,422 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-6.bmp
2025-07-30 13:37:47,422 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-4.bmp
2025-07-30 13:37:47,422 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-3.bmp
2025-07-30 13:37:47,422 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-5.bmp
2025-07-30 13:37:47,422 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-7.bmp
2025-07-30 13:37:47,422 - INFO - ✅ 找到 5 个图像文件
2025-07-30 13:37:47,422 - INFO - 🔍 验证模块文件存在性...
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: zhumaidakai_select
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: seg_zhimai_split
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: yuandu_halcon
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: dealing_images_juchi
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: import_yanye_user_feature
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: import_yanye_user_feature_rgb_hist
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: user_feature_tobacoo_width
2025-07-30 13:37:47,422 - INFO - ✅ 模块文件存在: user_feature_smoothness
2025-07-30 13:37:47,423 - INFO - ✅ 模块文件存在: correlation_json_generate
2025-07-30 13:37:47,423 - INFO - ✅ 模块文件存在: sum_yanye_user_feature
2025-07-30 13:37:47,423 - INFO - ✅ 模块文件存在: color_variety
2025-07-30 13:37:47,423 - INFO - ✅ 模块文件存在: canque_type_category
2025-07-30 13:37:47,423 - INFO - ✅ 所有模块文件验证通过
2025-07-30 13:37:47,423 - INFO - ✅ 阶段1完成: 系统初始化成功
2025-07-30 13:37:47,423 - INFO - 
🔄 阶段2: 主脉打开选择处理 (串行)
2025-07-30 13:37:47,423 - INFO - --------------------------------------------------
2025-07-30 13:37:47,423 - INFO - 🔄 执行模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:37:47,423 - INFO - ✅ 模块 zhumaidakai_select 执行成功，耗时: 0.00秒
2025-07-30 13:37:47,423 - ERROR - ❌ 未找到主脉打开选择模块生成的JSON文件
2025-07-30 13:37:47,423 - ERROR - 
❌ 烟叶综合后处理系统执行失败！
2025-07-30 13:37:47,423 - ERROR - 📊 耗时: 0.00秒
2025-07-30 13:40:09,669 - INFO - 🔥🔥🔥 烟叶综合后处理系统启动 🔥🔥🔥
2025-07-30 13:40:09,669 - INFO - ================================================================================
2025-07-30 13:40:09,669 - INFO - 项目: 烟叶综合后处理系统
2025-07-30 13:40:09,670 - INFO - 版本: 1.0.0
2025-07-30 13:40:09,670 - INFO - 作者: 系统架构师
2025-07-30 13:40:09,670 - INFO - ================================================================================
2025-07-30 13:40:09,670 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:40:09,670 - INFO - 📁 输入目录: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:40:09,670 - INFO - 📁 输出目录: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:40:09,670 - INFO - ⚡ 最大工作线程: 16
2025-07-30 13:40:09,670 - INFO - 
🧪 第一阶段：测试各个模块
2025-07-30 13:40:09,670 - INFO - ============================================================
2025-07-30 13:40:09,670 - INFO - 🧪🧪🧪 开始测试各个模块的独立运行 🧪🧪🧪
2025-07-30 13:40:09,670 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:40:09,670 - INFO - 初始化完成: 12 个模块
2025-07-30 13:40:09,670 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:40:09,670 - INFO - 
🧪 测试所有模块...
2025-07-30 13:40:09,670 - INFO - ============================================================
2025-07-30 13:40:09,670 - INFO - 🧪 测试模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:40:09,795 - INFO - ✅ 模块 zhumaidakai_select 导入成功
2025-07-30 13:40:09,795 - INFO - ✅ 模块 zhumaidakai_select 具有main函数
2025-07-30 13:40:09,795 - INFO - 🧪 测试模块: seg_zhimai_split (支脉分离处理)
2025-07-30 13:40:09,803 - INFO - ✅ 模块 seg_zhimai_split 导入成功
2025-07-30 13:40:09,803 - INFO - ✅ 模块 seg_zhimai_split 具有main函数
2025-07-30 13:40:09,803 - INFO - 🧪 测试模块: yuandu_halcon (圆度计算)
2025-07-30 13:40:09,803 - INFO - ✅ 模块 yuandu_halcon 导入成功
2025-07-30 13:40:09,803 - INFO - ✅ 模块 yuandu_halcon 具有main函数
2025-07-30 13:40:09,803 - INFO - 🧪 测试模块: dealing_images_juchi (锯齿平滑处理)
2025-07-30 13:40:09,952 - INFO - ✅ 模块 dealing_images_juchi 导入成功
2025-07-30 13:40:09,953 - INFO - ✅ 模块 dealing_images_juchi 具有main函数
2025-07-30 13:40:09,953 - INFO - 🧪 测试模块: import_yanye_user_feature (主要特征分析)
2025-07-30 13:40:09,955 - INFO - ✅ 模块 import_yanye_user_feature 导入成功
2025-07-30 13:40:09,955 - INFO - ✅ 模块 import_yanye_user_feature 具有main函数
2025-07-30 13:40:09,956 - INFO - 🧪 测试模块: import_yanye_user_feature_rgb_hist (主要特征颜色分析)
2025-07-30 13:40:10,213 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 导入成功
2025-07-30 13:40:10,213 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 具有main函数
2025-07-30 13:40:10,213 - INFO - 🧪 测试模块: user_feature_tobacoo_width (烟叶宽度计算)
2025-07-30 13:40:10,214 - INFO - ✅ 模块 user_feature_tobacoo_width 导入成功
2025-07-30 13:40:10,214 - INFO - ✅ 模块 user_feature_tobacoo_width 具有main函数
2025-07-30 13:40:10,214 - INFO - 🧪 测试模块: user_feature_smoothness (烟叶平滑度计算)
2025-07-30 13:40:10,219 - INFO - ✅ 模块 user_feature_smoothness 导入成功
2025-07-30 13:40:10,219 - INFO - ✅ 模块 user_feature_smoothness 具有main函数
2025-07-30 13:40:10,219 - INFO - 🧪 测试模块: correlation_json_generate (烟叶相关性分析)
2025-07-30 13:40:10,222 - INFO - ✅ 模块 correlation_json_generate 导入成功
2025-07-30 13:40:10,222 - INFO - ✅ 模块 correlation_json_generate 具有main函数
2025-07-30 13:40:10,222 - INFO - 🧪 测试模块: sum_yanye_user_feature (烟叶特征汇总)
2025-07-30 13:40:10,226 - INFO - ✅ 模块 sum_yanye_user_feature 导入成功
2025-07-30 13:40:10,226 - INFO - ✅ 模块 sum_yanye_user_feature 具有main函数
2025-07-30 13:40:10,226 - INFO - 🧪 测试模块: color_variety (烟叶颜色多样性分析)
2025-07-30 13:40:10,227 - INFO - ✅ 模块 color_variety 导入成功
2025-07-30 13:40:10,228 - INFO - ✅ 模块 color_variety 具有main函数
2025-07-30 13:40:10,228 - INFO - 🧪 测试模块: canque_type_category (残缺类型分类和排序)
2025-07-30 13:40:10,229 - INFO - ✅ 模块 canque_type_category 导入成功
2025-07-30 13:40:10,229 - INFO - ✅ 模块 canque_type_category 具有main函数
2025-07-30 13:40:10,229 - INFO - ✅ 所有 12 个模块测试通过
2025-07-30 13:40:10,229 - INFO - 🎉 所有模块测试通过，可以进行组合运行
2025-07-30 13:40:10,229 - INFO - ✅ 第一阶段完成：所有模块测试通过
2025-07-30 13:40:10,229 - INFO - 
🚀 第二阶段：执行完整流水线
2025-07-30 13:40:10,229 - INFO - ============================================================
2025-07-30 13:40:10,229 - INFO - 初始化完成: 12 个模块
2025-07-30 13:40:10,229 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:40:10,229 - INFO - 🚀 开始执行烟叶综合后处理流水线
2025-07-30 13:40:10,229 - INFO - ================================================================================
2025-07-30 13:40:10,229 - INFO - 
🔍 阶段1: 系统初始化
2025-07-30 13:40:10,229 - INFO - --------------------------------------------------
2025-07-30 13:40:10,229 - INFO - 🔍 验证运行环境...
2025-07-30 13:40:10,229 - INFO - Python版本: 3.12.9
2025-07-30 13:40:10,229 - INFO - ✅ 输入目录存在: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:40:10,229 - INFO - ✅ 输出目录准备完成: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:40:10,229 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-6.bmp
2025-07-30 13:40:10,229 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-4.bmp
2025-07-30 13:40:10,229 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-3.bmp
2025-07-30 13:40:10,229 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-5.bmp
2025-07-30 13:40:10,229 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-7.bmp
2025-07-30 13:40:10,229 - INFO - ✅ 找到 5 个图像文件
2025-07-30 13:40:10,229 - INFO - 🔍 验证模块文件存在性...
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: zhumaidakai_select
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: seg_zhimai_split
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: yuandu_halcon
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: dealing_images_juchi
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: import_yanye_user_feature
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: import_yanye_user_feature_rgb_hist
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: user_feature_tobacoo_width
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: user_feature_smoothness
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: correlation_json_generate
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: sum_yanye_user_feature
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: color_variety
2025-07-30 13:40:10,230 - INFO - ✅ 模块文件存在: canque_type_category
2025-07-30 13:40:10,230 - INFO - ✅ 所有模块文件验证通过
2025-07-30 13:40:10,230 - INFO - ✅ 阶段1完成: 系统初始化成功
2025-07-30 13:40:10,230 - INFO - 
🔄 阶段2: 主脉打开选择处理 (串行)
2025-07-30 13:40:10,230 - INFO - --------------------------------------------------
2025-07-30 13:40:10,230 - INFO - 🔄 执行模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:40:10,255 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-6.bmp -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,279 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-4.bmp -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,279 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-4.json -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,280 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-5.json -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,280 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-7.json -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,304 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-3.bmp -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,328 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-5.bmp -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,328 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-6.json -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,329 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-3.json -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,354 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-7.bmp -> /home/<USER>/xm/code/coderafactor/seg_zhumaidakai_select/test_data/test_inputs
2025-07-30 13:40:10,354 - INFO - ✅ 模块 zhumaidakai_select 执行成功，耗时: 0.12秒
2025-07-30 13:40:10,354 - ERROR - ❌ 未找到主脉打开选择模块生成的JSON文件
2025-07-30 13:40:10,354 - ERROR - 
❌ 烟叶综合后处理系统执行失败！
2025-07-30 13:40:10,354 - ERROR - 📊 耗时: 0.12秒
2025-07-30 13:41:00,397 - INFO - 🔥🔥🔥 烟叶综合后处理系统启动 🔥🔥🔥
2025-07-30 13:41:00,397 - INFO - ================================================================================
2025-07-30 13:41:00,397 - INFO - 项目: 烟叶综合后处理系统
2025-07-30 13:41:00,397 - INFO - 版本: 1.0.0
2025-07-30 13:41:00,397 - INFO - 作者: 系统架构师
2025-07-30 13:41:00,397 - INFO - ================================================================================
2025-07-30 13:41:00,397 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:41:00,397 - INFO - 📁 输入目录: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:41:00,397 - INFO - 📁 输出目录: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:41:00,397 - INFO - ⚡ 最大工作线程: 16
2025-07-30 13:41:00,397 - INFO - 
🧪 第一阶段：测试各个模块
2025-07-30 13:41:00,397 - INFO - ============================================================
2025-07-30 13:41:00,397 - INFO - 🧪🧪🧪 开始测试各个模块的独立运行 🧪🧪🧪
2025-07-30 13:41:00,397 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:41:00,397 - INFO - 初始化完成: 12 个模块
2025-07-30 13:41:00,397 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:41:00,397 - INFO - 
🧪 测试所有模块...
2025-07-30 13:41:00,397 - INFO - ============================================================
2025-07-30 13:41:00,397 - INFO - 🧪 测试模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:41:00,509 - INFO - ✅ 模块 zhumaidakai_select 导入成功
2025-07-30 13:41:00,509 - INFO - ✅ 模块 zhumaidakai_select 具有main函数
2025-07-30 13:41:00,510 - INFO - 🧪 测试模块: seg_zhimai_split (支脉分离处理)
2025-07-30 13:41:00,520 - INFO - ✅ 模块 seg_zhimai_split 导入成功
2025-07-30 13:41:00,520 - INFO - ✅ 模块 seg_zhimai_split 具有main函数
2025-07-30 13:41:00,520 - INFO - 🧪 测试模块: yuandu_halcon (圆度计算)
2025-07-30 13:41:00,520 - INFO - ✅ 模块 yuandu_halcon 导入成功
2025-07-30 13:41:00,520 - INFO - ✅ 模块 yuandu_halcon 具有main函数
2025-07-30 13:41:00,520 - INFO - 🧪 测试模块: dealing_images_juchi (锯齿平滑处理)
2025-07-30 13:41:00,675 - INFO - ✅ 模块 dealing_images_juchi 导入成功
2025-07-30 13:41:00,675 - INFO - ✅ 模块 dealing_images_juchi 具有main函数
2025-07-30 13:41:00,675 - INFO - 🧪 测试模块: import_yanye_user_feature (主要特征分析)
2025-07-30 13:41:00,678 - INFO - ✅ 模块 import_yanye_user_feature 导入成功
2025-07-30 13:41:00,678 - INFO - ✅ 模块 import_yanye_user_feature 具有main函数
2025-07-30 13:41:00,678 - INFO - 🧪 测试模块: import_yanye_user_feature_rgb_hist (主要特征颜色分析)
2025-07-30 13:41:00,937 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 导入成功
2025-07-30 13:41:00,937 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 具有main函数
2025-07-30 13:41:00,937 - INFO - 🧪 测试模块: user_feature_tobacoo_width (烟叶宽度计算)
2025-07-30 13:41:00,938 - INFO - ✅ 模块 user_feature_tobacoo_width 导入成功
2025-07-30 13:41:00,938 - INFO - ✅ 模块 user_feature_tobacoo_width 具有main函数
2025-07-30 13:41:00,938 - INFO - 🧪 测试模块: user_feature_smoothness (烟叶平滑度计算)
2025-07-30 13:41:00,943 - INFO - ✅ 模块 user_feature_smoothness 导入成功
2025-07-30 13:41:00,944 - INFO - ✅ 模块 user_feature_smoothness 具有main函数
2025-07-30 13:41:00,944 - INFO - 🧪 测试模块: correlation_json_generate (烟叶相关性分析)
2025-07-30 13:41:00,946 - INFO - ✅ 模块 correlation_json_generate 导入成功
2025-07-30 13:41:00,946 - INFO - ✅ 模块 correlation_json_generate 具有main函数
2025-07-30 13:41:00,946 - INFO - 🧪 测试模块: sum_yanye_user_feature (烟叶特征汇总)
2025-07-30 13:41:00,950 - INFO - ✅ 模块 sum_yanye_user_feature 导入成功
2025-07-30 13:41:00,950 - INFO - ✅ 模块 sum_yanye_user_feature 具有main函数
2025-07-30 13:41:00,950 - INFO - 🧪 测试模块: color_variety (烟叶颜色多样性分析)
2025-07-30 13:41:00,951 - INFO - ✅ 模块 color_variety 导入成功
2025-07-30 13:41:00,951 - INFO - ✅ 模块 color_variety 具有main函数
2025-07-30 13:41:00,951 - INFO - 🧪 测试模块: canque_type_category (残缺类型分类和排序)
2025-07-30 13:41:00,953 - INFO - ✅ 模块 canque_type_category 导入成功
2025-07-30 13:41:00,953 - INFO - ✅ 模块 canque_type_category 具有main函数
2025-07-30 13:41:00,953 - INFO - ✅ 所有 12 个模块测试通过
2025-07-30 13:41:00,953 - INFO - 🎉 所有模块测试通过，可以进行组合运行
2025-07-30 13:41:00,953 - INFO - ✅ 第一阶段完成：所有模块测试通过
2025-07-30 13:41:00,953 - INFO - 
🚀 第二阶段：执行完整流水线
2025-07-30 13:41:00,953 - INFO - ============================================================
2025-07-30 13:41:00,953 - INFO - 初始化完成: 12 个模块
2025-07-30 13:41:00,953 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:41:00,953 - INFO - 🚀 开始执行烟叶综合后处理流水线
2025-07-30 13:41:00,953 - INFO - ================================================================================
2025-07-30 13:41:00,953 - INFO - 
🔍 阶段1: 系统初始化
2025-07-30 13:41:00,953 - INFO - --------------------------------------------------
2025-07-30 13:41:00,953 - INFO - 🔍 验证运行环境...
2025-07-30 13:41:00,953 - INFO - Python版本: 3.12.9
2025-07-30 13:41:00,953 - INFO - ✅ 输入目录存在: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:41:00,953 - INFO - ✅ 输出目录准备完成: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:41:00,953 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-6.bmp
2025-07-30 13:41:00,953 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-4.bmp
2025-07-30 13:41:00,953 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-3.bmp
2025-07-30 13:41:00,953 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-5.bmp
2025-07-30 13:41:00,953 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-7.bmp
2025-07-30 13:41:00,953 - INFO - ✅ 找到 5 个图像文件
2025-07-30 13:41:00,953 - INFO - 🔍 验证模块文件存在性...
2025-07-30 13:41:00,953 - INFO - ✅ 模块文件存在: zhumaidakai_select
2025-07-30 13:41:00,953 - INFO - ✅ 模块文件存在: seg_zhimai_split
2025-07-30 13:41:00,953 - INFO - ✅ 模块文件存在: yuandu_halcon
2025-07-30 13:41:00,953 - INFO - ✅ 模块文件存在: dealing_images_juchi
2025-07-30 13:41:00,953 - INFO - ✅ 模块文件存在: import_yanye_user_feature
2025-07-30 13:41:00,954 - INFO - ✅ 模块文件存在: import_yanye_user_feature_rgb_hist
2025-07-30 13:41:00,954 - INFO - ✅ 模块文件存在: user_feature_tobacoo_width
2025-07-30 13:41:00,954 - INFO - ✅ 模块文件存在: user_feature_smoothness
2025-07-30 13:41:00,954 - INFO - ✅ 模块文件存在: correlation_json_generate
2025-07-30 13:41:00,954 - INFO - ✅ 模块文件存在: sum_yanye_user_feature
2025-07-30 13:41:00,954 - INFO - ✅ 模块文件存在: color_variety
2025-07-30 13:41:00,954 - INFO - ✅ 模块文件存在: canque_type_category
2025-07-30 13:41:00,954 - INFO - ✅ 所有模块文件验证通过
2025-07-30 13:41:00,954 - INFO - ✅ 阶段1完成: 系统初始化成功
2025-07-30 13:41:00,954 - INFO - 
🔄 阶段2: 主脉打开选择处理 (串行)
2025-07-30 13:41:00,954 - INFO - --------------------------------------------------
2025-07-30 13:41:00,954 - INFO - 🔄 执行模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:41:00,979 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-6.bmp -> test_images
2025-07-30 13:41:01,003 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-4.bmp -> test_images
2025-07-30 13:41:01,003 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-4.json -> test_images
2025-07-30 13:41:01,003 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-5.json -> test_images
2025-07-30 13:41:01,004 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-7.json -> test_images
2025-07-30 13:41:01,029 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-3.bmp -> test_images
2025-07-30 13:41:01,055 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-5.bmp -> test_images
2025-07-30 13:41:01,055 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-6.json -> test_images
2025-07-30 13:41:01,056 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-3.json -> test_images
2025-07-30 13:41:01,082 - INFO - 📋 复制文件: hn-cz-2023-C2F-C21-7.bmp -> test_images
2025-07-30 13:41:01,082 - INFO - ✅ 模块 zhumaidakai_select 执行成功，耗时: 0.13秒
2025-07-30 13:41:01,082 - ERROR - ❌ 未找到主脉打开选择模块生成的JSON文件
2025-07-30 13:41:01,083 - ERROR - 
❌ 烟叶综合后处理系统执行失败！
2025-07-30 13:41:01,083 - ERROR - 📊 耗时: 0.13秒
2025-07-30 13:42:08,369 - INFO - 🔥🔥🔥 烟叶综合后处理系统启动 🔥🔥🔥
2025-07-30 13:42:08,369 - INFO - ================================================================================
2025-07-30 13:42:08,369 - INFO - 项目: 烟叶综合后处理系统
2025-07-30 13:42:08,369 - INFO - 版本: 1.0.0
2025-07-30 13:42:08,369 - INFO - 作者: 系统架构师
2025-07-30 13:42:08,369 - INFO - ================================================================================
2025-07-30 13:42:08,369 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:42:08,369 - INFO - 📁 输入目录: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:42:08,369 - INFO - 📁 输出目录: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:42:08,369 - INFO - ⚡ 最大工作线程: 16
2025-07-30 13:42:08,369 - INFO - 
🧪 第一阶段：测试各个模块
2025-07-30 13:42:08,369 - INFO - ============================================================
2025-07-30 13:42:08,369 - INFO - 🧪🧪🧪 开始测试各个模块的独立运行 🧪🧪🧪
2025-07-30 13:42:08,369 - INFO - 配置初始化: 最大工作线程=16, CPU核心数=96
2025-07-30 13:42:08,369 - INFO - 初始化完成: 12 个模块
2025-07-30 13:42:08,369 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:42:08,369 - INFO - 
🧪 测试所有模块...
2025-07-30 13:42:08,369 - INFO - ============================================================
2025-07-30 13:42:08,369 - INFO - 🧪 测试模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:42:08,481 - INFO - ✅ 模块 zhumaidakai_select 导入成功
2025-07-30 13:42:08,481 - INFO - ✅ 模块 zhumaidakai_select 具有main函数
2025-07-30 13:42:08,481 - INFO - 🧪 测试模块: seg_zhimai_split (支脉分离处理)
2025-07-30 13:42:08,491 - INFO - ✅ 模块 seg_zhimai_split 导入成功
2025-07-30 13:42:08,491 - INFO - ✅ 模块 seg_zhimai_split 具有main函数
2025-07-30 13:42:08,491 - INFO - 🧪 测试模块: yuandu_halcon (圆度计算)
2025-07-30 13:42:08,491 - INFO - ✅ 模块 yuandu_halcon 导入成功
2025-07-30 13:42:08,492 - INFO - ✅ 模块 yuandu_halcon 具有main函数
2025-07-30 13:42:08,492 - INFO - 🧪 测试模块: dealing_images_juchi (锯齿平滑处理)
2025-07-30 13:42:08,649 - INFO - ✅ 模块 dealing_images_juchi 导入成功
2025-07-30 13:42:08,649 - INFO - ✅ 模块 dealing_images_juchi 具有main函数
2025-07-30 13:42:08,649 - INFO - 🧪 测试模块: import_yanye_user_feature (主要特征分析)
2025-07-30 13:42:08,651 - INFO - ✅ 模块 import_yanye_user_feature 导入成功
2025-07-30 13:42:08,651 - INFO - ✅ 模块 import_yanye_user_feature 具有main函数
2025-07-30 13:42:08,651 - INFO - 🧪 测试模块: import_yanye_user_feature_rgb_hist (主要特征颜色分析)
2025-07-30 13:42:08,921 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 导入成功
2025-07-30 13:42:08,921 - INFO - ✅ 模块 import_yanye_user_feature_rgb_hist 具有main函数
2025-07-30 13:42:08,921 - INFO - 🧪 测试模块: user_feature_tobacoo_width (烟叶宽度计算)
2025-07-30 13:42:08,923 - INFO - ✅ 模块 user_feature_tobacoo_width 导入成功
2025-07-30 13:42:08,923 - INFO - ✅ 模块 user_feature_tobacoo_width 具有main函数
2025-07-30 13:42:08,923 - INFO - 🧪 测试模块: user_feature_smoothness (烟叶平滑度计算)
2025-07-30 13:42:08,929 - INFO - ✅ 模块 user_feature_smoothness 导入成功
2025-07-30 13:42:08,929 - INFO - ✅ 模块 user_feature_smoothness 具有main函数
2025-07-30 13:42:08,929 - INFO - 🧪 测试模块: correlation_json_generate (烟叶相关性分析)
2025-07-30 13:42:08,932 - INFO - ✅ 模块 correlation_json_generate 导入成功
2025-07-30 13:42:08,933 - INFO - ✅ 模块 correlation_json_generate 具有main函数
2025-07-30 13:42:08,933 - INFO - 🧪 测试模块: sum_yanye_user_feature (烟叶特征汇总)
2025-07-30 13:42:08,936 - INFO - ✅ 模块 sum_yanye_user_feature 导入成功
2025-07-30 13:42:08,937 - INFO - ✅ 模块 sum_yanye_user_feature 具有main函数
2025-07-30 13:42:08,937 - INFO - 🧪 测试模块: color_variety (烟叶颜色多样性分析)
2025-07-30 13:42:08,938 - INFO - ✅ 模块 color_variety 导入成功
2025-07-30 13:42:08,938 - INFO - ✅ 模块 color_variety 具有main函数
2025-07-30 13:42:08,938 - INFO - 🧪 测试模块: canque_type_category (残缺类型分类和排序)
2025-07-30 13:42:08,940 - INFO - ✅ 模块 canque_type_category 导入成功
2025-07-30 13:42:08,940 - INFO - ✅ 模块 canque_type_category 具有main函数
2025-07-30 13:42:08,940 - INFO - ✅ 所有 12 个模块测试通过
2025-07-30 13:42:08,940 - INFO - 🎉 所有模块测试通过，可以进行组合运行
2025-07-30 13:42:08,940 - INFO - ✅ 第一阶段完成：所有模块测试通过
2025-07-30 13:42:08,940 - INFO - 
🚀 第二阶段：执行完整流水线
2025-07-30 13:42:08,940 - INFO - ============================================================
2025-07-30 13:42:08,940 - INFO - 初始化完成: 12 个模块
2025-07-30 13:42:08,940 - INFO - 🔥🔥🔥 烟叶综合后处理系统初始化完成 🔥🔥🔥
2025-07-30 13:42:08,940 - INFO - 🚀 开始执行烟叶综合后处理流水线
2025-07-30 13:42:08,940 - INFO - ================================================================================
2025-07-30 13:42:08,940 - INFO - 
🔍 阶段1: 系统初始化
2025-07-30 13:42:08,940 - INFO - --------------------------------------------------
2025-07-30 13:42:08,940 - INFO - 🔍 验证运行环境...
2025-07-30 13:42:08,940 - INFO - Python版本: 3.12.9
2025-07-30 13:42:08,940 - INFO - ✅ 输入目录存在: /home/<USER>/xm/code/coderafactor/test_data/test_inputs/
2025-07-30 13:42:08,940 - INFO - ✅ 输出目录准备完成: /home/<USER>/xm/code/coderafactor/test_data/test_outputs1/
2025-07-30 13:42:08,940 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-6.bmp
2025-07-30 13:42:08,940 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-4.bmp
2025-07-30 13:42:08,940 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-3.bmp
2025-07-30 13:42:08,940 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-5.bmp
2025-07-30 13:42:08,940 - INFO - 📷 发现图像文件: hn-cz-2023-C2F-C21-7.bmp
2025-07-30 13:42:08,940 - INFO - ✅ 找到 5 个图像文件
2025-07-30 13:42:08,940 - INFO - 🔍 验证模块文件存在性...
2025-07-30 13:42:08,940 - INFO - ✅ 模块文件存在: zhumaidakai_select
2025-07-30 13:42:08,940 - INFO - ✅ 模块文件存在: seg_zhimai_split
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: yuandu_halcon
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: dealing_images_juchi
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: import_yanye_user_feature
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: import_yanye_user_feature_rgb_hist
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: user_feature_tobacoo_width
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: user_feature_smoothness
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: correlation_json_generate
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: sum_yanye_user_feature
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: color_variety
2025-07-30 13:42:08,941 - INFO - ✅ 模块文件存在: canque_type_category
2025-07-30 13:42:08,941 - INFO - ✅ 所有模块文件验证通过
2025-07-30 13:42:08,941 - INFO - ✅ 阶段1完成: 系统初始化成功
2025-07-30 13:42:08,941 - INFO - 
🔄 阶段2: 主脉打开选择处理 (串行)
2025-07-30 13:42:08,941 - INFO - --------------------------------------------------
2025-07-30 13:42:08,941 - INFO - 🔄 执行模块: zhumaidakai_select (主脉打开检测和选择)
2025-07-30 13:42:08,949 - INFO - 📋 创建依赖文件: hn-cz-2023-C2F-C21-4.json -> zoushi(1), dakai(1)
2025-07-30 13:42:08,954 - INFO - 📋 创建依赖文件: hn-cz-2023-C2F-C21-5.json -> zoushi(1), dakai(6)
2025-07-30 13:42:08,959 - INFO - 📋 创建依赖文件: hn-cz-2023-C2F-C21-7.json -> zoushi(3), dakai(1)
2025-07-30 13:42:08,966 - INFO - 📋 创建依赖文件: hn-cz-2023-C2F-C21-6.json -> zoushi(3), dakai(3)
2025-07-30 13:42:08,975 - INFO - 📋 创建依赖文件: hn-cz-2023-C2F-C21-3.json -> zoushi(3), dakai(7)
2025-07-30 13:42:09,120 - INFO - ✅ 模块 zhumaidakai_select 执行成功，耗时: 0.18秒
2025-07-30 13:42:09,120 - ERROR - ❌ 未找到主脉打开选择模块生成的JSON文件
2025-07-30 13:42:09,120 - ERROR - 
❌ 烟叶综合后处理系统执行失败！
2025-07-30 13:42:09,120 - ERROR - 📊 耗时: 0.18秒
